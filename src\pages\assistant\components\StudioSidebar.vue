<template>
  <div class="h-full bg-gray-50 border-l border-gray-200 flex flex-col">
    <!-- Header -->
    <div class="p-4 border-b border-gray-200 bg-white">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-medium text-gray-900">Studio</h2>
        <button 
          @click="handleNewNote"
          class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors"
        >
          <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
        </button>
      </div>
      
      <!-- Tabs -->
      <div class="flex space-x-1 bg-gray-100 rounded-lg p-1">
        <button
          v-for="tab in tabs"
          :key="tab.id"
          @click="activeTab = tab.id"
          :class="[
            'flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors',
            activeTab === tab.id
              ? 'bg-white text-gray-900 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          ]"
        >
          {{ tab.label }}
        </button>
      </div>
    </div>
    
    <!-- Content -->
    <div class="flex-1 overflow-hidden">
      <!-- Notes Tab -->
      <div v-if="activeTab === 'notes'" class="h-full flex flex-col">
        <!-- Notes List -->
        <div class="flex-1 overflow-y-auto p-4">
          <!-- Loading State -->
          <div v-if="isLoading" class="space-y-3">
            <div v-for="i in 3" :key="i" class="bg-gray-200 h-20 rounded animate-pulse"></div>
          </div>
          
          <!-- Empty State -->
          <div v-else-if="notes.length === 0" class="text-center py-8">
            <svg class="h-12 w-12 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            <p class="text-gray-500 text-sm">No notes yet</p>
            <p class="text-gray-400 text-xs mt-1">Create your first note to get started</p>
          </div>
          
          <!-- Notes -->
          <div v-else class="space-y-3">
            <div 
              v-for="note in notes" 
              :key="note.id"
              class="bg-white rounded-lg border border-gray-200 p-3 hover:shadow-sm transition-shadow cursor-pointer"
              @click="handleNoteClick(note)"
            >
              <div class="flex items-start justify-between mb-2">
                <h3 class="text-sm font-medium text-gray-900 line-clamp-1">{{ note.title || 'Untitled' }}</h3>
                <button 
                  @click.stop="handleDeleteNote(note)"
                  class="p-1 text-gray-400 hover:text-red-500 transition-colors"
                >
                  <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
              <p class="text-xs text-gray-600 line-clamp-2">{{ note.content || 'No content' }}</p>
              <p class="text-xs text-gray-400 mt-2">{{ formatDate(note.updated_at) }}</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Audio Tab -->
      <div v-else-if="activeTab === 'audio'" class="h-full flex flex-col">
        <div class="flex-1 overflow-y-auto p-4">
          <!-- Audio Upload Area -->
          <div 
            @drop="handleAudioDrop"
            @dragover.prevent
            @dragenter.prevent
            class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors cursor-pointer mb-4"
            @click="triggerAudioInput"
          >
            <input 
              ref="audioInput"
              type="file"
              accept="audio/*"
              @change="handleAudioSelect"
              class="hidden"
            />
            <svg class="h-8 w-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
            </svg>
            <p class="text-sm text-gray-600">Drop audio files here or click to upload</p>
            <p class="text-xs text-gray-500 mt-1">MP3, WAV, M4A files supported</p>
          </div>
          
          <!-- Audio Files -->
          <div v-if="audioFiles.length > 0" class="space-y-3">
            <div 
              v-for="audio in audioFiles" 
              :key="audio.id"
              class="bg-white rounded-lg border border-gray-200 p-3"
            >
              <div class="flex items-center justify-between mb-2">
                <h3 class="text-sm font-medium text-gray-900">{{ audio.title }}</h3>
                <button 
                  @click="handleDeleteAudio(audio)"
                  class="p-1 text-gray-400 hover:text-red-500 transition-colors"
                >
                  <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
              
              <!-- Audio Player -->
              <audio controls class="w-full mb-2">
                <source :src="audio.url" :type="audio.type">
                Your browser does not support the audio element.
              </audio>
              
              <p class="text-xs text-gray-500">{{ formatDate(audio.created_at) }}</p>
            </div>
          </div>
          
          <!-- Empty Audio State -->
          <div v-else class="text-center py-8">
            <svg class="h-12 w-12 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
            </svg>
            <p class="text-gray-500 text-sm">No audio files yet</p>
            <p class="text-gray-400 text-xs mt-1">Upload audio to get started</p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Note Editor Modal -->
    <div v-if="showNoteEditor" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-lg w-full max-w-2xl max-h-[80vh] flex flex-col">
        <div class="p-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <input
              v-model="editingNote.title"
              placeholder="Note title..."
              class="text-lg font-medium text-gray-900 border-none outline-none bg-transparent flex-1"
            />
            <div class="flex items-center space-x-2">
              <button 
                @click="saveNote"
                :disabled="isSaving"
                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {{ isSaving ? 'Saving...' : 'Save' }}
              </button>
              <button 
                @click="closeNoteEditor"
                class="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
        </div>
        <div class="flex-1 p-4">
          <textarea
            v-model="editingNote.content"
            placeholder="Start writing your note..."
            class="w-full h-full resize-none border-none outline-none text-gray-900"
          ></textarea>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StudioSidebar',
  props: {
    notebookId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      activeTab: 'notes',
      tabs: [
        { id: 'notes', label: 'Notes' },
        { id: 'audio', label: 'Audio' }
      ],
      notes: [],
      audioFiles: [],
      isLoading: false,
      showNoteEditor: false,
      editingNote: { id: null, title: '', content: '' },
      isSaving: false
    };
  },
  created() {
    this.loadNotes();
    this.loadAudioFiles();
  },
  methods: {
    async loadNotes() {
      this.isLoading = true;
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Mock notes
        this.notes = [
          {
            id: '1',
            title: 'Research Summary',
            content: 'Key findings from the uploaded documents...',
            updated_at: new Date().toISOString()
          }
        ];
      } catch (error) {
        console.error('Failed to load notes:', error);
      } finally {
        this.isLoading = false;
      }
    },
    
    async loadAudioFiles() {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 300));
        
        // Mock audio files
        this.audioFiles = [];
      } catch (error) {
        console.error('Failed to load audio files:', error);
      }
    },
    
    handleNewNote() {
      this.editingNote = { id: null, title: '', content: '' };
      this.showNoteEditor = true;
    },
    
    handleNoteClick(note) {
      this.editingNote = { ...note };
      this.showNoteEditor = true;
    },
    
    async saveNote() {
      this.isSaving = true;
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        if (this.editingNote.id) {
          // Update existing note
          const index = this.notes.findIndex(n => n.id === this.editingNote.id);
          if (index !== -1) {
            this.notes[index] = { ...this.editingNote, updated_at: new Date().toISOString() };
          }
        } else {
          // Create new note
          const newNote = {
            ...this.editingNote,
            id: Date.now().toString(),
            updated_at: new Date().toISOString()
          };
          this.notes.unshift(newNote);
        }
        
        this.closeNoteEditor();
        this.__showNotif('success', 'Success', 'Note saved successfully');
      } catch (error) {
        console.error('Failed to save note:', error);
        this.__showNotif('error', 'Error', 'Failed to save note');
      } finally {
        this.isSaving = false;
      }
    },
    
    closeNoteEditor() {
      this.showNoteEditor = false;
      this.editingNote = { id: null, title: '', content: '' };
    },
    
    async handleDeleteNote(note) {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500));
        
        this.notes = this.notes.filter(n => n.id !== note.id);
        this.__showNotif('success', 'Success', 'Note deleted successfully');
      } catch (error) {
        console.error('Failed to delete note:', error);
        this.__showNotif('error', 'Error', 'Failed to delete note');
      }
    },
    
    triggerAudioInput() {
      this.$refs.audioInput.click();
    },
    
    handleAudioSelect(event) {
      const files = Array.from(event.target.files);
      this.uploadAudioFiles(files);
    },
    
    handleAudioDrop(event) {
      event.preventDefault();
      const files = Array.from(event.dataTransfer.files);
      this.uploadAudioFiles(files);
    },
    
    async uploadAudioFiles(files) {
      if (!files.length) return;
      
      try {
        for (const file of files) {
          // Simulate upload
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          const newAudio = {
            id: Date.now().toString() + Math.random(),
            title: file.name,
            type: file.type,
            url: URL.createObjectURL(file),
            created_at: new Date().toISOString()
          };
          
          this.audioFiles.unshift(newAudio);
        }
        
        this.__showNotif('success', 'Success', `${files.length} audio file(s) uploaded successfully`);
      } catch (error) {
        console.error('Failed to upload audio files:', error);
        this.__showNotif('error', 'Error', 'Failed to upload audio files');
      }
    },
    
    async handleDeleteAudio(audio) {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500));
        
        this.audioFiles = this.audioFiles.filter(a => a.id !== audio.id);
        this.__showNotif('success', 'Success', 'Audio file deleted successfully');
      } catch (error) {
        console.error('Failed to delete audio file:', error);
        this.__showNotif('error', 'Error', 'Failed to delete audio file');
      }
    },
    
    formatDate(dateString) {
      return new Date(dateString).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      });
    }
  }
};
</script>

<style scoped>
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
