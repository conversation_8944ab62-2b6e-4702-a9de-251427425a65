<template>
	<div class="min-h-screen flex flex-col justify-center overflow-hidden py-4 lg:py-0">
		<!-- <div class="mx-auto">
			<img
				class="mx-auto h-12 mb-5 w-auto"
				src="@/assets/images/svg/bannerbite.svg"
				alt="Bannerbite"
			>
		</div> -->
		<span class="text-center mb-6"> {{ $t('Something yummy coming your way') }} </span>

		<div class="mt-2 sm:mx-auto sm:w-full lg:max-w-md drop-shadow-xl">
			<div class="bg-white py-12 px-8 shadow sm:rounded-xl lg:px-14 md:px-14 relative">
				<form
					class="space-y-3"
					@submit.prevent="submit"
				>
					<div>
						<div class="mb-2">
							{{ $t('Email Address') }}
						</div>
						<div class="mt-1">
							<t-input
								v-model="email"
								:dataTest="'email_register'"
								:type="`email`"
								:value="email"
								class="w-full"
							/>
							<span
								v-if="!isValidEmailAddress && email && email.length !== 0"
								class="text-red-500 text-xs"
							>{{ $t('Invalid Email Address') }}</span>
						</div>
					</div>
					<div>
						<div class="flex justify-between">
							<div class="mb-2">
								{{ $t('Password') }}
							</div>
							<!-- <div
								class="text-sm"
								:class="{'very-weak': warningError === 'Too Weak',
									'weak': warningError === 'Weak',
									'strong': warningError === 'Strong' || warningError === 'Medium',
								}"
							>
								{{ warningError }}
							</div> -->
							<div
								class="text-[10px] mt-1"
							>
								{{ $t('Min 8 chars, Max 20 chars, numeric and special char') }}
							</div>
						</div>
						<div>
							<t-input
								v-model="password"
								:dataTest="'password_register'"
								:type="`password`"
								:value="password"
								:maxlength="20"
								class="w-full"
							/>
						</div>
						<span
							v-if="warningError === 'Weak' && password"
							class="text-red-500 text-xs"
						>{{ $t('Please enter a password of at least 8 characters, and use a combination of uppercase and lowercase letters, and numbers') }}</span>
					</div>
					<div class="min-h-[100px]">
						<div class="mb-2">
							{{ $t('Confirm Password') }}
						</div>
						<div class="mt-1">
							<t-input
								v-model="confirm_password"
								:dataTest="'confirm_password'"
								:type="`password`"
								:maxlength="20"
								:value="confirm_password"
								class="w-full"
							/>
						</div>
						<div
							v-if="confirm_password && confirm_password !== password"
							class="text-red-500 text-xs"
						>
							{{ $t('Password do not match') }}
						</div>
					</div>

					<div>
						<t-button
							:type="'submit'"
							:color="`primary-solid`"
							class="w-full h-[50px]"
							:isLoading="isSendingEmail"
							:isDisabled="!isFormValid || isSendingEmail"
						>
							{{ $t('Signup') }}
						</t-button>
					</div>
				</form>
				<div class="mt-8">
					<div class="relative">
						<div class="absolute inset-0 flex items-center">
							<div class="w-full border-t border-gray-300" />
						</div>
						<div class="relative flex justify-center text-sm">
							<span class="px-2 bg-white text-gray-500">
								{{ $t('Or Signup with') }}
							</span>
						</div>
					</div>

					<div class="mt-8">
						<div
							v-if="!isSignIn"
							@click="loginWithGoogle"
						>
							<a
								href="#"
								class="flex items-center h-[50px] drop-shadow-sm w-full justify-left bg-[#EEEEEE] border border-gray-300 rounded-md shadow-sm bg-[#fff] text-sm font-medium hover:bg-gray-50"
							>
								<svg
									class="w-10 h-5 ml-[6px]"
									aria-hidden="true"
									fill="#4f96ff"
									viewBox="0 0 20 25"
								>
									<path d="M12.545,10.239v3.821h5.445c-0.712,2.315-2.647,3.972-5.445,3.972c-3.332,0-6.033-2.701-6.033-6.032 s2.701-6.032,6.033-6.032c1.498,0,2.866,0.549,3.921,1.453l2.814-2.814C17.503,2.988,15.139,2,12.545,2 C7.021,2,2.543,6.477,2.543,12s4.478,10,10.002,10c8.396,0,10.249-7.85,9.426-11.748L12.545,10.239z" />
								</svg>
								<div class="justify-center absolute flex right-0 left-0 mx-auto font-medium">{{ $t('Signup With Google') }}</div>
							</a>
						</div>
					</div>

					<div class="my-4">
						<div
							v-if="!isSignIn"
							@click="loginTwitter"
						>
							<a
								href="#"
								class="flex items-center h-[50px] drop-shadow-sm w-full justify-left bg-[#EEEEEE] border border-gray-300 rounded-md shadow-sm bg-[#fff] text-sm font-medium hover:bg-gray-50"
							>
								<svg
									style="width:24px;height:24px"
									viewBox="0 0 24 24"
									class="ml-4"
								>
									<path
										fill="#0bc8e6"
										d="M22.46,6C21.69,6.35 20.86,6.58 20,6.69C20.88,6.16 21.56,5.32 21.88,4.31C21.05,4.81 20.13,5.16 19.16,5.36C18.37,4.5 17.26,4 16,4C13.65,4 11.73,5.92 11.73,8.29C11.73,8.63 11.77,8.96 11.84,9.27C8.28,9.09 5.11,7.38 3,4.79C2.63,5.42 2.42,6.16 2.42,6.94C2.42,8.43 3.17,9.75 4.33,10.5C3.62,10.5 2.96,10.3 2.38,10C2.38,10 2.38,10 2.38,10.03C2.38,12.11 3.86,13.85 5.82,14.24C5.46,14.34 5.08,14.39 4.69,14.39C4.42,14.39 4.15,14.36 3.89,14.31C4.43,16 6,17.26 7.89,17.29C6.43,18.45 4.58,19.13 2.56,19.13C2.22,19.13 1.88,19.11 1.54,19.07C3.44,20.29 5.7,21 8.12,21C16,21 20.33,14.46 20.33,8.79C20.33,8.6 20.33,8.42 20.32,8.23C21.16,7.63 21.88,6.87 22.46,6Z"
									/>
								</svg>
								<div class="justify-center absolute flex right-0 left-0 mx-auto font-medium">{{ $t('Signup With Twitter') }}</div>
							</a>
						</div>
					</div>
				</div>
				<!-- privacy policy -->
				<div class="text-gray-600 text-xs text-center mt-4 absolute ml-[-8px] bottom-0 mb-[30px]">
					{{ $t('By sigining up, you agree to our') }}
					<a
						href="https://bannerbite.com/terms-conditions.html"
						target="_blank"
						class="underline"
					>{{ $t('terms of service') }}</a> {{ $t('and') }} <a
						href="https://bannerbite.com/privacy-policy.html"
						target="_blank"
						class="underline"
					>{{ $t('privacy policy') }}</a>
				</div>
			</div>
			<div class="text-sm text-center mt-6">
				<router-link
					to="/login"
					class="text-blue"
				>
					{{ $t('Back to Login') }}
				</router-link>
			</div>
		</div>
	</div>
	<div class="mt-2 md:mr-12 md:fixed md:bottom-0 flex md:right-0 mb-4 justify-center text-center">
		<Menu
			as="div"
			class="relative z-10 mr-4"
		>
			<div>
				<MenuButton
					class="max-w-xs flex items-center focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
				>
					<span class="sr-only">Open Lang Menu</span>
					<div
						v-if="$i18n.locale === 'id'"
					>
						Bahasa
					</div>
					<div
						v-if="$i18n.locale === 'no'"
					>
						Norsk
					</div>
					<div
						v-if="$i18n.locale === 'en'"
					>
						English
					</div>
				</MenuButton>
			</div>
			<transition
				enterActiveClass="transition ease-out duration-100"
				enterFromClass="transform opacity-0 scale-95"
				enterToClass="transform opacity-100 scale-100"
				leaveActiveClass="transition ease-in duration-75"
				leaveFromClass="transform opacity-100 scale-100"
				leaveToClass="transform opacity-0 scale-95"
			>
				<MenuItems
					class="origin-top-right absolute right-0 mt-2 w-48 mt-[-9em] rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none"
				>
					<MenuItem
						v-for="item in langMenus"
						v-slot="{ active }"
						:key="item.name"
						@click="changeLocale(item)"
					>
						<div
							class="flex justify-between items-center px-2"
							:class="[active ? 'bg-gray-100' : '', 'block text-sm text-gray-700']"
						>
							<div>
								{{ item.name }}
							</div>
							<img
								v-if="item.name === 'English'"
								class="h-7 w-7 rounded-full my-1"
								src="../../assets/images/lang/icons8-english-48.png"
								alt="country-image"
							>
							<img
								v-if="item.name === 'Bahasa'"
								class="h-7 w-7 rounded-full my-1"
								src="../../assets/images/lang/icons8-indonesia-48.png"
								alt="country-image"
							>
							<img
								v-if="item.name === 'Norsk'"
								class="h-7 w-7 rounded-full my-1"
								src="../../assets/images/lang/icons8-norway-48.png"
								alt="country-image"
							>
						</div>
					</MenuItem>
				</MenuItems>
			</transition>
		</Menu>
		<a
			href="https://bannerbite.com/terms-conditions.html"
			target="_blank"
			class="mr-2"
		>{{ $t('Terms & Condition') }}</a>
		<a
			href="https://bannerbite.com/privacy-policy.html"
			target="_blank"
			class="ml-2"
		>{{ $t('Privacy Policy') }}</a>
	</div>
</template>

<script>

import authApi from '@/api/auth';
import TButton from '@/components/global/Button.vue';
import TInput from '@/components/form/Input.vue';
import { isValidEmail, checkPassword } from '@/libraries/helper';
import { googleTokenLogin } from "vue3-google-login";

import {
	Menu,
	MenuButton,
	MenuItem,
	MenuItems,
} from '@headlessui/vue';

export default {
	
	components: {
		TInput,
		TButton,
		Menu,
		MenuButton,
		MenuItem,
		MenuItems,
	},
	setup() {
		return {
		};
	},
	data() {
		return {
			email: null,
			isSendingEmail: false,
			name: null,
			password: null,
			confirm_password: null,
			warningError: '',
			isSignIn: false,
			isInit: false,
			langMenus: [
				{name: 'English', key: 'en'},
				{name: 'Norsk', key: 'no'},
				{name: 'Bahasa', key: 'id'},
			],
		};
	},
	computed: {
		isValidEmailAddress() {
			return isValidEmail(this.email);
		},
		isFormValid() {
			return (
				this.isValidEmailAddress
				&& this.email
				&& this.password === this.confirm_password
				&& this.warningError !== 'Weak' 
				&& this.warningError !== 'Too Weak'
			);
		},
	},
	watch: {
		password() {
			if (this.password) {
				const result = checkPassword(this.password);
				if (result === 0) this.warningError = 'Too Weak';
				if (result === 1) this.warningError = 'Weak';
				if (result === 2) this.warningError = 'Medium';
				if (result === 3) this.warningError = 'Strong';
			}
			if (!this.password) this.warningError = '';
		},
	},
	mounted() {
		
	},
	methods: {
		changeLocale(locale) {
			localStorage.setItem(`locale`,  locale.key);
			this.$i18n.locale = locale.key;
		},
		submit() {
			this.isSendingEmail = true;
			const name = this.email.split('@');
			const params = {
				name: name[0],
				email: this.email,
				password: this.password,
				confirm_password: this.confirm_password,
			};
			const callback = (response) => {
				
				const message = response.message;
				this.isSendingEmail = false;
				this.name = null;
				this.email = null;
				this.password = null;
				this.confirm_password = null;

				this.$router.push('/login?register=true');
			};
			const errorCallback = (error) => {
				let message = error.response.data.message;
				if (!message) {
					message = error.response && error.response.data.errors && error.response.data.errors[0] && error.response.data.errors[0].message ? error.response.data.errors[0].message : error.response.data.errors;
				}
				this.__showNotif('error', 'Error', message);
				this.isSendingEmail = false;
			};
			authApi.register(params, callback, errorCallback);
		},
		async loginWithGoogle() {
			this.isSubmitting = true;
			this.isSubmitting = true;
			try {
				googleTokenLogin().then((Gauth) => {
					const params = {
						token_id: Gauth.access_token,
						type: 'google',
					};
					const callback = (response) => {
						const data = response.data;
						this.$store.dispatch('auth/clearAuth');
						this.$store.dispatch('auth/setSession', data);
						window.location.href = '/';
						localStorage.removeItem('renderList');
						this.isSubmitting = false;
					};
					const errorCallback = (err) => {
						const message = err?.response?.data?.message;
						this.__showNotif('error', 'Error', message);
						this.isSubmitting = false;
					};
					authApi.loginSocialMedia(params, callback, errorCallback);
				});
			} catch (error) {
				//on fail do something
				console.error(error);
				this.isSubmitting = false;
				return null;
			}
    
		},
		loginTwitter() {
			this.$store.dispatch('auth/clearAuth');
			window.location.replace(`${import.meta.env.VITE_API_URL}/twitter/redirect`);
		},
	}
};
</script>