<template>
  <!-- If we have a selected citation, show the content viewer in full screen -->
  <div v-if="selectedCitation" class="h-full bg-white flex flex-col overflow-hidden">
    <!-- Header with close button -->
    <div class="p-4 border-b border-gray-200 flex-shrink-0">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-medium text-gray-900">{{ selectedCitation.source_title || 'Source Document' }}</h3>
        <button
          @click="handleCitationClose"
          class="p-1 text-gray-400 hover:text-gray-600 transition-colors"
        >
          <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Source Content Viewer in full screen -->
    <SourceContentViewer
      :citation="selectedCitation"
      :source-content="currentSourceContent"
      :source-summary="currentSourceSummary"
      :source-scenario="currentSourceScenario"
      :source-description="currentSourceDescription"
      :is-opened-from-source-list="false"
      class="flex-1 overflow-hidden"
    />
  </div>

  <!-- Default sources list view -->
  <div v-else class="h-full bg-gray-50 border-r border-gray-200 flex flex-col">
    <!-- Header -->
    <div class="p-4 border-b border-gray-200 bg-white flex-shrink-0">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-medium text-gray-900">Sources</h2>
      </div>

      <div class="flex space-x-2">
        <t-button
          variant="outline"
          size="sm"
          class="flex-1"
          @click="handleAddSource"
        >
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
          Add
        </t-button>
      </div>
    </div>

    <!-- Sources List -->
    <div class="flex-1 overflow-hidden">
      <div class="h-full overflow-y-auto p-4">
        <div v-if="isLoadingSources" class="flex items-center justify-center h-32">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>

        <div v-else-if="sources.length === 0" class="text-center py-8">
          <div class="w-16 h-16 bg-gray-200 rounded-lg mx-auto mb-4 flex items-center justify-center">
            <span class="text-gray-400 text-2xl">📄</span>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">Saved sources will appear here</h3>
          <p class="text-sm text-gray-600 mb-4">Click Add source above to add PDFs, text, or audio files.</p>
        </div>

        <div v-else class="space-y-3">
          <div
            v-for="source in sources"
            :key="source.id"
            @click="handleSourceClick(source)"
            class="p-3 bg-white rounded-lg border border-gray-200 hover:border-gray-300 cursor-pointer transition-colors"
          >
            <div class="flex items-start space-x-3">
              <!-- File Type Icon -->
              <div class="flex-shrink-0 mt-1">
                <div class="w-6 h-6 bg-white rounded border border-gray-200 flex items-center justify-center flex-shrink-0 overflow-hidden">
                  <svg v-if="source.type === 'pdf'" class="h-4 w-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd" />
                  </svg>
                  <svg v-else class="h-4 w-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd" />
                  </svg>
                </div>
              </div>

              <!-- Source Info -->
              <div class="flex-1 min-w-0">
                <h4 class="text-sm font-medium text-gray-900 truncate">{{ source.title }}</h4>
                <p class="text-xs text-gray-500 mt-1">{{ source.type.toUpperCase() }}</p>
                <p class="text-xs text-gray-400 mt-1">{{ formatDate(source.created_at) }}</p>
              </div>

              <!-- Actions -->
              <div class="flex-shrink-0">
                <button
                  @click.stop="handleDeleteSource(source)"
                  class="p-1 text-gray-400 hover:text-red-600 transition-colors"
                >
                  <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SourceContentViewer from './SourceContentViewer.vue';

export default {
  name: 'SourcesSidebar',
  components: {
    SourceContentViewer
  },
  props: {
    hasSource: {
      type: Boolean,
      default: false
    },
    notebookId: {
      type: String,
      required: true
    },
    selectedCitation: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      sources: [
        {
          id: 'source-1',
          title: 'RAG: Skriftlig Svarinn, Verkstedshagen 7.pdf',
          type: 'pdf',
          created_at: '2024-01-15T10:30:00Z',
          processing_status: 'completed'
        },
        {
          id: 'source-2',
          title: 'Scenario: Service på dårpunkter og automatikk i verkstedshagen',
          type: 'text',
          created_at: '2024-01-14T15:45:00Z',
          processing_status: 'completed'
        }
      ],
      selectedSource: null,
      isLoadingSources: false
    };
  },
  computed: {
    currentSourceContent() {
      if (!this.selectedCitation) return '';

      // Return dummy content that matches insights-lm-public structure
      return `Fredrikstad Syd ved Lånekassen Trafik er ansvarlig for koordinering og servicetilbud på dårpunkter.

21. § kan styret kan kontakte Fredrikstad direkte ved behov for:

• Koordinering av servicetilbud på dårpunkter
• Feil eller behov for manuell oppfølging på dårpunktsarbeid

22. § kontaktinformasjon:

• Tlf 09 386

"Ansvarlig interesse":

• Håvard Sandverk

"Hei" - meldis skal ikke kontaktes av følgende: All kontakt skal ikke gjennomføres styret.

Scenario: Feil på plattformen eller gjennomføring.

"Beskrivelse":

Lene Dalsrud ved Lånekassen Trafik er ansvarlig og kontaktperson for styret ved behov av service på plattformen og gjennomføring.

Styret ved behov av service på plattformen og gjennomføring.

"Tiltak":

25. § ved feil på inngangsporter eller dører i garasjen.`;
    },

    currentSourceSummary() {
      if (!this.selectedCitation) return '';

      return `Dette dokumentet beskriver ansvar og kontaktinformasjon for servicetilbud på dårpunkter i Fredrikstad Syd ved Lånekassen Trafik. Det inkluderer kontaktpersoner, prosedyrer for feilhåndtering og ansvarlige for koordinering av servicetilbud.`;
    },

    currentSourceScenario() {
      if (!this.selectedCitation) return '';

      return `Service på dårpunkter og automatikk i verkstedshagen`;
    },

    currentSourceDescription() {
      if (!this.selectedCitation) return '';

      return `Verksted og automatikk i verkstedshagen som styrer koordinering og servicetilbud på dårpunkter.`;
    }
  },
  methods: {
    handleAddSource() {
      // Placeholder for add source functionality
      console.log('Add source clicked');
    },

    handleSourceClick(source) {
      console.log('Source clicked:', source);
      this.selectedSource = source;
    },

    handleDeleteSource(source) {
      console.log('Delete source:', source);
      // Remove source from list
      this.sources = this.sources.filter(s => s.id !== source.id);
    },

    handleCitationClose() {
      console.log('SourcesSidebar: Citation close clicked');
      this.$emit('citation-close');
    },

    formatDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    }
  }
};
</script>