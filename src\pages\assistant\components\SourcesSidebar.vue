<template>
  <div class="h-full bg-gray-50 border-r border-gray-200 flex flex-col">
    <!-- Header -->
    <div class="p-4 border-b border-gray-200 bg-white">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-medium text-gray-900">Sources</h2>
        <button 
          @click="handleAddSource"
          class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors"
        >
          <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
        </button>
      </div>
      
      <!-- Upload Area -->
      <div
        @drop="handleDrop"
        @dragover.prevent
        @dragenter.prevent
        class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 transition-colors cursor-pointer mb-4"
        @click="triggerFileInput"
      >
        <input
          ref="fileInput"
          type="file"
          multiple
          accept=".pdf,.doc,.docx,.txt"
          @change="handleFileSelect"
          class="hidden"
        />
        <svg class="h-8 w-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
        </svg>
        <p class="text-sm text-gray-600">
          Drop files here or click to upload
        </p>
        <p class="text-xs text-gray-500 mt-1">
          PDF, DOC, TXT files supported
        </p>
      </div>

      <!-- URL Input -->
      <UrlInput :notebookId="notebookId" @source-added="handleSourceAdded" />
    </div>
    
    <!-- Sources List -->
    <div class="flex-1 overflow-y-auto">
      <!-- Loading State -->
      <div v-if="isLoading" class="p-4">
        <div class="animate-pulse space-y-3">
          <div v-for="i in 3" :key="i" class="bg-gray-200 h-16 rounded"></div>
        </div>
      </div>
      
      <!-- Empty State -->
      <div v-else-if="!hasSource" class="p-4 text-center">
        <svg class="h-12 w-12 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <p class="text-gray-500 text-sm">No sources added yet</p>
        <p class="text-gray-400 text-xs mt-1">Upload documents to get started</p>
      </div>
      
      <!-- Sources -->
      <div v-else class="p-4 space-y-4">
        <div
          v-for="source in sources"
          :key="source.id"
          class="bg-white rounded-lg border border-gray-200 p-3 hover:bg-gray-50 transition-colors cursor-pointer group"
          @click="handleSourceClick(source)"
        >
          <div class="flex items-start justify-between space-x-3">
            <div class="flex items-center space-x-2 flex-1 min-w-0">
              <div class="w-6 h-6 bg-white rounded border border-gray-200 flex items-center justify-center flex-shrink-0 overflow-hidden">
                <component :is="getSourceIcon(source.type)" class="w-full h-full object-contain" />
              </div>
              <div class="flex-1 min-w-0">
                <h3 class="text-sm font-medium text-gray-900 truncate">{{ source.title }}</h3>
                <div class="flex items-center space-x-2 mt-1">
                  <span class="text-xs text-gray-500">{{ formatDate(source.created_at) }}</span>
                  <span v-if="source.processing_status" class="text-xs px-2 py-0.5 rounded-full" :class="getStatusClass(source.processing_status)">
                    {{ getStatusText(source.processing_status) }}
                  </span>
                </div>
              </div>
            </div>

            <!-- Context Menu Button -->
            <div class="relative">
              <button
                @click.stop="toggleContextMenu(source.id)"
                class="p-1 text-gray-400 hover:text-gray-600 transition-colors opacity-0 group-hover:opacity-100"
              >
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                </svg>
              </button>

              <!-- Context Menu -->
              <div
                v-if="contextMenuOpen === source.id"
                class="absolute right-0 top-8 w-48 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-10"
                @click.stop
              >
                <button
                  @click="handleDeleteSource(source)"
                  class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
                >
                  <svg class="h-4 w-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                  Delete source
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Source Content Viewer -->
    <div v-if="selectedCitation || selectedSource" class="flex-1 border-t border-gray-200 overflow-hidden">
      <SourceContentViewer
        :citation="selectedCitation"
        :source-content="currentSourceContent"
        :source-summary="currentSourceSummary"
        :source-scenario="currentSourceScenario"
        :source-description="currentSourceDescription"
        :is-opened-from-source-list="!!selectedSource"
        @close="handleViewerClose"
      />
    </div>
  </div>
</template>

<script>
import UrlInput from './UrlInput.vue';
import SourceContentViewer from './SourceContentViewer.vue';

export default {
  name: 'SourcesSidebar',
  components: {
    UrlInput,
    SourceContentViewer,
    // Icon components
    PdfIcon: {
      template: `<svg class="h-4 w-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd" />
      </svg>`
    },
    TextIcon: {
      template: `<svg class="h-4 w-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd" />
      </svg>`
    },
    WebIcon: {
      template: `<svg class="h-4 w-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 919-9" />
      </svg>`
    },
    DocIcon: {
      template: `<svg class="h-4 w-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd" />
      </svg>`
    },
    AudioIcon: {
      template: `<svg class="h-4 w-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
      </svg>`
    }
  },
  props: {
    hasSource: {
      type: Boolean,
      default: false
    },
    notebookId: {
      type: String,
      required: true
    },
    selectedCitation: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      sources: [],
      isLoading: false,
      isUploading: false,
      selectedSource: null,
      currentSourceContent: '',
      currentSourceSummary: '',
      currentSourceScenario: '',
      currentSourceDescription: '',
      contextMenuOpen: null
    };
  },
  created() {
    this.loadSources();
  },
  methods: {
    async loadSources() {
      this.isLoading = true;
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Mock sources
        this.sources = [
          {
            id: '1',
            title: 'RAG Tekniske Rutiner og Kontaktoveriskt for Verkstedshagen',
            type: 'pdf',
            created_at: new Date().toISOString(),
            processing_status: 'completed'
          },
          {
            id: '2',
            title: 'Meeting Notes.docx',
            type: 'docx',
            created_at: new Date(Date.now() - 86400000).toISOString(),
            processing_status: 'completed'
          },
          {
            id: '3',
            title: 'Project Guidelines.txt',
            type: 'text',
            created_at: new Date(Date.now() - 172800000).toISOString(),
            processing_status: 'processing'
          }
        ];
      } catch (error) {
        console.error('Failed to load sources:', error);
      } finally {
        this.isLoading = false;
      }
    },
    
    handleAddSource() {
      this.triggerFileInput();
    },
    
    triggerFileInput() {
      this.$refs.fileInput.click();
    },
    
    handleFileSelect(event) {
      const files = Array.from(event.target.files);
      this.uploadFiles(files);
    },
    
    handleDrop(event) {
      event.preventDefault();
      const files = Array.from(event.dataTransfer.files);
      this.uploadFiles(files);
    },
    
    async uploadFiles(files) {
      if (!files.length) return;
      
      this.isUploading = true;
      try {
        for (const file of files) {
          // Simulate upload
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          const newSource = {
            id: Date.now().toString() + Math.random(),
            title: file.name,
            type: file.type,
            created_at: new Date().toISOString()
          };
          
          this.sources.unshift(newSource);
        }
        
        this.__showNotif('success', 'Success', `${files.length} file(s) uploaded successfully`);
      } catch (error) {
        console.error('Failed to upload files:', error);
        this.__showNotif('error', 'Error', 'Failed to upload files');
      } finally {
        this.isUploading = false;
      }
    },
    
    async handleSourceClick(source) {
      this.selectedSource = source;

      // Load source content and metadata
      await this.loadSourceContent(source);

      // Emit source click event
      this.$emit('source-click', source);
    },

    async loadSourceContent(source) {
      try {
        // Simulate API call to get source content
        await new Promise(resolve => setTimeout(resolve, 500));

        // Mock content based on source type
        this.currentSourceContent = this.generateMockContent(source);
        this.currentSourceSummary = `This document contains important information about ${source.title}. It includes detailed analysis, findings, and recommendations that are relevant to your research.`;
        this.currentSourceScenario = `Scenario: This document was created as part of a comprehensive study to analyze key aspects of the subject matter.`;
        this.currentSourceDescription = `Description: A detailed document providing insights and analysis on the topic.`;

      } catch (error) {
        console.error('Failed to load source content:', error);
        this.__showNotif('error', 'Error', 'Failed to load source content');
      }
    },

    generateMockContent(source) {
      const lines = [
        `Document Title: ${source.title}`,
        '',
        'Executive Summary',
        'This document provides a comprehensive overview of the subject matter.',
        'The analysis includes multiple perspectives and data-driven insights.',
        '',
        'Key Findings',
        '1. Primary finding based on extensive research',
        '2. Secondary observation with supporting evidence',
        '3. Additional insights derived from data analysis',
        '',
        'Methodology',
        'The research methodology employed a mixed-methods approach.',
        'Data collection involved multiple sources and validation techniques.',
        'Analysis was conducted using industry-standard frameworks.',
        '',
        'Detailed Analysis',
        'The comprehensive analysis reveals several important patterns.',
        'These patterns indicate significant trends in the data.',
        'Further investigation is recommended to validate these findings.',
        '',
        'Recommendations',
        '1. Implement the proposed framework for better outcomes',
        '2. Continue monitoring key performance indicators',
        '3. Establish regular review cycles for ongoing assessment',
        '',
        'Conclusion',
        'The findings support the initial hypothesis and provide',
        'a solid foundation for future decision-making processes.',
        'Regular updates and reviews will ensure continued relevance.'
      ];

      return lines.join('\n');
    },

    handleViewerClose() {
      this.selectedSource = null;
      this.selectedCitation = null;
      this.$emit('citation-close');
    },

    handleSourceAdded(source) {
      this.sources.unshift(source);
    },
    
    async handleDeleteSource(source) {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500));
        
        this.sources = this.sources.filter(s => s.id !== source.id);
        this.__showNotif('success', 'Success', 'Source deleted successfully');
      } catch (error) {
        console.error('Failed to delete source:', error);
        this.__showNotif('error', 'Error', 'Failed to delete source');
      }
    },
    
    formatDate(dateString) {
      return new Date(dateString).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      });
    },

    getSourceIcon(type) {
      // Return a Vue component name or template for the icon
      const iconMap = {
        'pdf': 'PdfIcon',
        'text': 'TextIcon',
        'website': 'WebIcon',
        'youtube': 'AudioIcon',
        'audio': 'AudioIcon',
        'doc': 'DocIcon',
        'docx': 'DocIcon',
        'multiple-websites': 'WebIcon',
        'copied-text': 'TextIcon'
      };

      return iconMap[type] || 'TextIcon';
    },

    getStatusClass(status) {
      const statusMap = {
        'processing': 'bg-yellow-100 text-yellow-800',
        'completed': 'bg-green-100 text-green-800',
        'failed': 'bg-red-100 text-red-800',
        'pending': 'bg-gray-100 text-gray-800'
      };

      return statusMap[status] || 'bg-gray-100 text-gray-800';
    },

    getStatusText(status) {
      const statusMap = {
        'processing': 'Processing',
        'completed': 'Ready',
        'failed': 'Failed',
        'pending': 'Pending'
      };

      return statusMap[status] || 'Unknown';
    },

    toggleContextMenu(sourceId) {
      this.contextMenuOpen = this.contextMenuOpen === sourceId ? null : sourceId;
    }
  },

  created() {
    this.loadSources();
    // Close context menu when clicking outside
    document.addEventListener('click', () => {
      this.contextMenuOpen = null;
    });
  }
  }
};
</script>
