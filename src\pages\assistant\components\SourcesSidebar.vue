<template>
  <div class="h-full bg-gray-50 border-r border-gray-200 flex flex-col">
    <!-- Header -->
    <div class="p-4 border-b border-gray-200 bg-white">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-medium text-gray-900">Sources</h2>
        <button 
          @click="handleAddSource"
          class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors"
        >
          <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
        </button>
      </div>
      
      <!-- Upload Area -->
      <div
        @drop="handleDrop"
        @dragover.prevent
        @dragenter.prevent
        class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 transition-colors cursor-pointer mb-4"
        @click="triggerFileInput"
      >
        <input
          ref="fileInput"
          type="file"
          multiple
          accept=".pdf,.doc,.docx,.txt"
          @change="handleFileSelect"
          class="hidden"
        />
        <svg class="h-8 w-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
        </svg>
        <p class="text-sm text-gray-600">
          Drop files here or click to upload
        </p>
        <p class="text-xs text-gray-500 mt-1">
          PDF, DOC, TXT files supported
        </p>
      </div>

      <!-- URL Input -->
      <UrlInput :notebookId="notebookId" @source-added="handleSourceAdded" />
    </div>
    
    <!-- Sources List -->
    <div class="flex-1 overflow-y-auto">
      <!-- Loading State -->
      <div v-if="isLoading" class="p-4">
        <div class="animate-pulse space-y-3">
          <div v-for="i in 3" :key="i" class="bg-gray-200 h-16 rounded"></div>
        </div>
      </div>
      
      <!-- Empty State -->
      <div v-else-if="!hasSource" class="p-4 text-center">
        <svg class="h-12 w-12 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <p class="text-gray-500 text-sm">No sources added yet</p>
        <p class="text-gray-400 text-xs mt-1">Upload documents to get started</p>
      </div>
      
      <!-- Sources -->
      <div v-else class="p-4 space-y-3">
        <div 
          v-for="source in sources" 
          :key="source.id"
          class="bg-white rounded-lg border border-gray-200 p-3 hover:shadow-sm transition-shadow cursor-pointer"
          @click="handleSourceClick(source)"
        >
          <div class="flex items-start justify-between">
            <div class="flex-1 min-w-0">
              <div class="flex items-center space-x-2 mb-1">
                <div class="w-6 h-6 bg-red-100 rounded flex items-center justify-center flex-shrink-0">
                  <svg class="h-3 w-3 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd" />
                  </svg>
                </div>
                <h3 class="text-sm font-medium text-gray-900 truncate">{{ source.title }}</h3>
              </div>
              <p class="text-xs text-gray-500">
                {{ formatDate(source.created_at) }}
              </p>
            </div>
            
            <button 
              @click.stop="handleDeleteSource(source)"
              class="p-1 text-gray-400 hover:text-red-500 transition-colors"
            >
              <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Citation Document Viewer -->
    <div v-if="selectedCitation" class="border-t border-gray-200 bg-white">
      <div class="p-4">
        <div class="flex items-center justify-between mb-3">
          <h3 class="text-sm font-medium text-gray-900">{{ selectedCitation.title }}</h3>
          <button 
            @click="$emit('citation-close')"
            class="p-1 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <div class="bg-gray-50 rounded p-3 text-sm text-gray-700 max-h-40 overflow-y-auto">
          {{ selectedCitation.content }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import UrlInput from './UrlInput.vue';

export default {
  name: 'SourcesSidebar',
  components: {
    UrlInput,
  },
  props: {
    hasSource: {
      type: Boolean,
      default: false
    },
    notebookId: {
      type: String,
      required: true
    },
    selectedCitation: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      sources: [],
      isLoading: false,
      isUploading: false
    };
  },
  created() {
    this.loadSources();
  },
  methods: {
    async loadSources() {
      this.isLoading = true;
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Mock sources
        this.sources = [
          {
            id: '1',
            title: 'Research Document.pdf',
            type: 'pdf',
            created_at: new Date().toISOString()
          },
          {
            id: '2',
            title: 'Meeting Notes.docx',
            type: 'docx',
            created_at: new Date(Date.now() - 86400000).toISOString()
          }
        ];
      } catch (error) {
        console.error('Failed to load sources:', error);
      } finally {
        this.isLoading = false;
      }
    },
    
    handleAddSource() {
      this.triggerFileInput();
    },
    
    triggerFileInput() {
      this.$refs.fileInput.click();
    },
    
    handleFileSelect(event) {
      const files = Array.from(event.target.files);
      this.uploadFiles(files);
    },
    
    handleDrop(event) {
      event.preventDefault();
      const files = Array.from(event.dataTransfer.files);
      this.uploadFiles(files);
    },
    
    async uploadFiles(files) {
      if (!files.length) return;
      
      this.isUploading = true;
      try {
        for (const file of files) {
          // Simulate upload
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          const newSource = {
            id: Date.now().toString() + Math.random(),
            title: file.name,
            type: file.type,
            created_at: new Date().toISOString()
          };
          
          this.sources.unshift(newSource);
        }
        
        this.__showNotif('success', 'Success', `${files.length} file(s) uploaded successfully`);
      } catch (error) {
        console.error('Failed to upload files:', error);
        this.__showNotif('error', 'Error', 'Failed to upload files');
      } finally {
        this.isUploading = false;
      }
    },
    
    handleSourceClick(source) {
      // Emit source click event
      this.$emit('source-click', source);
    },

    handleSourceAdded(source) {
      this.sources.unshift(source);
    },
    
    async handleDeleteSource(source) {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500));
        
        this.sources = this.sources.filter(s => s.id !== source.id);
        this.__showNotif('success', 'Success', 'Source deleted successfully');
      } catch (error) {
        console.error('Failed to delete source:', error);
        this.__showNotif('error', 'Error', 'Failed to delete source');
      }
    },
    
    formatDate(dateString) {
      return new Date(dateString).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      });
    }
  }
};
</script>
