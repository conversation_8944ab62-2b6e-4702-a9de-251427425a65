<template>
  <div class="min-h-screen bg-gray-50 flex flex-col justify-around sm:justify-center py-6 px-[40px] sm:py-12 px-4 sm:px-6 lg:px-8">
    <!-- Logo -->
    <div class="sm:max-w-sm sm:mx-auto flex justify-start mb-8 sm:mb-16">
      <img class="h-8 mb-10 sm:h-16 w-auto" src="@/assets/images/logo_desidia.png" alt="desidia">
    </div>

    <!-- Main Content -->
    <div class="min-h-[384px] sm:min-h-[508px] flex flex-col justify-end sm:justify-center max-w-md mx-auto w-full">
      <div class="sm:bg-white rounded-md  sm:p-12">
        <!-- Title -->
        <h1 class="text-2xl sm:text-3xl font-bold text-gray-900 text-left mb-2 sm:mb-12">
          Sign In
        </h1>

        <!-- Form -->
        <form class="space-y-6" @submit.prevent="loginSubmit">
          <!-- Email Field -->
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
              Email
            </label>
            <t-input
              v-model="email"
              :dataTest="'email'"
              :type="'email'"
              :value="email"
              :placeholder="'<EMAIL>'"
              class="w-full"
            />
            <span v-if="!isValidEmailAddress && email && email.length !== 0" class="text-red-500 text-sm mt-1 block">
              Invalid Email Address
            </span>
          </div>

          <!-- Password Field -->
          <div>
            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
              Password
            </label>
            <t-input
              v-model="password"
              :dataTest="'password'"
              :type="'password'"
              :value="password"
              :placeholder="'••••••'"
              class="w-full"
            />
          </div>

          <!-- Sign In Button -->
          <t-button
            :type="'submit'"
            :color="'primary-solid'"
            class="w-full mt-6"
            :isLoading="isSubmitting"
            :isDisabled="isSubmitting || !isFormValid"
          >
            Sign in
          </t-button>

          <!-- Bottom Links -->
          <div class="flex justify-between items-center text-sm mt-6">
            <router-link
              to="/magic-link"
              class="text-gray-600 hover:text-gray-800 transition-colors duration-200"
            >
              Login with magic link
            </router-link>
            <router-link
              to="/request-invite"
              class="text-primary-600 hover:text-primary-700 transition-colors duration-200"
            >
              Request Invite
            </router-link>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions } from "vuex";
import authApi from '@/api/auth';
import { isValidEmail } from '@/libraries/helper';

export default {
  name: 'Login',
  data() {
    return {
      email: '',
      password: '',
      isSubmitting: false,
    };
  },
  computed: {
    ...mapGetters({
      user: 'auth/user',
      isClient: 'auth/isClient',
    }),
    isValidEmailAddress() {
      return isValidEmail(this.email);
    },
    isFormValid() {
      return this.isValidEmailAddress && this.password;
    },
  },
  created() {
    const { register } = this.$route.query ? this.$route.query : false;
    if (register) {
      this.__showNotif('register', 'User', this.$t(
        'Thank you for your registration. We\'ve sent an email to your email address. Please Follow the link to activate your account'
      ));
    }
  },
  methods: {
    ...mapActions({
      setUser: 'auth/setUser'
    }),
    fetchUser() {
      const callback = (response) => {
        const user = response.data;
        this.setUser(user);
        if (this.isClient && !user.isActive) {
          window.location.href = '/welcome';
        } else if (user?.role?.id !== 1 && user?.role?.id !== 6 && !user.isActive) {
          window.location.href = '/welcome-settings';
        } else {
          window.location.href = '/';
        }
      };
      const errorCallback = function (e) {
        console.log(e);
      };
      authApi.getProfile(callback, errorCallback);
    },
    loginSubmit() {
      if (!this.isFormValid || this.isSubmitting) return;

      this.isSubmitting = true;
      const params = {
        email: this.email,
        password: this.password,
        lang: 'en',
      };
      const callback = (response) => {
        this.$store.dispatch('auth/clearAuth');
        const data = response.data;
        console.log(data);
        this.$store.dispatch('auth/setSession', data);
        this.fetchUser();
        this.isSubmitting = false;
      };
      const errorCallback = (err) => {
        const message = err?.response?.data?.message;
        this.__showNotif('error', 'Error', message);
        this.isSubmitting = false;
      };
      authApi.login(params, callback, errorCallback);
    },
  },
};
</script>