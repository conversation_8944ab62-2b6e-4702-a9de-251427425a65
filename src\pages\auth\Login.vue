<template>
	<div class="min-h-screen flex flex-col justify-center overflow-hidden py-4 lg:py-0">
		<div class="mx-auto">
			<img class="mx-auto h-12 mb-5 w-auto" src="@/assets/images/logo_large.png" alt="Planlagt">
		</div>
		<span class="text-center mb-8 mt-10"> <span class="font-bold text-2xl">{{ $t('Sign in to your account') }} </span>
			<div class="mt-6 sm:mx-auto sm:w-full lg:max-w-lg drop-shadow-xl">
				<div class="text-left bg-white py-12 px-8 shadow sm:rounded-lg lg:px-14 md:px-14">
					<form class="space-y-3" @submit.prevent="loginSubmit">
						<div>
							<div class="mb-2 font-medium text-sm">
								{{ $t('Email address') }}
							</div>
							<t-input v-model="email" :dataTest="'email'" :type="`email`" :value="email"
								class="w-full" />
							<span v-if="!isValidEmailAddress && email && email.length !== 0"
								class="text-red-500 text-xs">{{ $t('Invalid Email Address') }}</span>
						</div>
						<div>
							<div class="mb-2 font-medium text-sm">
								{{ $t('Password') }}
							</div>
							<t-input v-model="password" :dataTest="'password'" :type="`password`" :value="password"
								class="w-full" />
						</div>
						<div class="flex items-center justify-between">
							<div class="flex items-center mt-4">
								<input id="remember-me" name="remember-me" type="checkbox"
									class="h-4 w-4 focus:ring-primary-500 border-gray-300 rounded">
								<label for="remember-me" class="ml-2 block text-sm text-gray-900">
									{{ $t('Remember me') }}
								</label>
							</div>
							<div class="text-sm pt-4">
								<router-link to="/forgot-password" class="text-blue">
									{{ $t('Forgot password?') }}
								</router-link>
							</div>
						</div>
						<div>
							<t-button :type="'submit'" :color="`primary-solid`" class="w-full mt-3 h-[40px]"
								:isLoading="isSubmitting" :isDisabled="isSubmitting || !isFormValid">
								{{ $t('Sign In') }}
							</t-button>
						</div>
					</form>
				</div>
			</div>
		</span>
	</div>
	<div class="mt-2 md:fixed md:bottom-0 flex md:left-0 mb-4 justify-center text-center items-center ml-4">
		<div class="font-medium">©Planlagt 2025</div>
	</div>
	<div class="mt-2 md:fixed md:bottom-0 flex md:right-0 mb-4 justify-center text-center items-center">
		<a
			href=""
			target="_blank"
			class="ml-2 md:mr-12 font-medium"
		>{{ $t('Privacy Policy') }}</a>
    <a
			href=""
			target="_blank"
			class="mr-6 font-medium"
		>{{ $t('Terms & Condition') }}</a>
	</div>
</template>

<script>
	import {
		mapGetters
	} from "vuex";
	import authApi from '@/api/auth';
	import TButton from '@/components/global/Button.vue';
	import TInput from '@/components/form/Input.vue';
	import {
		isValidEmail
	} from '@/libraries/helper';
	import {
		googleTokenLogin
	} from "vue3-google-login";

	import {
		Menu,
		MenuButton,
		MenuItem,
		MenuItems,
	} from '@headlessui/vue';
	import { mapActions } from 'vuex';

	export default {
		components: {
			TButton,
			TInput,
			Menu,
			MenuButton,
			MenuItem,
			MenuItems,
		},
		setup() {
			return {};
		},
		data() {
			return {
				email: null,
				password: null,
				isSubmitting: false,
				isSignIn: false,
				isInit: false,
				langMenus: [{
						name: 'English',
						key: 'en'
					},
					{
						name: 'Norsk',
						key: 'no'
					},
				],
			};
		},
		computed: {
      ...mapGetters({
				user: 'auth/user',
				isClient: 'auth/isClient',
			}),
			isValidEmailAddress() {
				return isValidEmail(this.email);
			},
			isFormValid() {
				return (
					this.isValidEmailAddress &&
					this.password
				);
			},
		},
		created() {
			const {
				register
			} = this.$route.query ? this.$route.query : false;
			if (register) {
				this.__showNotif('register', 'User', this.$t(
					'Thank you for your registration. We\'ve sent an email to your email address. Please Follow the link to activate your account'
					));
			}
		},
		mounted() {

		},
		methods: {
			...mapActions({
				setUser: 'auth/setUser'
			}),
			changeLocale(locale) {
				localStorage.setItem(`locale`, locale.key);
				this.$i18n.locale = locale.key;
			},
			fetchUser() {
				const callback = (response) => {
					const user = response.data;
					this.setUser(user)
					if (this.isClient && !user.isActive) {
						window.location.href = '/welcome';
					} else if (user?.role?.id !== 1 && user?.role?.id !== 6 && !user.isActive) {
						window.location.href = '/welcome-settings';
					} else {
						window.location.href = '/';
					}
				};
				const errorCallback = function (e) {
					console.log(e)
				};
				authApi.getProfile(callback, errorCallback);
			},
			loginSubmit() {
				this.isSubmitting = true;
				const params = {
					email: this.email,
					password: this.password,
					lang: 'en',
				};
				const callback = (response) => {
					this.$store.dispatch('auth/clearAuth');
					const data = response.data;
					console.log(data)
					this.$store.dispatch('auth/setSession', data);
					this.fetchUser()
					// window.location.href = '/';
					this.isSubmitting = false;
				};
				const errorCallback = (err) => {
					const message = err?.response?.data?.message;
					this.__showNotif('error', 'Error', message);
					this.isSubmitting = false;
				};
				authApi.login(params, callback, errorCallback);
			},
			
			
		},
	};
</script>