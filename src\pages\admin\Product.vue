<template>
  <loader-circle v-if="isFetching" />
    <div>
        <div class="flex justify-between m-2">
            <div class="mt-2 ml-4">
                <label for="table-header" class="font-medium text-gray-800">Product list</label>
            </div>
            <div class="flex">
                <div class="relative mr-2">
                    <input type="text" v-model="keyword" v-value="keyword" @input="onInputSearch"
                        class="py-[7px] ps-10 pe-8 block w-full bg-white border-gray-200 rounded-md text-sm focus:bg-white focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none"
                        placeholder="Search">
                    <div class="absolute inset-y-0 start-0 flex items-center pointer-events-none ps-4">
                        <svg class="shrink-0 size-4 text-gray-500 dark:text-neutral-500"
                            xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
                        </svg>
                    </div>
                </div>
                <div>
                    <t-button :color="`primary-solid`" @click="add()">
                        <PlusIcon class="h-5 w-5 text-white" />
                        <span class="text-sm text-white">Add New</span>
                    </t-button>
                </div>
            </div>
        </div>
        <!-- Table Section -->
        <div v-if="!isFetching && items.length"
            class="m-2 overflow-x-auto [&::-webkit-scrollbar]:h-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300">
            <div class="min-w-full inline-block align-middle">
                <!-- Table -->
                <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                        <tr>
                            <th scope="col" class="w-[10px]">
                                <div class="text-gray-800"></div>
                            </th>
                            <th scope="col" class="min-w-[150px]">
                                <div
                                    class="pe-4 py-3 text-start flex items-center gap-x-1 text-sm font-medium text-gray-800">
                                    <!-- Sort Dropdown -->
                                    <div class="hs-dropdown relative inline-flex w-full cursor-pointer">
                                        <button id="hs-pro-dutnms" type="button"
                                            class="py-2.5 text-start font-semibold w-full flex items-center gap-x-1 text-sm text-nowrap  text-black-500 focus:outline-none focus:bg-gray-100"
                                            aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                                            Name
                                            <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24"
                                                height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="m7 15 5 5 5-5" />
                                                <path d="m7 9 5-5 5 5" /></svg>
                                        </button>

                                        <!-- Dropdown -->
                                        <div class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-40 transition-[opacity,margin] duration opacity-0 hidden z-10 bg-white rounded-sm shadow-[0_10px_40px_10px_rgba(0,0,0,0.08)]"
                                            role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-dutnms">
                                            <div class="p-1">
                                                <button type="button" @click="sortAsc()"
                                                    class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg"
                                                        width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                        stroke-linejoin="round">
                                                        <path d="m5 12 7-7 7 7" />
                                                        <path d="M12 19V5" /></svg>
                                                    Sort ascending
                                                </button>
                                                <button type="button" @click="sortDsc()"
                                                    class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg"
                                                        width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                        stroke-linejoin="round">
                                                        <path d="M12 5v14" />
                                                        <path d="m19 12-7 7-7-7" /></svg>
                                                    Sort descending
                                                </button>
                                            </div>
                                        </div>
                                        <!-- End Dropdown -->
                                    </div>
                                    <!-- End Sort Dropdown -->
                                </div>
                            </th>
                            <th scope="col" class="min-w-10">
                                <div
                                    class="px-4 py-3 text-start flex items-center gap-x-1 text-sm font-medium text-gray-800">
                                    <span> Cost </span>
                                    <span>
                                        <svg class="flex-shrink-0 w-3.5 h-3.5 text-gray-500 dark:text-gray-500"
                                            xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round">
                                            <path d="m7 15 5 5 5-5" />
                                            <path d="m7 9 5-5 5 5" />
                                        </svg>
                                    </span>
                                </div>
                            </th>
                            <th scope="col">
                                <div
                                    class="px-4 py-3 text-start flex items-center gap-x-1 text-sm font-medium text-gray-800">
                                </div>
                            </th>
                        </tr>
                    </thead>

                    <tbody class="divide-y divide-gray-200">
                        <tr v-for="(item, index) in items" :key="item.id" @mouseover="onHover(item)"
                            :class="{'bg-slate-100': item.id === curerentActive}">
                            <td class="whitespace-nowrap py-3">
                                <div class="w-[50px] flex items-center">
                                    <div class="grow">
                                        <span class="text-sm font-medium ml-4 text-gray-800">
                                            {{ (index + 1) + ((page-1)*limit) }}
                                        </span>
                                    </div>
                                    
                                </div>
                            </td>
                            <td class="whitespace-nowrap pe-4 py-2" @dblclick="edit(item)">
                                <div class="w-full flex items-center gap-x-3">
                                    <div class="grow">
                                        <span class="text-sm font-normal text-gray-800">
                                            {{ item.name }}
                                        </span>
                                    </div>
                                    <div class="w-[32px]">
                                            <div class="hs-dropdown hs-dropdown-example relative inline-flex w-[32px]"
                                                :class="{'hidden': item.id !== curerentActive}">
                                                <button id="hs-dropdown-example" type="button"
                                                    @click="this.selectedItem = item"
                                                    class="max-w-[32px] h-[30px] text-start w-full flex items-center text-sm text-nowrap font-normal pl-1 text-gray-800 shadow outline-gray-200 border border-gray-200 rounded focus:bg-gray-100"
                                                    aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                                        stroke-width="1.5" stroke="currentColor" class="size-5">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            d="M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z" />
                                                    </svg>
                                                </button>

                                                <div class="hs-dropdown-menu z-50 transition-[opacity,margin] duration hs-dropdown-open:opacity-100 opacity-0 
                                                hidden min-w-60 bg-white shadow-md rounded-md p-1 space-y-0.5 !mt-[-0.5rem] dark:bg-neutral-800 dark:border dark:border-neutral-700"
                                                    role="menu" aria-orientation="vertical"
                                                    aria-labelledby="hs-dropdown-custom-icon-trigger">
                                                    <a data-hs-overlay="#drawer-right" @click="edit(item)"
                                                        class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700"
                                                        href="#">
                                                        Edit
                                                    </a>
                                                    <!-- <a @click="addNew(item)"
                                                        class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700"
                                                        href="#">
                                                        Duplicate
                                                    </a> -->
                                                    <hr class="border-1">
                                                    <a @click="openDeleteModal(item)"
                                                        class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700"
                                                        href="#">
                                                        Delete
                                                    </a>
                                                </div>
                                            </div>
                                    </div>
                                </div>
                            </td>
                            <td class="whitespace-nowrap px-4 py-3">
                                <span class="text-sm text-gray-600">
                                  NOK. {{ isAdmin ? `${__currencyWithoutSymbol(item.costPrice)} (${__currencyWithoutSymbol(item.sellingPrice)})` : `${__currencyWithoutSymbol(item.costPrice)}` }}
                                </span>
                            </td>
                            <td class="whitespace-nowrap px-4 py-3">
                                <div class="flex">
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <!-- End Table -->
            </div>
        </div>
        <!-- End Table Section -->
        <!-- Footer -->
        <div v-if="!isFetching && items.length" class="mt-5 flex flex-wrap justify-between items-center gap-2">
            <p class="text-sm ml-4">
                <span class="font-medium text-stone-800 mr-1">{{ total }}</span>
                <span class="font-medium text-stone-800">Results</span>
            </p>
            <!-- Pagination -->
            <nav class="flex justify-end items-center gap-x-1" aria-label="Pagination">
                <button type="button" v-if="page > 1" @click="prevPage()"
                    class="py-2 inline-flex justify-center items-center gap-x-2 text-sm rounded-mdtext-stone-800 hover:bg-stone-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none"
                    aria-label="Previous">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round">
                        <path d="m15 18-6-6 6-6"></path>
                    </svg>
                    <span class="sr-only">Previous</span>
                </button>
                <div class="flex items-center gap-x-1 mr-2 ml-2">
                    <span
                        class="h-[26px] w-[26px] flex justify-center items-center bg-gray-300 py-2 text-sm rounded-md disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-700"
                        aria-current="page">{{ page }}</span>
                    <span
                        class="min-h-[38px] flex justify-center items-center text-stone-500 py-2 px-1.5 text-sm dark:text-neutral-500">of</span>
                    <span
                        class="min-h-[38px] flex justify-center items-center text-stone-500 py-2 px-1.5 text-sm dark:text-neutral-500">{{ maxPage }}</span>
                </div>
                <button type="button" v-if="page < maxPage" @click="nextPage()"
                    class="py-2 inline-flex justify-center items-center gap-x-2 text-sm rounded-mdtext-stone-800 hover:bg-stone-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none"
                    aria-label="Next">
                    <span class="sr-only">Next</span>
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round">
                        <path d="m9 18 6-6-6-6"></path>
                    </svg>
                </button>
            </nav>
            <!-- End Pagination -->
        </div>
        <!-- End Footer -->
        <DrawerRight :id="`drawer-right`" ref="drawerRight">
            <template #header>
                <div class="">
                    <div class="flex mt-4 pb-2 ">
                        
                        <p v-if="!isEdit" id="hs-pro-dutoo-label" class="text-sm font-semibold text-gray-800 ml-6 ">
                        Add Product
                        </p>
                        <p v-if="isEdit" id="hs-pro-dutoo-label" class="text-sm font-semibold text-gray-800 ml-6 ">
                        Edit {{ selectedItem.name }}
                        </p>
                    </div>
                </div>
            </template>
            <template #body>
                <div class="mt-2 mb-2 px-5 flex-1 flex flex-col overflow-y-auto overflow-hidden 
                        [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 
                        [&::-webkit-scrollbar-thumb]:bg-gray-300">

                    <!-- List Item -->
                    <div class="pt-2 grid grid-cols-1 gap-x-4 border-gray-200">
                        <div class="col-span-1">
                            <label for="productName" class="block text-sm font-semibold text-gray-700">Product
                                Name</label>
                            <t-input v-model="name" :value="name" :type="`text`" placeholder="Type in product name" :tabIndex="1">
                            </t-input>
                            <span v-if="errors.name" class="text-red-500 text-sm">{{ errors.name }}</span>
                        </div>
                    </div>
                    <!-- End List Item -->
                    <div class="mt-2">
                        <label for="costPrice" class="block text-sm font-semibold text-gray-700">Cost price</label>
                        <t-input class="block text-sm font-medium text-gray-700" v-model="costPrice" :value="costPrice"
                            :type="`text-pre-input`" placeholder="Type in price per unit" :tabIndex="2" :min="0">
                        </t-input>
                        <span v-if="errors.costPrice" class="text-red-500 text-sm">{{ errors.costPrice }}</span>

                    </div>
                    <div class="mt-2">
                        <label for="sellingPrice" class="block text-sm font-semibold text-gray-700">Selling
                            price</label>
                        <t-input v-model="sellingPrice" :value="sellingPrice" :type="`text-pre-input`"
                            placeholder="Type in selling price" :tabIndex="3" :min="0">
                        </t-input>
                        <span v-if="errors.sellingPrice" class="text-red-500 text-sm">{{ errors.sellingPrice }}</span>
                    </div>

                    <div class="relative mt-2">
                        <label class="block text-sm font-semibold text-gray-700">Category</label>
                        <select :tabIndex="4" v-model="selectedCategory"
                        class="text-sm block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="" disabled selected>Select category</option>
                        <option v-for="(category, index) in categories" :key="category.id" :value="category.id">
                            {{ category.name }}
                        </option>
                        </select>
                        <span v-if="errors.selectedCategory" class="text-red-500 text-sm">{{ errors.selectedCategory }}</span>
                    </div>
                    <!-- List Item -->
                    <div class="pt-2 grid grid-cols-1 gap-x-4 border-gray-200">
                        <label for="productName" class="block text-sm font-semibold text-gray-700">Description</label>
                        <t-input v-model="description" :value="description" :type="`area`"
                            placeholder="Type in product description" :tabIndex="5"> </t-input>
                        <span v-if="errors.description" class="text-red-500 text-sm">{{ errors.description }}</span>
                    </div>
                    <!-- End List Item -->
                    <div class="mb-4 flex justify-between items-center">
                        <div class="mt-2">
                            <label for="trackStock" class="block text-sm font-semibold text-gray-700 mr-2">Track
                                Stock</label>
                            <label class="block text-xs font-medium text-gray-400 mr-2"> For perishable item per use</label>
                        </div>
                        <!-- Switch/Toggle -->
                        <div class="relative inline-block mt-2">
                            <input type="checkbox" @change="onInputChange" :checked="isTrackStock"
                                id="hs-small-switch-with-icons" class="peer relative w-11 h-6 p-px 
                                bg-gray-100 border-transparent text-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:ring-blue-600 
                                disabled:opacity-50 disabled:pointer-events-none checked:bg-none checked:text-blue-600 checked:border-blue-600 focus:checked:border-blue-600 
                                dark:bg-neutral-800 dark:border-neutral-700 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-600
                                before:inline-block before:size-5 before:bg-white checked:before:bg-blue-200 before:translate-x-0 checked:before:translate-x-full 
                                before:rounded-full before:shadow before:transform before:ring-0 before:transition before:ease-in-out before:duration-200 
                                dark:before:bg-neutral-400 dark:checked:before:bg-blue-200" :tabIndex="5">
                            <label for="hs-small-switch-with-icons" class="sr-only">switch</label>
                            <span v-if="!isTrackStock" class="peer-checked:text-white text-gray-500 size-5 absolute top-[3px] 
                                start-0.5 flex justify-center items-center pointer-events-none 
                                transition-colors ease-in-out duration-200 dark:text-neutral-500">
                                <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M18 6 6 18"></path>
                                    <path d="m6 6 12 12"></path>
                                </svg>
                            </span>
                            <span v-if="isTrackStock" class="peer-checked:text-blue-600 text-gray-500 size-5 absolute top-[3px] 
                                end-0.5 flex justify-center items-center pointer-events-none 
                                transition-colors ease-in-out duration-200 dark:text-neutral-500">
                                <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <polyline points="20 6 9 17 4 12"></polyline>
                                </svg>
                            </span>
                        </div>

                        <!-- End Switch/Toggle -->
                    </div>
                    <div class="my-1">
                        <label for="stock" class="block text-sm font-medium text-gray-700">Stock Quantity</label>
                        <input type="number" id="stock" v-model="stock"
                            class="mt-1 p-2 block w-[1/4] border border-gray-300 rounded-md" placeholder="99" :tabIndex="6" />
                        <span v-if="errors.stock" class="text-red-500 text-sm">{{ errors.stock }}</span>
                        
                    </div>
                </div>

            </template>
            <template #footer>
                <div class="p-5 border-t border-gray-200">
                    <div class="flex items-center gap-x-2 justify-end">
                        <!-- Button -->
                        <t-button :color="`secondary-solid`" @click="closeDrawer" :tabIndex="8">
                            Cancel
                        </t-button>
                        <!-- End Button -->
                        <!-- Button -->
                        <t-button :color="`primary-solid`" @click="save()" :isLoading="isSaving"
                        :isDisabled="isSaving" :tabIndex="9">
                        {{ $t('Save Changes') }}
                        </t-button>
                        <!-- End Button -->
                    </div>
                </div>
            </template>
        </DrawerRight>


        <Confirmation :id="`confirm`">
            <template #header>
                <p class="text-base font-bold text-gray-800 dark:text-white">
                    Delete item
                </p>
            </template>
            <template #body>
                <!-- <div class="text-xs text-gray-800 dark:text-neutral-400"> -->
                <p class="text-sm text-gray-800 dark:text-neutral-400"> Are you sure want to delete this item? </p>
                <!-- </div> -->
            </template>
            <template #footer>
                <button type="button" data-hs-overlay="#confirm"
                    class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 focus:outline-none focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
                    Cancel
                </button>
                <button type="button" @click="confirmDelete()" data-hs-overlay="#confirm"
                    class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent 
                bg-red-600 text-white hover:bg-red-700 focus:outline-none focus:bg-red-700 disabled:opacity-50 disabled:pointer-events-none">
                    Delete
                </button>
            </template>
        </Confirmation>
    </div>
    <div v-if="!isFetching && !items.length" class="font-bold text-center w-[300px] top-0 bottom-0 my-auto mx-auto right-0 left-0 absolute h-10 w-10 z-50">
    {{ $t('No records found') }}
  </div>
</template>

<script>
    import {
        mapGetters
    } from 'vuex';
    import {
        AdjustmentsIcon,
        UserCircleIcon
    } from "@heroicons/vue/solid";
    import TInput from '@/components/form/Input.vue';
    import {
        PlusIcon
    } from "@heroicons/vue/solid";
    import productApi from "@/api/product";
    import DrawerRight from "@/components/form/DrawerRight.vue";
    import Confirmation from "@/components/modal/Confirmation.vue";
    import categoryApi from "@/api/category";

    import {
        HSStaticMethods
    } from "preline";
    export default {
        name: "userExpertise",
        components: {
            AdjustmentsIcon,
            UserCircleIcon,
            PlusIcon,
            DrawerRight,
            TInput,
            Confirmation
        },
        data() {
            return {
                orderBy: 'name',
                sortBy: 'asc',
                page: 1,
                total: 0,
                maxPage: 1,
                limit: 10,
                name: "",
                description: "",
                isTrackStock: false,
                isMultiQuantity: false,
                costPrice: 0,
                sellingPrice: 0,
                stock: 0,
                selectedItem: null,
                itemId: null,
                items: [],
                meta: null,
                isEdit: false,
                keyword: "",
                curerentActive: -1,
                isSaving: false,
                debounceGetAll: null,
                categories: [],
                selectedCategory: "",
                errors: {
                    name: null,
                    costPrice: null,
                    sellingPrice: null,
                    stock: null,
                    selectedCategory: null,
                },
                isFetching: false,
            };
        },
        computed: {
            ...mapGetters({
                user: 'auth/user',
                isAdmin: 'auth/isAdmin',
            }),
        },
        created() {
            this.debounceGetAll = this.debounce(this.getAll, 300);
            this.getAll();
            this.getAllCategories()
        },
        methods: {
            getAllCategories(keyword = null) {
                const callback = (response) => {
                    const categories = response.data;
                    this.categories = categories;
                }
                const errCallback = (err) => {
                    const message = err?.response?.data?.message;
                    this.__showNotif('error', 'Error', message);
                }
                const params = {
                    orderBy: "name",
                    sortBy: "asc",
                    page: 1,
                    limit: 1000,
                }
                if (keyword) params.keyword = keyword;
                categoryApi.getList(params, callback, errCallback)
            },
            validateForm() {
                this.errors.name = !this.name ? 'Product name is required' : null;
                this.errors.costPrice = this.costPrice < 0  ? 'Cost price is required & cannot be less than 0' : null;
                this.errors.sellingPrice = this.sellingPrice < 0   ? 'Selling price is required & be less than  0' : null;
                this.errors.stock = (this.stock < 1 ) ? 'Stock quantity is required' : null;
                return Object.values(this.errors).every(error => !error);
            },
            openDeleteModal(item) {
                HSOverlay.open('#confirm');
                this.selectedItem = item
            },
            sortAsc() {
                this.sortBy = "asc"
                this.getAll()
            },
            sortDsc() {
                this.sortBy = "desc"
                this.getAll()
            },
            onHover(item) {
                this.curerentActive = item.id;
            },
            onHoverOut(item) {
                this.curerentActive = -1;
            },
            onInputChange(e) {
                this.isTrackStock = e.target.checked;
            },
            onMultiQuantityChange(e) {
                this.isMultiQuantity = e.target.checked;
            },
            nextPage() {
                this.page = this.page + 1;
                this.getAll()

            },
            prevPage() {
                this.page = this.page - 1;
                this.getAll()

            },
            debounce(func, wait) {
                let timeout;
                return function (...args) {
                    clearTimeout(timeout);
                    timeout = setTimeout(() => {
                        func.apply(this, args);
                    }, wait);
                };
            },
            onInputSearch() {
                // this.debounce(this.getAll(this.keyword), 300); // 300ms debounce
                this.debounceGetAll(this.keyword);
            },
            confirmDelete() {
                if (this.selectedItem) {
                    const callback = (response) => {
                        const data = response.data;
                        const message = response.message;
                        const isDelete = true;
                        if (this.items.length <= 1  && this.page > 1) this.page = this.page - 1
                        this.getAll();
                        this.__showNotif('success', 'Success', message);
                    }
                    const errCallback = (err) => {
                        const message = err?.response?.data?.message;
                        this.__showNotif('error', 'Error', message);
                    }
                    const id = this.selectedItem.id;
                    productApi.delete(id, callback, errCallback)
                }
            },
            closeDrawer() {
                this.$refs.drawerRight.visibleRight = false;
            },
            reset() {
                this.name = ""
                this.description = ""
                this.isTrackStock = false
                this.isMultiQuantity = false
                this.costPrice = 0
                this.sellingPrice = 0
                this.stock = 1

                this.errors.name = null 
                this.errors.costPrice = null 
                this.errors.sellingPrice = null 
                this.errors.description = null 
                this.errors.stock = null 
            },
            save() {
                if (this.validateForm()) {
                    if (!this.isEdit) {
                        this.addNew()
                    } else {
                        this.updateItem()
                    }
                    this.$refs.drawerRight.visibleRight = false;
                }
            },
            add() {
                this.isEdit = false;
                this.reset(); 
                this.edit();
            },
            add() {
                this.isEdit = false;
                this.reset(); 
                this.edit();
                setTimeout(() => {
                    HSStaticMethods.autoInit();
                }, 500);
            },
            edit(item) {
                this.reset();
                this.$refs.drawerRight.visibleRight = true;
                if (item) {
                    this.isEdit = true;
                    this.selectedItem = item;
                    this.name = item.name;
                    this.description = item.description;
                    this.isTrackStock = item.isTrackStock;
                    this.isMultiQuantity = item.isMultiQuantity;
                    this.costPrice = item.costPrice;
                    this.sellingPrice = item.sellingPrice;
                    this.stock = item.stock;
                    this.selectedCategory = item?.category?.id;
                }
            },
            updateItem() {
                this.isSaving = true;
                const callback = (response) => {
                    const message = response.message;
                    this.getAll();
                    this.name = "";
                    this.description = "";
                    this.__showNotif('success', 'Success', message);
                    this.isSaving = false;
                }
                const errCallback = (err) => {
                    const message = err?.response?.data?.message;
                    this.__showNotif('error', 'Error', message);
                    this.isSaving = false;
                }
                const params = {
                    name: this.name,
                    description: this.description,
                    isTrackStock: this.isTrackStock,
                    costPrice: this.costPrice,
                    sellingPrice: this.sellingPrice,
                    stock: this.stock,
                    isMultiQuantity: this.isMultiQuantity,
                    categoryId: this.selectedCategory
                }
                this.isEdit = false;
                productApi.update(this.selectedItem.id, params, callback, errCallback)
            },
            addNew(item = null) {
                this.isSaving = true;
                if (item) {
                    this.name = this.selectedItem.name
                    this.description = this.selectedItem.description
                    this.isTrackStock = this.selectedItem.isTrackStock
                    this.costPrice = this.selectedItem.costPrice
                    this.sellingPrice = this.selectedItem.sellingPrice
                    this.stock = this.selectedItem.stock
                    this.isMultiQuantity = this.selectedItem.isMultiQuantity
                    this.selectedCategory = this.selectedItem?.category?.id
                }

                const callback = (response) => {
                    const message = response.message;
                    this.getAll();
                    this.name = "";
                    this.description = "";
                    this.isTrackStock = false;
                    this.costPrice = 0;
                    this.sellingPrice = 0;
                    this.stock = 0;

                    this.__showNotif('success', 'Success', message);
                    this.isSaving = false;
                }
                const errCallback = (err) => {
                    const message = err?.response?.data?.message;
                    this.__showNotif('error', 'Error', message);
                    this.isSaving = false;
                }
                const params = {
                    name: this.name,
                    description: this.description,
                    isTrackStock: this.isTrackStock,
                    costPrice: this.costPrice,
                    sellingPrice: this.sellingPrice,
                    stock: this.stock,
                    isMultiQuantity: this.isMultiQuantity,
                    categoryId: this.selectedCategory
                }
                productApi.create(params, callback, errCallback)
            },
            getAll(keyword = null) {
                this.isFetching = true;
                const callback = (response) => {
                    const data = response.data;

                    const meta = response.meta;
                    this.items = data;
                    this.meta = meta;
                    this.page = meta.currentPage;
                    this.maxPage = meta.lastPage;
                    this.total = meta.total;
                    setTimeout(() => {
                        HSStaticMethods.autoInit();
                    }, 500);
                    
                    if (keyword ) {
                        this.page = 1;
                    }
                    this.isFetching = false;
                }
                const errCallback = (err) => {
                    const message = err?.response?.data?.message;
                    this.__showNotif('error', 'Error', message);
                    this.isFetching = false;
                }

                const params = {
                    orderBy: this.orderBy,
                    sortBy: this.sortBy,
                    page: this.page,
                    limit: this.limit,
                }
                if (keyword) params.keyword = keyword;
                productApi.getList(params, callback, errCallback)
            }
        },
    };
</script>

<style scoped>
    /* Add any scoped styles here if necessary */
</style>