<template>
  <div>
    <div
      class="mx-5 flex flex-row items-center justify-between px-2 py-1 hover:bg-gray-200 border-t-[1px] border-b-[1px] border-gray-200"
      v-for="(item, index) in items"
      :key="index"
      @click.stop="showTask(item)"
    >
      <div class="flex items-center">
        <CheckCircleIcon  @click.stop="updateComplete(item)" class="h-6 w-6 pointer mr-1" aria-hidden="true"
        :class="{'text-green-600': item?.status === 'completed'}"></CheckCircleIcon>
        <kbInput
          controlType="subtaskInput"
          :ref="`subtask${index}`"
          v-model="item.name"
          :value="item.name"
          @blur="handleBlur(item, index)"
          @focus="handleFocus(item)"
          @click.stop=""
          @keyupEnter="handleBlur(item, index)"
          :style="{ minWidth: '50px', maxWidth: '400px', width: 'auto' }"
        ></kbInput>
      </div>
      <div class="flex items-center">
        <AssigneeDirectSubtask @click.stop="" :task="item" @select="assigneeSelected" @remove="assigneeRemoved" :key="item.assign?.id" class="w-full"
          :currentAssignee="item.assign" :users="users"></AssigneeDirectSubtask>
        <ChevronRightIcon @click.stop="showTask(item)" class="pointer ml-2 w-4 h-4" />
      </div>
    </div>
    <t-button @click="addColumn()" :color="`primary-white`" class="ml-5 px-2 py-1 mt-1">
      <span class="text-sm">+ {{ mode === "board" ? "Column" : "Subtask" }}</span>
    </t-button>
  </div>
</template>

<script>
import AssigneeDirectSubtask from "@/components/kanban/AssigneeDirectSubtask.vue";
import store from "../../../pages/kanban/store.js";
import TButton from '@/components/global/Button.vue';
import { ChevronRightIcon } from '@heroicons/vue/outline';
import { CheckCircleIcon } from '@heroicons/vue/solid';
import tasksApi from "@/api/tasks";
import { delay } from '@/libraries/helper';
import {
  mapGetters,
  mapActions
} from 'vuex';

export default {
  components: {
    TButton,
    ChevronRightIcon,
    AssigneeDirectSubtask,
    CheckCircleIcon
  },
  props: {
    data: {
      type: Object,
      required: true
    },
    mode: {
      type: String,
      required: true
    },
    parentId: {
      type: Number,
      required: false
    },
    projectId: {
      type: Number,
      required: false
    },
    users: {},
  },
  data() {
    return {
      itemRefs: [],
      items: [],
      hasMounted: false, // New state to track if component has mounted
      assignTo: null,
      task: null,
    };
  },
  created() {
    this.$soketio.on('task_add', (task) => {
      delay(() => {
        if (task?.parentId === this.parentId) {
          // Find if the task already exists in the current items array
          const taskIndex = this.items.findIndex(item => item.id === task.id);

          if (taskIndex !== -1) {
            // If the task exists, update it
            this.items.splice(taskIndex, 1, task);
          } else {
            // If the task doesn't exist, add it to the list
            this.items.push(task);
          }
          // Optionally update the data.subTask array as well
          this.data.subTask = Array.from(this.items);
        }
      }, 1000);
    });

    this.$soketio.on('task_delete', (task) => {
      if (task?.parentId === this.parentId) {
        // Find the index of the task in the current items array
        const taskIndex = this.items.findIndex(item => item.id === task.id);

        if (taskIndex !== -1) {
          // If the task exists, remove it from the array
          this.items.splice(taskIndex, 1);
          
          // Optionally update the data.subTask array as well
          this.data.subTask = Array.from(this.items);
        }
      }
    });
  },

  mounted() {
    this.initSubtask()

    // Set the component as mounted after initialization
    this.$nextTick(() => {
      this.hasMounted = true;
    });
  },
  computed: {
    ...mapGetters({
      getActiveProject: 'application/getActiveProject',
      user: 'auth/user',
      isAdmin: 'auth/isAdmin',
    }),
  },
  methods: {
    ...mapActions({
      updateTaskInStore: 'application/updateTaskInStore',
    }),
    initSubtask() {
      this.data.subTask.forEach((el) => {
        el.interacted = false; // Add the interacted flag
        this.items.push(el);
      });
    },
    assigneeSelected(event, task) {
      this.assignTo = event?.id
      this.task = task
      if (task?.id) {
        this.assignTask();
      }
    },
    assignTask() {
      // Define the callback function that will be called after the API request is successful
      const callback = (response) => {
        const data = response.data;
        // this.updateTaskInStore(data);
        this.task = null
      };

      // Define the error callback function to handle any errors during the API request
      const errCallback = (err) => {
        const message = err?.response?.data?.message || 'Error occurred while updating the task';
        this.__showNotif('error', 'Error', message);
        // this.handleErrors(err.response.data);
        this.task = null
      };

      const params = {
        assignTo: this.assignTo,
      }
      // Make the API request to update the task
      tasksApi.updateTask(this.task?.id, params, callback, errCallback);
    },
    assigneeRemoved(task) {
      this.assignTo = null
      this.task = task
      if (task?.id) {
        this.assignTask()
      }
    },
    addColumn() {
      this.items.push({ 
        name: '',
        dueDate: '',
        assignTo: '',
        projectId: this.projectId,
        type: this.$store.state.application.task.type,
        parentId: this.parentId,
        interacted: false // Add interacted flag for new columns or subtasks
      });
      this.data.subTask = Array.from(this.items);
      setTimeout(() => {
        const lastRef = this.$refs[`subtask${this.items.length - 1}`];
        lastRef[0].setFocusSectionSubtask();
      }, 200);
    },
    delColumn(idx, item) {
      this.confirmDelete(idx, item)
    },
    confirmDelete(idx, item) {
      const callback = (response) => {
          const data = response.data;
          this.items.splice(idx, 1);
          this.data.subTask = Array.from(this.items);
      }
      const errCallback = (err) => {
          console.log(err)
      }
      tasksApi.delete(item.id, callback, errCallback)
    },
    handleBlur(item, index) {
      // Only proceed if the component has mounted and the field has been interacted with
      if (this.hasMounted && item.interacted) {
        if (item.id) {
          // Update the existing task
          this.updateTask(item, index);
        } else {
          // Create a new subtask
          this.createSubtask(item, index);
        }
      }
    },

    openTask(item) {
      this.$emit('openTask', item)
    },
    handleFocus(item) {
      // Mark the field as interacted when it gains focus
      item.interacted = true;
    },
    updateTask(item) {
      const callback = (response) => {
        const data = response.data;
        const taskIndex = this.items.findIndex(t => t.id === data.id);
        if (taskIndex !== -1) {
          Object.assign(this.items[taskIndex], data);
        }
      };
      const errCallback = (err) => {
        const message = err.response.data.message;
        this.__showNotif('error', 'Error', message || 'Error User');
      };
      item.project = item.project.id;
      tasksApi.update(item.id, item, callback, errCallback);
    },
    createSubtask(item, index) {
      const callback = (response) => {
        const data = response.data;
        data.interacted = true;
        if (index !== -1) {
          this.items[index] = data;
        }
        // If you also need to update the `data.subTask` array
        this.data.subTask = Array.from(this.items);
        this.task = data
        this.assignTask()
      };

      const errCallback = (err) => {
        const message = err.response.data.message;
      };

      tasksApi.create(item, callback, errCallback);
    },
    showTask(task) {
      if (this.$route.name === 'TaskDetailKanbanFull' || this.$route.name === 'TaskDetailKanban') {
        this.$router.push(`/e/kanban/${this.__encryptProjectData(this.getActiveProject.id, this.getActiveProject.slug)}/${btoa(task?.slug)}`)
      } else if (this.$route.name === 'TaskDetailFull' || this.$route.name === 'TaskDetail') {
        this.$router.push(`/tasks/${btoa(this.getActiveProject.id)}/${btoa(task.slug)}`)
      } else if (this.$route.name === 'TaskAloneFull') {
        this.$router.push(`/e/${btoa(this.getActiveProject.id)}/${btoa(task?.slug)}/f`)
      } else if (this.$route.name === 'TaskAlone') {
        this.$router.push(`/e/${btoa(this.getActiveProject.id)}/${btoa(task?.slug)}`)
      }
      this.$store.state.application.task = task
      this.$store.state.application.task.active = task.id;
      this.$store.state.application.task.status = task.status;
      this.$store.state.application.task.type = task.type;
      this.$store.state.application.task.addDirectly = false;
      this.$store.state.application.task.edit = true;
      this.$store.state.application.task.add = false;
      this.$store.state.application.mutate = true;
    },

    updateComplete(task) {
        const callback = (response) => {
          const data = response.data;

          // Merge the updated data with the existing task data
          const finalData = {
            ...this.getActiveTask, // Existing task data
            ...data, // New data from the server
          };

          // Update the status in the items array
          const taskIndex = this.items.findIndex(item => item.id === task.id);
          if (taskIndex !== -1) {
            this.items[taskIndex].status = data.status;
          }

          // Optionally, update the data.subTask array as well
          this.data.subTask = Array.from(this.items);
        };
        let params = {
          status: !task.status || task.status === 'incomplete' ? 'complete' : 'incomplete'
        }
        // Make the API call to update the task on the server
        tasksApi.compeleteTask(task.id, params, callback, (error) => {
          console.error('Error updating task:', error);
        });
      },
  },
  expose: ['validate']
};
</script>

<style scoped></style>
