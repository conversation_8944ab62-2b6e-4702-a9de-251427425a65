import { createRouter, createWebHistory, createWebHashHistory } from 'vue-router';

// Routes
const routes = [
	{ name: 'Login', path: '/login', component: () => import('@/pages/auth/Login.vue'), meta: { title: 'Login', layout: 'auth' }, },
	{ name: 'TestComponent', path: '/test', component: () => import('@/pages/TestComponent.vue'), meta: { title: 'Testing', layout: 'auth' }, },
	{ name: 'Logout', path: '/logout', component: () => import('@/pages/auth/Logout.vue'), meta: { title: 'Logout', layout: 'auth' }, },
	{ name: 'ForgotPassword', path: '/forgot-password', component: () => import('@/pages/auth/ForgotPassword.vue'), meta: { title: 'Forgot Password', layout: 'auth' }, },
	{ name: 'ResetPassword', path: '/user/resetPassword', component: () => import('@/pages/auth/ResetPassword.vue'), meta: { title: 'Reset Password', layout: 'auth' }, },
	{ name: 'Register', path: '/register', component: () => import('@/pages/auth/Register.vue'), meta: { title: 'Register', layout: 'auth' }, },
	{ name: 'Resend Email', path: '/resend-email', component: () => import('@/pages/auth/ResendEmail.vue'), meta: { title: 'Resend Email', layout: 'auth' }, },
	{ name: 'Verify', path: '/user/verify', component: () => import('@/pages/auth/Verify.vue'), meta: { title: 'Verify', layout: 'auth' }, },
	{ name: 'MagicLink', path: '/magic-link', component: () => import('@/pages/auth/MagicLink.vue'), meta: { title: 'Magic Link', layout: 'auth' }, },
	{ name: 'RequestInvite', path: '/request-invite', component: () => import('@/pages/auth/RequestInvite.vue'), meta: { title: 'Request Invite', layout: 'auth' }, },
	{ name: 'Home', path: '/home', component: () => import('@/pages/home/<USER>'), meta: { title: 'Home', layout: 'main' }, },
	{ name: 'Client', path: '/admin/client', component: () => import('@/pages/client/Index.vue'), meta: { title: 'Client', layout: 'main' }, },
	{ name: 'Users', path: '/admin/users', component: () => import('@/pages/admin/User.vue'), meta: { title: 'Users', layout: 'main' }, },
	{ name: 'MyTask', path: '/tasks', component: () => import('@/pages/task/Index.vue'), meta: { title: 'Task', layout: 'main' }, },
	{ name: 'TaskDetail', path: '/tasks/:id/:slug', component: () => import('@/pages/task/Index.vue'), meta: { title: 'Task', layout: 'main' }, },
	{ name: 'TaskDetailFull', path: '/tasks/:id/:slug/f', component: () => import('@/pages/task/Index.vue'), meta: { title: 'Task', layout: 'main' }, },
	{ name: 'Inbox', path: '/inbox', component: () => import('@/pages/inbox/Index.vue'), meta: { title: 'Inbox', layout: 'main' }, },
	{ name: 'UserRole', path: '/admin/role', component: () => import('@/pages/admin/UserRole.vue'), meta: { title: 'Manage Role', layout: 'main' }, },
	{ name: 'Category', path: '/admin/category', component: () => import('@/pages/admin/Category.vue'), meta: { title: 'Manage Category', layout: 'main' }, },

	{ name: 'UserJob', path: '/admin/job', component: () => import('@/pages/admin/JobTitle.vue'), meta: { title: 'Manage Job', layout: 'main' }, },
	{ name: 'UserExpertise', path: '/admin/expertise', component: () => import('@/pages/admin/Expertise.vue'), meta: { title: 'Manage Expertise', layout: 'main' }, },
	{ name: 'Service', path: '/admin/service', component: () => import('@/pages/admin/Service.vue'), meta: { title: 'Manage Service', layout: 'main' }, },
	{ name: 'Product', path: '/admin/product', component: () => import('@/pages/admin/Product.vue'), meta: { title: 'Manage Product', layout: 'main' }, },
	{ name: 'Offer', path: '/admin/offer', component: () => import('@/pages/admin/Package.vue'), meta: { title: 'Manage Package', layout: 'main' }, },
	{ name: 'EventHome', path: '/event', component: () => import('@/pages/event/Event.vue'), meta: { title: 'Event', layout: 'main' }, },
	
	{ name: 'Home', path: '/', component: () => import('@/pages/home/<USER>'), meta: { title: 'Event', layout: 'main' }, },
	{ name: 'Testing', path: '/testing-fikuri', component: () => import('@/components/testing.vue'), meta: { title: 'Event', layout: 'main' }, },

	{ name: 'Profile', path: '/profile', component: () => import('@/pages/profile/AccountProfile.vue'), meta: { title: 'Profile ', layout: 'main' }, },
	
	{ name: 'Freelancer', path: '/welcome-settings', component: () => import('@/pages/freelancer/Index.vue'), meta: { title: 'Event', layout: 'main' }, },
	{ name: 'ChangePassword', path: '/settings/change-password', component: () => import('@/pages/freelancer/ChangePassword.vue'), meta: { title: 'Settings ', layout: 'main' }, },
	{ name: 'AccountProfile', path: '/settings/profile', component: () => import('@/pages/freelancer/AccountProfile.vue'), meta: { title: 'Settings ', layout: 'main' }, },
	{ name: 'SelectExpertise', path: '/settings/expertise', component: () => import('@/pages/freelancer/SelectExpertise.vue'), meta: { title: 'Settings ', layout: 'main' }, },
	{ name: 'ServicePackage', path: '/settings/service', component: () => import('@/pages/freelancer/ServicePackage.vue'), meta: { title: 'Settings ', layout: 'main' }, },
	{ name: 'AddServicePackage', path: '/settings/service/:id', component: () => import('@/pages/freelancer/AddServicePackage.vue'), meta: { title: 'Settings ', layout: 'main' }, },
	{ name: 'Payment', path: '/settings/payment', component: () => import('@/pages/freelancer/Payment.vue'), meta: { title: 'Settings ', layout: 'main' }, },
	{ name: 'Calendar', path: '/settings/calendar', component: () => import('@/pages/freelancer/Calendar.vue'), meta: { title: 'Settings ', layout: 'main' }, },
	
	{ name: 'FirstLoginClient', path: '/welcome', component: () => import('@/pages/client/FirstLogin.vue'), meta: { title: 'Welcome', layout: 'main' }, },
	
	{ name: 'Event', path: '/event', component: () => import('@/pages/event/Event.vue'), meta: { title: 'Event', layout: 'main' }, },
	{ name: 'EventItems', path: '/event/items/:id', component: () => import('@/pages/event/EventItems.vue'), meta: { title: 'Event Items', layout: 'main' }, },
	{ name: 'EventQuotation', path: '/event/quotation/:id', component: () => import('@/pages/event/EventQuotation.vue'), meta: { title: 'Event Quotation', layout: 'main' }, },
	{ name: 'EventQuotationClient', path: '/client/quotation/:id', component: () => import('@/pages/event/EventQuotationClient.vue'), meta: { title: 'Event Quotation', layout: 'main' }, },
	{ name: 'PrintEventQuotation', path: '/print/quotation/:id', component: () => import('@/pages/event/EventQuotation.vue'), meta: { title: 'Event Quotation', layout: 'auth' }, },

	{ name: 'TaskAlone', path: '/e/:id/:slug', component: () => import('@/pages/kanban/TaskAlone.vue'), meta: { title: 'Task Detail', layout: 'main' }, },
	{ name: 'TaskAloneFull', path: '/e/:id/:slug/f', component: () => import('@/pages/kanban/TaskAlone.vue'), meta: { title: 'Task Detail', layout: 'main' }, },
	{ name: 'EventKanban', path: '/e/kanban/:id', component: () => import('@/pages/kanban/Index.vue'), meta: { title: 'Event Kanban', layout: 'main' }, },
	{ name: 'TaskDetailKanban', path: '/e/kanban/:id/:slug', component: () => import('@/pages/kanban/Index.vue'), meta: { title: 'Event Kanban', layout: 'main' }, },
	{ name: 'TaskDetailKanbanFull', path: '/e/kanban/:id/:slug/f', component: () => import('@/pages/kanban/Index.vue'), meta: { title: 'Event Kanban', layout: 'main' }, },
	{ name: 'EventOverview', path: '/e/overview/:id', component: () => import('@/pages/overview/Index.vue'), meta: { title: 'Event Overview', layout: 'main' }, },
	{ name: 'EventReport', path: '/e/report/:id', component: () => import('@/pages/report/Index.vue'), meta: { title: 'Event Report', layout: 'main' }, },
	{ name: 'EventFiles', path: '/e/files/:id', component: () => import('@/pages/files/Index.vue'), meta: { title: 'Event Files', layout: 'main' }, },
	{ name: 'PackageItem', path: '/admin/offer/:id', component: () => import('@/pages/admin/PackageItem.vue'), meta: { title: 'Manage Package', layout: 'main' }, },
	{ name: 'EventProjects', path: '/projects', component: () => import('@/pages/projects/Index.vue'), meta: { title: 'Manage Projects', layout: 'main' }, },

	{ name: 'NotFound', path: '/:path(.*)', component: () => import('@/pages/errors/NotFound.vue'), meta: { title: 'Not Found', layout: 'auth' }, },

];

// Middleware here
import vueMiddleware from "@grafikri/vue-middleware";
import authMiddleware from '@/middleware/auth.js';
// import { HSStaticMethods } from 'preline';
import { nextTick } from 'vue';
const middleware = [authMiddleware];

// Apply middleware.
for (var r in routes) {
	routes[r].meta.middleware = middleware;
}

// @todo: move this somewhere else
let mode = 'spa';
const history = (mode == 'spa') ? createWebHistory() : createWebHashHistory();

export default {
	createRouter(_store) {
		let router = createRouter({
			history: history,
			scrollBehavior() {
				return { x: 0, y: 0 };
			},
			routes
		});
		router.afterEach((to, from, failure) => {
			if (!failure) {
				// setTimeout(() => {
				// 	// nextTick(()=>{
				// 		HSStaticMethods.autoInit();
				// 	// })
				// }, 500);
			}
		});
		router.beforeEach(vueMiddleware({ store: _store }));
		return router;
	}
};