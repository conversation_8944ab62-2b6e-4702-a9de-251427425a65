<template>
	<div class="min-h-full flex flex-col justify-center py-40 sm:px-6 lg:px-8">
		<!-- <div class="sm:mx-auto sm:w-full sm:max-w-lg">
			<img
				class="mx-auto h-12 mb-5 w-auto"
				src="@/assets/images/svg/bannerbite.svg"
				alt="Bannerbite"
			>
		</div> -->
		<span class="text-center mb-6"> Please enter your new password </span>


		<div class="mt-2 sm:mx-auto sm:w-full lg:max-w-md drop-shadow-xl">
			<div class="bg-white py-12 px-14 shadow sm:rounded-xl lg:px-14 md:px-14">
				<form
					class="space-y-2"
					@submit.prevent="submit"
				>
					<div>
						<div class="flex justify-between">
							<div
								for="password"
								class="block text-sm font-medium text-gray-700"
							>
								Password
							</div>
							<!-- <div
								class="text-sm"
								:class="{'very-weak': warningError === 'Too Weak',
									'weak': warningError === 'Weak',
									'strong': warningError === 'Strong' || warningError === 'Medium',
								}"
							>
								{{ warningError }}
							</div> -->
							<div
								class="text-[10px] mt-1"
							>
								Min 8 chars, Max 20 chars, numeric and special char
							</div>
						</div>
						<div class="mt-1">
							<t-input
								v-model="password"
								:type="`password`"
								:maxlength="20"
								:value="password"
								class="w-full"
							/>
						</div>
						<span
							v-if="warningError === 'Weak' && password"
							class="text-red-500 text-xs"
						>Please enter a password of at least 8 characters, and use a combination of uppercase and lowercase letters, and numbers</span>
					</div>
					<div>
						<label
							for="confirm_password"
							class="block text-sm font-medium text-gray-700"
						>Confirm Password</label>
						<div class="mt-1">
							<t-input
								v-model="confirm_password"
								:type="`password`"
								:maxlength="20"
								:value="confirm_password"
								class="w-full"
							/>
						</div>
						<label
							v-if="confirm_password && confirm_password !== password"
							class="text-red-500 text-xs"
						>Password do not match</label>
					</div>

					<div class="flex items-center justify-end">
						<div class="text-sm mb-5">
							<router-link
								to="/login"
								class="font-medium text-blue"
							>
								Back to login
							</router-link>
						</div>
					</div>

					<div>
						<t-button
							:type="'submit'"
							:color="`primary-solid`"
							class="w-full"
							:isLoading="isSaving"
							:isDisabled="!isFormValid || isSaving"
						>
							Reset Password
						</t-button>
					</div>
				</form>
			</div>
		</div>
	</div>
</template>


<script>

import authApi from '@/api/auth';
import TInput from '@/components/form/Input.vue';
import TButton from '@/components/global/Button.vue';
import { checkPassword  } from '@/libraries/helper';

export default {
	
	components: {
		TInput,
		TButton,
	},
	setup() {
		return {
		};
	},
	data() {
		return {
			password: null,
			confirm_password: null,
			isSaving: false,
			warningError: '',
			isError: false,
		};
	},
	computed: {
		token() {
			return localStorage.getItem('access_token');
		},
		email() {
			const { email } = this.$route.query ? this.$route.query : '';
			return email;
		},
		isFormValid() {
			return (
				this.password === this.confirm_password && this.warningError !== 'Weak' && this.warningError !== 'Too Weak' && this.password
			);
		},
	},
	watch: {
		password() {
			if (this.password) {
				const result = checkPassword(this.password);
				if (result === 0) this.warningError = 'Too Weak';
				if (result === 1) this.warningError = 'Weak';
				if (result === 2) this.warningError = 'Medium';
				if (result === 3) this.warningError = 'Strong';
			}
			if (!this.password) this.warningError = '';
		},
	},
	mounted() {
	},
	beforeUnmount() {
		this.doLogout();
	},
	methods: {
		submit() {
			this.isSaving = true;
			this.isError = false;

			const params = {
				email: this.email,
				token: this.token,
				newPassword: this.password,
				confirmPassword: this.password,
			};
			const callback = (response) => {
				const message = response.message;
				this.__showNotif('success', 'User', message);
				this.$router.push('/');
				this.isSaving = false;
				this.isError = false;
				this.doLogout();
			};
			const errorCallback = (err) => {
				const message = err?.response?.data?.message;
				this.__showNotif('error', 'Error', message);
				this.isSaving = false;
				this.isError = true;
				this.doLogout();
			};
			authApi.reset(params, callback, errorCallback);
		},
		doLogout() {
			this.$store.dispatch('auth/clearAuth');
		},
	},
};
</script>