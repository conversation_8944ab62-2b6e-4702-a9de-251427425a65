// tests/fixtures.js
const {
    test: base
} = require('@playwright/test');

exports.test = base.extend({
    loggedInPage: async ({
        page
    }, use) => {
        // Navigate to the login page and wait until network is idle
        await page.goto('http://localhost:4444/', {
            waitUntil: 'networkidle'
        });

        // Fill in the email address
        await page.fill('[data-test="email"]', '<EMAIL>');

        // Fill in the password
        await page.fill('[data-test="password"]', 'PlanlagtAdmin2024');

        // Click the "Sign In" button and wait for navigation to complete
        await Promise.all([
            page.waitForNavigation({
                waitUntil: 'networkidle'
            }),
            page.click('button:has-text("Sign In")'),
        ]);

        // Wait for any additional network activity to finish after login
        await page.waitForLoadState('networkidle');

        // Wait for an element containing text "Home" to appear
        await page.waitForSelector('text=Home', {
            timeout: 10000
        });

        // Provide the logged-in page to the test
        await use(page);
    },
});