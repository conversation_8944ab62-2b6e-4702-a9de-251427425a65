<template>
	<div class="relative">
		<!-- global -->
		<div v-if="type !== 'password' && type !== 'area' && type !== 'timer' && type !== 'text-input' && type !== 'text-pre-input'">
			<input
				class="w-full "
				:class="theClass"
				:type="type"
				:name="name"
				:data-test="dataTest"
				:value="value"
				:placeholder="placeholder"
				:autocomplete="autocomplete"
				:disabled="isDisabled"
				:tabindex="tabIndex"
				:style="customStyle"
				:min="min"
				:max="max"
				@input="onInput"
				@change="onChange"
			>
		</div>

		<!-- input direct form -->
		<div v-if="type === 'area'">
			<textarea class="py-3 px-4 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500
			focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 
			dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" rows="3" 
			:placeholder="placeholder"
			:class="theClass"
			:name="name"
			:data-test="dataTest"
			:value="value"
			:autocomplete="autocomplete"
			:disabled="isDisabled"
			:tabindex="tabIndex"
			@input="onInput"
			@change="onChange"
			></textarea>
		</div>
		<div v-if="type === 'text-input'">
			<input type="text" class="w-full" 
			:placeholder="placeholder"
			:class="theClass"
			:name="name"
			:data-test="dataTest"
			:value="value"
			:autocomplete="autocomplete"
			:disabled="isDisabled"
			:tabindex="tabIndex"
			@input="onInput"
			@change="onChange">
		</div>
		<div v-if="type === 'text-pre-input'" class="flex rounded-lg shadow-sm">
				<span class="font-bold px-4 inline-flex items-center min-w-fit rounded-s-md border border-r-0 border-gray-200 
				bg-transparent text-sm text-gray-300 dark:bg-neutral-700 dark:border-neutral-700 dark:text-neutral-400">{{ preText || "NOK." }}</span>
				<input type="number" class="py-2 px-3 block w-full border-l-0  border-gray-200 shadow-sm rounded-e-lg text-sm focus:z-10 
				focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 
				dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600"
				:placeholder="placeholder"
				:name="name"
				:data-test="dataTest"
				:value="value"
				:autocomplete="autocomplete"
				:disabled="isDisabled"
				:tabindex="tabIndex"
				:min="min"
				@input="onInput"
				@change="onChange">
		</div>
		<!-- password -->
		<div v-if="type === 'password'">
			<input
				class="w-full "
				:class="theClass"
				:data-test="dataTest"
				:type="passwordMode"
				:name="name"
				:value="value"
				:maxlength="maxlength"
				:placeholder="placeholder"
				:autocomplete="autocomplete"
				:tabindex="tabIndex"
				@input="onInput"
				@change="onChange"
			>
			<div
				class="absolute right-0 top-0 mt-3 mr-2"
				@click="togglePassword"
			>
				<EyeOffIcon
					v-if="passwordMode === 'password'"
					class="flex-shrink-0 ml-[-20px] mr-2 h-4 w-4 text-gray-400"
					aria-hidden="true"
				/>
				<EyeIcon
					v-if="passwordMode === 'text'"
					class="flex-shrink-0 ml-[-20px] mr-2 h-4 w-4 text-gray-400"
					aria-hidden="true"
				/>
			</div>
		</div>
		<template v-if="type === 'timer'">
			<scrubber-masked
				:class="theClass"
				:min="minTime"
				:max="maxTime"
				:steps="step"
				:style="customStyle"
				:value="value"
				:disabled="disabled"
				style="text-align: center"
				@blur="onBlur"
				@focus="onFocus"
				@input="onNumberChange"
			/>
		</template>
	</div>
</template>

<script>

import {
	EyeOffIcon,
	EyeIcon,
} from '@heroicons/vue/solid';
import ScrubberMasked from '@/components/form/ScrubberMasked.vue';

export default {
	components: {
		EyeOffIcon,
		EyeIcon,
		ScrubberMasked
	},
	props: {
		type: {
			type: String,
			default: () => 'text',
		},
		value: {
			type: String,
			default: () => '',
			required: true,
		},
		placeholder: {
			type: String,
			default: () => '',
		},
		autocomplete: {
			type: String,
			default: () => 'off',
		},
		isDisabled: {
			type: Boolean,
			default: () => false,
		},
		dataTest: {
			type: String,
			default: () => '',
		},
		customStyle: {
			type: String,
			default: () => '',
		},
		maxlength: {
			type: String,
			default: () => '',
		},
		max: {
			type: Number,
			default: () => 100000,
		},
		min: {
			type: Number,
			default: () => 0,
		},
		minTime: {
			type: Number,
			default: () => 0,
		},
		maxTime: {
			type: Number,
			default: () => 0,
		},
		uppercase: {
			type: Boolean,
			default: () => false,
		},
		preText: {
			type: String,
			default: () => '',
		},
		tabIndex: {
			type: Number,
			default: 1,
		}
	},
	data() {
		return {
			passwordMode: 'password',
			isFocus: false,
		};
	},
	computed: {
		name() {
			return this.value && typeof this.value === 'string' ? this.value.toLowerCase() : '';
		},
		theClass() {
			let myClass = `bg-white appearance-none block px-3 py-2 border border-gray-300 shadow-sm rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 text-sm`;
			if (this.uppercase) myClass = `uppercase appearance-none block px-3 py-2 border border-gray-300 shadow-sm  rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 text-sm`;
			if (this.isDisabled) myClass += ` text-[#999] bg-gray-100`;
			return myClass;
		},
		
	},
	watch: {},
	created() {},
	mounted() {},
	beforeUnmount() {},
	methods: {
		onInput(event) {
			// Can add validation here
			this.$emit('update:modelValue', event.target.value);
		},
		onChange(event) { // Supports .lazy
			// Can add validation here
			this.$emit('update:modelValue', event.target.value);
		},
		togglePassword() {
			this.passwordMode = this.passwordMode === 'password' ? 'text' : 'password';
		},
		onNumberChange(value) {
			if (!value.target) {
				this.$emit('update:modelValue', value);
			} else {
				this.$emit('input', value.target.value);
			}
		},
		onFocus() {
			this.isFocus = true;
		},
		onBlur() {
			this.isFocus = false;
			this.$emit('blur');
		},
	},
};
</script>