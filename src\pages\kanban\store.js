import {
  reactive,
  toRaw
} from "vue"
import {
  useWindowSize
} from "@vueuse/core"
const {
  width,
  height
} = useWindowSize()

const store = reactive({
  version: "1.0.00",
  activeAddTaskColumnIndex: null,
  wWidth: width,
  wHeight: height,
  data: null,
  hideAside: false,
  lightMode: true,
  isModal: false,
  mutate: false,
  project: null,
  board: {
    active: "", // name of the active board
    activeIndex: 0,
    add: false,
    edit: false,
    delete: false,
  },
  task: {
    id: null,
    title: '',
    projectName: '',
    completed: '',
    assign: '',
    creator: null,
    assignTo: '',
    startDate: '',
    dueDate: '',
    parentId: '',
    updatedAt: '',
    description: '',
    createdAt: '',
    status: '',
    index: '',
    type: '',
    subTask: [],
    collaborator: [],
    comments: [],
    active: "", // name of the active task (in case of edit)
    activeIndex: -1, // index of active task
    columnIndex: -1, // index of column of activeTask
    status: "", // name of the column (when add / edit task) to which the task belongs
    add: false,
    addDirectly: false,
    edit: false,
    delete: false,
    show: undefined, // holds the task object, in taskShow
  },

  setData: function (d) {
    this.data = toRaw(d.boards)
    if (JSON.stringify(this.data) != "{}") {
      this.board.active = this.data[0].title
      console.log('kia', this.board.active);
    }
  },

  setDataProject: function (project) {
    if (project) this.project = project
  },

  getActiveProject : function () {
    return this.project;
  },

  getActiveBoard: function () {
    if (this.data) {
      return this.data.find((el, index) => {
        if (el.title === this.board.active) {
          this.board.activeIndex = index
          return el
        }
      })
    }
  },
  getActiveColumn: function () {
    if (this.data) {
      return this.getActiveBoard().columns.find((el) => {
        if (el.title === this.task.type) {
          return el
        }
      })
    }
  },

  getActiveTask: function () {
    if (this.data) {
      return this.getActiveColumn().tasks.find((el, idx) => {
        if (el.id === this.task.active) {
          this.task.activeIndex = idx
          return el
        }
      })
    }
  },

  getBoardColsLength: function () {
    if (this.data && this.data.length > 0) {
      return this.getActiveBoard().columns.length
    }
    return 0
  },

  changeStatus: function (colIndex) {
    //  splice task from the old status column

    let task = this.data[this.board.activeIndex].columns[
      this.task.columnIndex
    ].tasks.splice(this.task.activeIndex, 1)

    // push it in the new status column
    this.data[this.board.activeIndex].columns[colIndex].tasks.push(task[0])
  },

  updateTaskInStore(task) {
    const column = this.getActiveColumn(task.type);
    if (column) {
        const taskIndex = column.tasks.findIndex(t => t.id === task.id);
        if (taskIndex !== -1) {
            column.tasks.splice(taskIndex, 1, task); // Update existing task
        } else {
            column.tasks.unshift(task); // Add new task at the beginning
        }
    }
  },

  // New method to reset store data
  resetStore: function () {
    this.data = {};
    this.hideAside = false;
    this.lightMode = true;
    this.isModal = false;
    this.mutate = false;
    this.project = null;
    this.board.active = "";
    this.board.activeIndex = 0;
    this.board.add = false;
    this.board.edit = false;
    this.board.delete = false;
    this.task = {
      id: null,
      title: '',
      projectName: '',
      assign: '',
      creator: null,
      assignTo: '',
      startDate: '',
      dueDate: '',
      parentId: '',
      updatedAt: '',
      description: '',
      createdAt: '',
      status: '',
      index: '',
      type: '',
      subTask: [],
      collaborator: [],
      comments: [],
      active: "",
      activeIndex: -1,
      columnIndex: -1,
      status: "",
      add: false,
      addDirectly: false,
      edit: false,
      delete: false,
      show: undefined,
    };
  },
})
export default store