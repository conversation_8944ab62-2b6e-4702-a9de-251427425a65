import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
})

// Helper functions for common operations
export const auth = {
  signUp: (email, password) => supabase.auth.signUp({ email, password }),
  signIn: (email, password) => supabase.auth.signInWithPassword({ email, password }),
  signOut: () => supabase.auth.signOut(),
  getUser: () => supabase.auth.getUser(),
  getSession: () => supabase.auth.getSession(),
  onAuthStateChange: (callback) => supabase.auth.onAuthStateChange(callback)
}

export const db = {
  // Notebooks
  getNotebooks: () => supabase.from('notebooks').select('*').order('updated_at', { ascending: false }),
  createNotebook: (notebook) => supabase.from('notebooks').insert([notebook]).select().single(),
  updateNotebook: (id, updates) => supabase.from('notebooks').update(updates).eq('id', id).select().single(),
  deleteNotebook: (id) => supabase.from('notebooks').delete().eq('id', id),
  
  // Sources
  getSources: (notebookId) => supabase.from('sources').select('*').eq('notebook_id', notebookId).order('created_at', { ascending: false }),
  createSource: (source) => supabase.from('sources').insert([source]).select().single(),
  updateSource: (id, updates) => supabase.from('sources').update(updates).eq('id', id).select().single(),
  deleteSource: (id) => supabase.from('sources').delete().eq('id', id),
  
  // Notes
  getNotes: (notebookId) => supabase.from('notes').select('*').eq('notebook_id', notebookId).order('updated_at', { ascending: false }),
  createNote: (note) => supabase.from('notes').insert([note]).select().single(),
  updateNote: (id, updates) => supabase.from('notes').update(updates).eq('id', id).select().single(),
  deleteNote: (id) => supabase.from('notes').delete().eq('id', id),
  
  // Chat messages
  getChatMessages: (notebookId) => supabase.from('chat_messages').select('*').eq('notebook_id', notebookId).order('created_at', { ascending: true }),
  createChatMessage: (message) => supabase.from('chat_messages').insert([message]).select().single(),
  deleteChatMessages: (notebookId) => supabase.from('chat_messages').delete().eq('notebook_id', notebookId)
}

export default supabase
