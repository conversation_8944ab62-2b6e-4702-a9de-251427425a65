<template>
  <div>
    <!-- Header with controls -->
    <div class="flex items-center justify-between mb-6">
      <div class="flex items-center space-x-4">
        <h2 class="text-lg font-medium text-gray-900">Your notebooks</h2>
        <span class="text-sm text-gray-500">{{ notebooks.length }} notebook{{ notebooks.length !== 1 ? 's' : '' }}</span>
      </div>
      
      <div class="flex items-center space-x-4">
        <!-- Sort Dropdown -->
        <!-- <div class="relative" ref="sortDropdown">
          <button 
            @click="toggleSortDropdown"
            class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            {{ sortBy }}
            <svg class="ml-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>
          
          <div 
            v-if="showSortDropdown"
            class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-50"
          >
            <div class="py-1">
              <button 
                v-for="option in sortOptions"
                :key="option"
                @click="setSortBy(option)"
                class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center justify-between"
              >
                {{ option }}
                <svg v-if="sortBy === option" class="h-4 w-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </div> -->
        
        <!-- View Toggle -->
        <!-- <div class="flex items-center border border-gray-300 rounded-md">
          <button 
            @click="viewMode = 'grid'"
            :class="[
              'p-2 text-sm',
              viewMode === 'grid' ? 'bg-gray-100 text-gray-900' : 'text-gray-500 hover:text-gray-700'
            ]"
          >
            <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
              <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
            </svg>
          </button>
          <button 
            @click="viewMode = 'list'"
            :class="[
              'p-2 text-sm',
              viewMode === 'list' ? 'bg-gray-100 text-gray-900' : 'text-gray-500 hover:text-gray-700'
            ]"
          >
            <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
            </svg>
          </button>
        </div> -->
        
        <!-- Create Button -->
        <t-button 
          :color="'primary-solid'" 
          @click="$emit('create-notebook')" 
          :disabled="isCreating"
          class="inline-flex items-center gap-2"
        >
          <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
          {{ isCreating ? 'Creating...' : 'New notebook' }}
        </t-button>
      </div>
    </div>

    <!-- Notebooks Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <NotebookCard 
        v-for="notebook in sortedNotebooks" 
        :key="notebook.id"
        :notebook="formatNotebook(notebook)"
        @click="handleNotebookClick(notebook.id)"
        @delete="handleDeleteNotebook"
      />
    </div>
  </div>
</template>

<script>
import NotebookCard from './NotebookCard.vue';
import TButton from '@/components/global/Button.vue';

export default {
  name: 'NotebookGrid',
  components: {
    NotebookCard,
    TButton,
  },
  props: {
    notebooks: {
      type: Array,
      default: () => []
    },
    isCreating: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      viewMode: 'grid',
      sortBy: 'Most recent',
      sortOptions: ['Most recent', 'Title'],
      showSortDropdown: false
    };
  },
  computed: {
    sortedNotebooks() {
      const sorted = [...this.notebooks];
      
      if (this.sortBy === 'Most recent') {
        return sorted.sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime());
      } else if (this.sortBy === 'Title') {
        return sorted.sort((a, b) => a.title.localeCompare(b.title));
      }
      
      return sorted;
    }
  },
  mounted() {
    document.addEventListener('click', this.handleClickOutside);
  },
  beforeUnmount() {
    document.removeEventListener('click', this.handleClickOutside);
  },
  methods: {
    toggleSortDropdown() {
      this.showSortDropdown = !this.showSortDropdown;
    },
    
    setSortBy(option) {
      this.sortBy = option;
      this.showSortDropdown = false;
    },
    
    handleClickOutside(event) {
      if (this.$refs.sortDropdown && !this.$refs.sortDropdown.contains(event.target)) {
        this.showSortDropdown = false;
      }
    },
    
    formatNotebook(notebook) {
      return {
        id: notebook.id,
        title: notebook.title,
        date: new Date(notebook.updated_at).toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
          year: 'numeric'
        }),
        sources: notebook.sources?.[0]?.count || 0,
        icon: notebook.icon || '📝',
        color: notebook.color || 'bg-gray-100'
      };
    },
    
    handleNotebookClick(notebookId) {
      this.$emit('open-notebook', notebookId);
    },
    
    handleDeleteNotebook(notebookId) {
      // Emit delete event to parent
      this.$emit('delete-notebook', notebookId);
    }
  }
};
</script>
