export default ({
	store,
	next,
	from,
	to
}) => {
	const { path, name, query } = to;

	// Handling 'mode' query parameter
	if (query.hasOwnProperty("mode") && query.mode === "preview") {
		const token = query.token || "";
		const domain = query.domain || "";

		// Dispatch preview mode action if needed
		store.dispatch("app/preflightPreview", {
			token,
			domain
		});

		// If the current route isn't already the login page, redirect to it
		if (path !== "/login") {
			return next({
				path: "/login",
				query: {
					mode: "preview",
					token,
					domain
				},
				replace: true
			});
		}
	}

	// Handling 'token' query parameter
	if (query.hasOwnProperty("token")) {
		const token = query.token;

		// Set the token in the store
		store.dispatch("auth/setToken", token);

		// Remove the 'token' query from the route and replace it to avoid further triggering
		const updatedQuery = {
			...query
		};
		delete updatedQuery.token;

		return next({
			path,
			query: updatedQuery,
			replace: true
		});
	}

	// Check for session
	const hasSession = store.getters["auth/hasSession"];
	const currentUser = store.getters["auth/user"] || {};

	// Allowed paths without login
	const allowedPaths = [
		"/login", "/logout", "/forgot-password", "/password/reset", "/register",
		"/magic-link", "/settings/change-password", "/auto_login", "/user/verify",
		"/confirmation-email", "/resend-email", "/embed", "/twitter", "/shareable_form",
		"/not-found", "/reset", "/templates/preview", '/request-invite', '/assistant'
	];

	const allowedPathNames = ["BiteEditor", "DownloadInvoice"];
	const clientPathNames = ["EventQuotation"];
	const adminOnlyPath = [
		'Client',
		'Users',
		'UserRole',
		'Category',
		'UserJob',
		'UserExpertise',
		'Service',
		'Product',
		'Offer',
	]

	if (adminOnlyPath.includes(name) && currentUser?.role?.id !== 1) {
		return next("/");
	}

	// Handle specific client redirection based on role
	if (clientPathNames.includes(name) && currentUser?.role?.id === 6) {
		const pathArray = path.split("/");
		const id = pathArray[pathArray.length - 1];

		if (name !== "EventQuotationClient") {
			return next({
				name: "EventQuotationClient",
				params: {
					id
				},
				replace: true,
			});
		}
	}

	// Allow access if the path or name is allowed without login
	if (allowedPaths.includes(path) || allowedPathNames.includes(name)) {
		return next();
	}

	// Redirect to login if no session and accessing restricted path
	if (!hasSession) {
		if (path === "/") {
			return next("/login");
		} else if (!allowedPaths.includes(path)) {
			return next({
				path: "/login",
				query: {
					redirect: to.fullPath
				}, // Save the intended path for redirection after login
				replace: true,
			});
		}
	}

	return next();
};