<template>
    <div class="mx-auto p-6 bg-white rounded-lg ">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6">
            <div>
                <h2 class="text-xl font-semibold text-gray-800">My Expertise</h2>
                <p class="text-sm text-gray-500">
                    Add and rank your expertise based on your proficiency and task preference.
                </p>
            </div>
            <button @click="openDrawer"
                class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                Add Expertise
            </button>
        </div>

        <!-- Expertise List with Drag and Drop -->
        <draggable v-if="expertises?.length" v-model="expertises" @end="updateExpertiseOrder" class="space-y-4 max-w-4xl">
            <template #item="{ element, index }">
                <div class="flex justify-between items-center bg-gray-100 px-4 py-3 rounded-lg shadow-sm">
                    <!-- Drag Handle -->
                    <div class="flex items-center space-x-2 cursor-move">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 18M6 12L18 12M6 6L18 6" />
                        </svg>
                        <p class="text-gray-800">{{ element.expertise.name }} - {{ element.level }}</p>
                    </div>

                    <!-- Delete Button -->
                    <button @click="removeExpertise(index)" class="text-gray-500 hover:text-gray-700 focus:outline-none">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6L18 18" />
                        </svg>
                    </button>
                </div>
            </template>
        </draggable>

        <!-- Empty State Message -->
        <div v-else class="flex items-center text-gray-600">
            <InformationCircleIcon class="w-8 mr-2"/>  <span class="text-gray-400">Start adding your service to receive assignments. </span>
        </div>
    </div>

    <DrawerRight :id="`drawer-right`" ref="drawerRight">
        <!-- Header -->
        <template #header>
            <div class="flex mt-4 pb-2">
                
                <p id="hs-pro-dutoo-label" class="text-sm font-semibold text-gray-800 ml-2">
                    Add Service
                </p>
            </div>
        </template>

        <!-- Body -->
        <template #body>
            <div>
                <div class="p-4">
                    <!-- Assigned Expertise -->
                    <div class="mb-4">
                        <label class="block text-sm font-semibold text-gray-700 mb-1">Assigned Expertise</label>
                        <div class="relative">
                            <select v-model="selectedExpertise"
                                class="text-sm block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                :tabIndex="1">
                                <option value="" disabled>Select Expertise</option>
                                <option v-for="(exp, index) in items" :key="exp.id" :value="exp.id">
                                    {{ exp.name }}
                                </option>
                            </select>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Only visible to selected job title</p>
                        <span v-if="errors.selectedExpertise" class="text-red-500 text-sm">{{ errors.selectedExpertise }}</span>
                    </div>

                    <!-- Expertise Level -->
                    <div class="mb-4">
                        <label class="block text-sm font-semibold text-gray-700 mb-1">Expertise Level</label>
                        <div class="relative">
                            <select v-model="selectedExpertiseLevel"
                                class="text-sm block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                :tabIndex="2">
                                <option value="" disabled>Select Level</option>
                                <option value="Basic">Basic</option>
                                <option value="Intermediate">Intermediate</option>
                                <option value="Advanced">Advanced</option>
                            </select>
                        </div>
                        <span v-if="errors.selectedExpertiseLevel" class="text-red-500 text-sm">{{ errors.selectedExpertiseLevel }}</span>
                    </div>
                </div>
            </div>
        </template>

        <!-- Footer -->
        <template #footer>
            <div class="p-5 border-t border-gray-200">
                <div class="flex items-center gap-x-2 justify-end">
                    <t-button :color="`secondary-solid`" data-hs-overlay="#drawer-right" tabindex="3" @click="closeDrawer">
                        Cancel
                    </t-button>
                    <t-button :color="`primary-solid`" @click="save" data-hs-overlay="#drawer-right" tabindex="4">
                        Save
                    </t-button>
                </div>
            </div>
        </template>
    </DrawerRight>

</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import draggable from 'vuedraggable';
import DrawerRight from "@/components/form/DrawerRight.vue";
import expertiseApi from '@/api/expertise';
import userApi from '@/api/user';
import { UserCircleIcon } from "@heroicons/vue/solid";
import TButton from '@/components/global/Button.vue';
import { InformationCircleIcon } from '@heroicons/vue/outline';

export default {
    components: {
        draggable,
        DrawerRight,
        UserCircleIcon,
        TButton,
        InformationCircleIcon
    },
    data() {
        return {
            orderBy: 'name',
            sortBy: 'asc',
            page: 1,
            total: 0,
            maxPage: 1,
            limit: 100,
            items: [],
            expertises: [],
            selectedExpertise: "",
            selectedExpertiseLevel: "",
            errors: {
                selectedExpertise: null,
                selectedExpertiseLevel: null,
            },

        };
    },
    computed: {
        ...mapGetters({
            user: 'auth/user'
        })
    },
    created() {
        this.getAllExpertise();
    },
    methods: {
        ...mapActions({
            fetchUser: 'auth/fetchUser',
            setUser: 'auth/setUser'
        }),
        validateForm() {
            let isValid = true;

            // Validate Expertise
            if (!this.selectedExpertise) {
                this.errors.selectedExpertise = 'Please select an expertise.';
                isValid = false;
            } else {
                this.errors.selectedExpertise = null;
            }

            // Validate Expertise Level
            if (!this.selectedExpertiseLevel) {
                this.errors.selectedExpertiseLevel = 'Please select an expertise level.';
                isValid = false;
            } else {
                this.errors.selectedExpertiseLevel = null;
            }

                return isValid;
            },
        openDrawer() {
            this.$refs.drawerRight.visibleRight = true;
        },
        closeDrawer() {
            this.$refs.drawerRight.visibleRight = false;
        },
        save() {
            if (this.validateForm()) {
                this.addUserExpertise();
            }
        },
        addUserExpertise() {
            const params = {
                userId: this.user.id,
                expertiseId: this.selectedExpertise,
                level: this.selectedExpertiseLevel,
            };
            userApi.addUserExpertise(params, (response) => {
                this.expertises = response.data.expertiseUsers;
                this.__showNotif("success", "Success", "Expertise added successfully.");
            }, (err) => {
                const message = err?.response?.data?.message;
                this.__showNotif('error', 'Error', message);
            });
        },
        getAllExpertise() {
            const params = {
                orderBy: "name",
                sortBy: "asc",
                page: this.page,
                limit: this.limit,
            };
            expertiseApi.getList(params, (response) => {
                this.items = response.data;
                this.fetchUser();
                setTimeout(() => {
                    this.expertises = this.user.expertiseUsers;
                }, 100);
            }, (err) => {
                const message = err?.response?.data?.message;
                this.__showNotif('error', 'Error', message);
            });
        },
        removeExpertise(index) {
            const id = this.expertises[index].id;
            userApi.removeUserExpertise(id, () => {
                this.expertises.splice(index, 1);
                this.__showNotif("success", "Success", "Expertise removed successfully.");
            }, (err) => {
                const message = err?.response?.data?.message;
                this.__showNotif('error', 'Error', message);
            });
        },
        updateExpertiseOrder() {
            // Logic to update the expertise order after dragging
            const reorderedExpertise = this.expertises.map(e => e.id);

            const params = { ids: reorderedExpertise };

            userApi.updateExpertiseOrder(params, (response) => {
                const data = response.data;
                this.setUser(data)
                this.__showNotif("success", "Success", "Expertise order updated successfully.");
            }, (err) => {
                const message = err?.response?.data?.message;
                this.__showNotif('error', 'Error', message);
                console.error("Error updating expertise order:", err);
            });
        }
    }
};
</script>

<style scoped>
/* Customize styling if necessary */
</style>
