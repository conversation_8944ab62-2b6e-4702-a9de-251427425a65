import createPersistedState from "vuex-persistedstate";
import { client } from '@/libraries/http-client';
import authApi from '../../api/auth';

const TOKEN_KEY = 'access_token';

export default {
	namespaced: true,
	plugins: [createPersistedState()],
	state: () => ({
		user: null,
		token: "",
		expiresAt: null,
		isFetchingUser: false,
		role: '',
		isShowGallery: false,
		isShowOnboard: '0',
		isAdmin: '',
		isClient: '',
		isFreelancer: '',
		isUser: '',
	}),
	mutations: {
		setUser(state, user) {
			state.user = user;
			state.isAdmin = user?.role?.id === 1;
			state.isClient = user?.role?.id === 6;
			state.isFreelancer = user?.role?.id === 3;
			state.isUser = user?.role?.id !== 1  && user?.role?.id !== 6;
		},
		setToken(state, token) {
			state.token = token;

			// Put access token to client header
			localStorage.setItem(TOKEN_KEY, token);
			client.defaults.headers.Authorization = `Bearer ${token}`;
		},
		setExpiresAt(state, expiresAt) {
			state.expiresAt = expiresAt;
		},
		fetchUserStart(state) {
			state.isFetchingUser = true;
		},
		fetchUserEnd(state) {
			state.isFetchingUser = false;
		},
		setRole(state, role) {
			localStorage.setItem('role', role);
			state.role = role;
		},
		setIsShowGallery(state, value) {
			state.isShowGallery = value;
		},
		setIsShowOnboard(state, value) {
			state.isShowOnboard = value;
		}
	},
	actions: {
		toggleMenuGallery({ commit }, value) {
			commit("setIsShowGallery", value);
		},
		setIsShowOnboard({ commit }, value) {
			commit("setIsShowOnboard", value);
		},
		clearAuth({ commit }) {
			commit("setUser", null);
			commit("setToken", "");
			localStorage.removeItem(TOKEN_KEY);
		},
		setSession({ commit }, data) {
			if (data.token) commit("setToken", data.token);
			if (data.user) commit("setUser", data.user);         
			if (data.expires_at) commit("setExpiresAt", data.expires_at);         
		},
		setToken({ commit }, token) {
			commit("setToken", token);
		},
		setExpiresAt({ commit }, expires_at) {
			commit("setExpiresAt", expires_at);
		},
		setUser({ commit }, user) {
			commit("setUser", user);
		},
		fetchUser({ commit }) {
			if (this.isFetchingUser) {
				return;
			}
			commit("fetchUserStart");
			const callback = function (response) {
				const user = response.data;
				commit("fetchUserEnd");
				commit("setUser", user);
			};
			const errorCallback = function (e) {
				console.log(e)
				commit("fetchUserEnd");
			};
			authApi.getProfile(callback, errorCallback);
		},
	},
	getters: {
		hasSession(state) {
			return state.token !== '';
		},
		getToken(state) {
			return state.token;
		},
		isFetchingUser(state) {
			return state.isFetchingUser;   
		},
		user(state) {
			return state.user;   
		},
		role(state) {
			return state.role;   
		},
		isAdmin(state) {
			return state.isAdmin;   
		},
		isClient(state) {
			return state.isClient;
		},
		isFreelancer(state) {
			return state.isFreelancer;
		},
		isUser(state) {
			return state.isUser;
		},
		getIsShowGallery(state) {
			return state.isShowGallery;
		},
		getIsShowOnboard(state) {
			return state.isShowOnboard;
		}
	},
};