<template>
  <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-6 sm:py-12 px-4 sm:px-6 lg:px-8">
    <!-- Logo -->
    <div class="flex justify-center mb-8 sm:mb-12">
      <div class="flex items-center">
        <div class="w-8 h-8 sm:w-10 sm:h-10 bg-primary-600 rounded-full flex items-center justify-center mr-3">
          <div class="w-4 h-4 sm:w-5 sm:h-5 bg-white rounded-full relative">
            <div class="absolute inset-1 bg-primary-600 rounded-full"></div>
            <div class="absolute top-0.5 left-0.5 w-1 h-1 bg-white rounded-full"></div>
            <div class="absolute top-1 right-0.5 w-0.5 h-0.5 bg-white rounded-full"></div>
            <div class="absolute bottom-0.5 left-1 w-0.5 h-0.5 bg-white rounded-full"></div>
          </div>
        </div>
        <span class="text-2xl sm:text-3xl font-normal text-gray-800">desidia</span>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-md mx-auto w-full">
      <!-- Title -->
      <h1 class="text-2xl sm:text-3xl font-bold text-gray-900 text-center mb-8 sm:mb-12">
        Request Invite
      </h1>

      <!-- Form -->
      <div class="space-y-6">
        <!-- Email Field -->
        <div>
          <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
            Email
          </label>
          <input
            id="email"
            v-model="email"
            type="email"
            autocomplete="email"
            required
            class="w-full px-3 py-3 border border-gray-300 rounded-lg text-base placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            placeholder="<EMAIL>"
          />
          <span v-if="!isValidEmailAddress && email && email.length !== 0" class="text-red-500 text-sm mt-1 block">
            Invalid Email Address
          </span>
        </div>

        <!-- Message Field -->
        <div>
          <label for="message" class="block text-sm font-medium text-gray-700 mb-2">
            Message
          </label>
          <textarea
            id="message"
            v-model="message"
            rows="4"
            class="w-full px-3 py-3 border border-gray-300 rounded-lg text-base placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 resize-none"
            placeholder="Inform us about yourself and the unit you are currently living"
          ></textarea>
        </div>

        <!-- Request Invite Button -->
        <button
          type="submit"
          :disabled="!isFormValid || isSubmitting"
          :class="[
            'w-full py-3 px-4 text-base font-medium rounded-lg transition-colors duration-200',
            isFormValid && !isSubmitting
              ? 'bg-primary-600 hover:bg-primary-700 text-white'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          ]"
          @click="requestInvite"
        >
          <span v-if="isSubmitting" class="flex items-center justify-center">
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Sending...
          </span>
          <span v-else>Request Invite</span>
        </button>

        <!-- Bottom Link -->
        <div class="text-center">
          <router-link
            to="/login"
            class="text-gray-600 hover:text-gray-800 transition-colors duration-200 text-sm"
          >
            Return to Login
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { isValidEmail } from '@/libraries/helper';

export default {
  name: 'RequestInvite',
  data() {
    return {
      email: '',
      message: '',
      isSubmitting: false,
    };
  },
  computed: {
    isValidEmailAddress() {
      return isValidEmail(this.email);
    },
    isFormValid() {
      return this.isValidEmailAddress && this.message.trim().length > 0;
    },
  },
  methods: {
    requestInvite() {
      if (!this.isFormValid || this.isSubmitting) return;
      
      this.isSubmitting = true;
      
      // Simulate API call - replace with actual API endpoint
      setTimeout(() => {
        this.__showNotif('success', 'Success', 'Your invite request has been submitted!');
        this.isSubmitting = false;
        this.email = '';
        this.message = '';
      }, 1500);
    },
  },
};
</script>
