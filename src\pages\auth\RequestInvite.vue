<template>
  <div class="min-h-screen bg-gray-50 flex flex-col justify-around sm:justify-center py-6 px-[40px] sm:py-12 px-4 sm:px-6 lg:px-8">
    <!-- Logo -->
    <div class="sm:max-w-sm sm:mx-auto flex justify-start mb-8 sm:mb-16">
      <img class="h-8 mb-10 sm:h-16 w-auto" src="@/assets/images/logo_desidia.png" alt="desidia">
    </div>

    <!-- Main Content -->
    <div class="min-h-[384px] sm:min-h-[508px] flex flex-col justify-end sm:justify-center max-w-md mx-auto w-full ">
      <div class="sm:bg-white rounded-md  sm:p-12">
        <!-- Title -->
        <h1 class="text-2xl sm:text-3xl font-bold text-gray-900 text-left mb-2 sm:mb-12">
          Request Invite
        </h1>

        <!-- Form -->
        <div class="space-y-6">
          <!-- Email Field -->
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
              Email
            </label>
            <t-input
              v-model="email"
              :dataTest="'email'"
              :type="'email'"
              :value="email"
              :placeholder="'<EMAIL>'"
              class="w-full"
            />
            <span v-if="!isValidEmailAddress && email && email.length !== 0" class="text-red-500 text-sm mt-1 block">
              Invalid Email Address
            </span>
          </div>

          <!-- Message Field -->
          <div>
            <label for="message" class="block text-sm font-medium text-gray-700 mb-2">
              Message
            </label>
            <t-textarea
              v-model="message"
              :dataTest="'message'"
              :type="'textarea'"
              :value="message"
              :placeholder="'Inform us about yourself and the unit you are currently living'"
              :rows="4"
              class="w-full"
            />
          </div>

          <!-- Request Invite Button -->
          <t-button
            :type="'button'"
            :color="'primary-solid'"
            class="w-full mt-6"
            :isLoading="isSubmitting"
            :isDisabled="isSubmitting || !isFormValid"
            @click="requestInvite"
          >
            Request Invite
          </t-button>

          <!-- Bottom Link -->
          <div class="text-left mt-6">
            <router-link
              to="/login"
              class="text-gray-600 hover:text-gray-800 transition-colors duration-200 text-sm"
            >
              Return to Login
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { isValidEmail } from '@/libraries/helper';
import TTextarea from '@/components/form/Textarea.vue';

export default {
  name: 'RequestInvite',
  components: {
    TTextarea,
  },
  data() {
    return {
      email: '',
      message: '',
      isSubmitting: false,
    };
  },
  computed: {
    isValidEmailAddress() {
      return isValidEmail(this.email);
    },
    isFormValid() {
      return this.isValidEmailAddress && this.message.trim().length > 0;
    },
  },
  methods: {
    requestInvite() {
      if (!this.isFormValid || this.isSubmitting) return;
      
      this.isSubmitting = true;
      
      // Simulate API call - replace with actual API endpoint
      setTimeout(() => {
        this.__showNotif('success', 'Success', 'Your invite request has been submitted!');
        this.isSubmitting = false;
        this.email = '';
        this.message = '';
      }, 1500);
    },
  },
};
</script>
