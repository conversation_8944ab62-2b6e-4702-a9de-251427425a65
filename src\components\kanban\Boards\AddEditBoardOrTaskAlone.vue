<template>
  <section class="max-h-[100vh] max-w-[660px] lg:min-w-[668px] bg-white dark:bg-kb_dark_grey rounded-md relative flex flex-col h-screen overflow-x-hidden" :class="{'lg:w-[660px] lg:mx-auto pb-8 lg:border-[1px]': isFull}">
    <div class="h-screen" v-if="isFetching">
      <loader-circle v-if="isFetching" />
    </div>
    <!-- head -->
    <div v-if="!isFetching"  class="z-10 fixed top-0 left-0 right-0 w-full flex flex-row justify-between items-center px-4 py-3 border-b-2 flex-shrink-0" :class="{'lg:w-[660px] lg:mx-auto': isFull}">
      <div class="flex items-center">
        <CheckCircleIcon @click.stop="updateComplete(task || workData)" class="h-6 w-6 pointer" aria-hidden="true"
        :class="{'text-green-600': task?.status === 'completed' || workData?.status === 'completed'}"></CheckCircleIcon>
        <div class="ml-2 font-medium">Mark Complete</div>
      </div>
      <div class="flex">
        <Menu v-if="isAdmin" as="div" class="relative mr-4">
          <div>
            <MenuButton>
              <DotsHorizontalIcon class="h-6 w-6 ml-2 pointer" aria-hidden="true"></DotsHorizontalIcon>
            </MenuButton>
          </div>
          <transition enterActiveClass="transition ease-out duration-100" enterFromClass="transform opacity-0 scale-95"
            enterToClass="transform opacity-100 scale-100" leaveActiveClass="transition ease-in duration-75"
            leaveFromClass="transform opacity-100 scale-100" leaveToClass="transform opacity-0 scale-95">
            <MenuItems
              class="pointer hover:bg-gray-200 w-[150px] p-4 absolute right-0 top-[10px] mt-2 mr-8 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none">
              <MenuItem
                @click="deletetask(task)"
                v-slot="{ active }"
                
              >
                <div class="">{{ $t('Delete') }}</div>
              </MenuItem>
            </MenuItems>
          </transition>
        </Menu>
        <LinkIcon @click="copyURL()" class="h-6 w-6 ml-2 pointer" aria-hidden="true"></LinkIcon>
        <ExternalLinkIcon @click="toggleRouteFullscreen" class="h-6 w-6 ml-2 pointer" aria-hidden="true"></ExternalLinkIcon>
        <!-- Close button -->
        <XIcon @click="close" class="h-6 w-6 ml-2 pointer" aria-hidden="true"></XIcon>
      </div>
    </div>
    <div class="flex items-center justify-end flex-row px-4 py-2 border-b-2" v-else>
      <XIcon @click="close" class="h-6 w-6 m-2 pointer" aria-hidden="true"></XIcon>
    </div>
    <!-- end head -->

    <!-- content middle -->
    <div ref="scrollContainerContent" v-if="!isFetching" class="mt-[54px] mb-4 flex-grow">
      <div class="">
        <!-- breadcumbs  -->
        <div v-if="workData?.parentId" class="mx-4 pt-2 pointer">
          <div class="flex items-center text-blue-600" @click="goBackParent()">
            <ChevronLeftIcon class="h-4 w-4  pointer mt-[2px]" aria-hidden="true"></ChevronLeftIcon>
            {{ workData?.parent?.name }}
          </div>
        </div>
        <!-- Name / Title -->
        <kbInput ref="inputTitle" controlType="titleTask" @blur="updateTask()" v-model="workData.name" :ph="ph" class="z-1 mx-5">
        </kbInput>
        <!-- end name -->
        <!-- project info -->
        <div class="flex justify-between pb-2 border-b-2">
          <div class="mt-2 text-gray-400 text-xs mx-5  truncate lg:max-w-[350px] max-w-[180px]">{{ $t('Project') }} <span class="font-bold text-gray-700 ml-2">{{ getActiveProject?.name || task.projectName }}</span></div>
          <statusSelect :_mode="statusMode" v-if="mode === 'task' && !this.workData.parentId && !isSocketHit"></statusSelect>
          <statusSelect :_mode="statusMode" v-if="mode === 'task' && !this.workData.parentId && isSocketHit"></statusSelect>
          <div class="mr-5" v-if="this.workData.parentId && !isSocketHit">{{ workData?.type }}</div>
        </div>

        <!-- assignee / due date -->
        <div class="flex justify-between mt-1">
          <div class="mt-2 text-gray-400 text-xs mx-5">{{ $t('Assignee') }}</div>
          <div class="mt-2 text-gray-400 text-xs mx-5">{{ $t('Due date') }}</div>
      </div>
      <!-- assignee / due date -->
      <div class="grid grid-cols-12 items-center gap-4">
        <!-- Assignee Component -->
        <div class="col-span-10">
          <Assignee 
            v-if="!isSocketHit" 
            @select="assigneeSelected" 
            @remove="assigneeRemoved" 
            :currentAssignee="workData.assign" 
            :users="users">
          </Assignee>
          <Assignee 
            v-if="isSocketHit" 
            @select="assigneeSelected" 
            @remove="assigneeRemoved" 
            :currentAssignee="workData.assign" 
            :users="users">
          </Assignee>
        </div>

        <!-- DatePickerTaskEdit Component -->
        <div class="col-span-2 flex justify-end mr-2">
          <div class="">
            <DatePickerTaskEdit 
              v-if="!isSocketHit" 
              :currentDate="workData.dueDate" 
              @updateDatepicker="updateDatepicker" 
              @select="datePickerSelect">
            </DatePickerTaskEdit>
            <DatePickerTaskEdit 
              v-if="isSocketHit" 
              :currentDate="workData.dueDate" 
              @updateDatepicker="updateDatepicker" 
              @select="datePickerSelect">
            </DatePickerTaskEdit>
          </div>
        </div>
      </div>


        <!-- in case of task: description -->
        <div>
          <CommentBox  class="mb-4 mx-5 mt-6" :description="workData.description" @update:description="updateTextCKEditor" :ph="'Task description'"></CommentBox>
        </div>

        <!-- attachment -->
        <div :class="{'h-[80px] max-w-[660px]': workData.attachments.length}" class="mx-5 overflow-y-auto mb-4">
          <div class="lg:inline-flex" v-for="file in workData.attachments">
            <div class="border-[1px] border-gray-300 p-2 flex items-center rounded-md w-[240px] h-[46px] lg:mr-2 mb-2 justify-between">
              <PaperClipIcon class="h-6 w-6 mr-2 text-gray-200" aria-hidden="true"></PaperClipIcon>
              <div>
                <div class="text-[12px] w-[150px] truncate">{{ getFileName(file.fileUrl) }}</div>
                <div class="text-[12px] w-[150px] truncate text-gray-400">{{ getFileExtension(file.fileUrl) }}</div>
              </div>
              <DownloadIcon class="h-6 w-6 mr-2 pointer" @click="downloadFile(file.fileUrl)" aria-hidden="true"></DownloadIcon>
            </div>
          </div>
        </div>

        <!-- subtask button -->
        <ListColumnsOrSubtasks v-if="!isSocketHit"  :users="users" @openTask="openTask" :parentId="task?.id || workData.id" :projectId="project?.id" ref="col_sub" :data="workData" :mode="mode" />
        <ListColumnsOrSubtasks v-else :users="users" @openTask="openTask" :parentId="task?.id" :projectId="project?.id" ref="col_sub" :data="workData" :mode="mode" />

        <!-- activity + Comments -->
        <Activity v-if="!isFetching" class="mt-6 relative" ref="activity" :workDataSubtask="workData" :mode="mode" />
      </div>
    </div>
    <!-- collaborator and -->
    <div class="min-h-[120px] ml-2 px-3 w-full flex-shrink-0">
      <!-- comments -->
        <div>
          <div class="text-xs text-gray-500 " >{{ $t('Comment') }}</div>
          <kbInput ref="inputComment" v-model="comment" controlType="textarea2"
            class="w-full pr-4"></kbInput>
        </div>
        <div class="my-2 flex justify-between relative items-center mt-6">
          <Collaborator class="z-99" @select="assigneeSelectedCollab" v-if="!isFetching" @remove="assigneeRemovedCollab" :currentCollab="workData?.collaborator" :users="users"></Collaborator>
            <div class="flex items-center absolute right-4">
                <t-button :isLoading="isUploadingFile" :loadingStyle="'mr-0 ml-[2px] text-primary-600'" :color="`primary-white`" class="px-1 md:px-2 py-[6px] mr-2 z-1 relative cursor-pointer">
                  <label v-if="!isUploadingFile" for="fileUploadInput">
                    <PaperClipIcon class="h-6 w-6 pointer" aria-hidden="true"></PaperClipIcon>
                    <input 
                    id="fileUploadInput" 
                    ref="file" 
                    accept="files" 
                    name="fileUploadInput" 
                    type="file" 
                    class="sr-only"
                    @change="upload"
                  >
                  </label>
                </t-button>
                


              <t-button
                :type="'submit'"
                :color="`primary-solid`"
                :isLoading="isCommenting"
                :isDisabled="!comment"
                @click="createComments()"
              >
                {{ $t('Comments') }}
              </t-button>
            </div>
        </div>
      </div>
  </section>
</template>


<script>

import {
  mapGetters,
  mapActions
} from 'vuex';
  import {
    CheckCircleIcon
  } from '@heroicons/vue/solid';
  import {
    ExternalLinkIcon,
    XIcon,
    LinkIcon,
    DotsHorizontalIcon,
    ThumbUpIcon,
    CalendarIcon,
    PaperClipIcon,
    DownloadIcon,
    DocumentTextIcon,
    ChevronLeftIcon
  } from '@heroicons/vue/outline';
import { ref, toRaw, onMounted } from 'vue';
import { onKeyStroke } from '@vueuse/core';
import statusSelect  from "@/components/kanban/StatusSelect.vue";
import Loader  from "@/components/loader/LoaderCircle.vue";
import store from '../../../pages/kanban/store.js';
import kbInput  from "@/components/kanban/kbInput.vue";
import TButton from '@/components/global/Button.vue';
import ListColumnsOrSubtasks  from "@/components/kanban/Boards/ListColumnsOrSubtasks.vue";
import Activity  from "@/components/kanban/Boards/Activity.vue";
import Assignee  from "@/components/kanban/Assignee.vue";
import Collaborator  from "@/components/kanban/Collaborator.vue";
import DatePickerTaskEdit  from "@/components/kanban/DatePickerTaskEdit.vue";
import userApi from "@/api/user";
import tasksApi from "@/api/tasks";
import fileApi from '@/api/files';
import attachmentApi from '@/api/attachment';
import kanbanApi from "@/api/kanban";
import projectApi from "@/api/project";
import CommentBox  from "@/components/kanban/CommentBox.vue";



export default {
  name: 'YourComponentName',
  components: {
    PaperClipIcon,
    Loader,
    kbInput,
    statusSelect,
    ExternalLinkIcon,
    XIcon,
    LinkIcon,
    ThumbUpIcon,
    DotsHorizontalIcon,
    CheckCircleIcon,
    CalendarIcon,
    TButton,
    ListColumnsOrSubtasks,
    Activity,
    Assignee,
    DatePickerTaskEdit,
    Collaborator,
    DocumentTextIcon,
    DownloadIcon,
    ChevronLeftIcon,
    CommentBox
  },
  data() {
    return {
      isUploading: false,
      workData: { title: '', description: '', status: '', subTask: [], columns: [] },
      vmTitle: '',
      vmDescription: '',
      validation: false,
      col_sub: null,
      head: '',
      ph: '',
      mode: '',
      statusCaption: '',
      statusMode: '',
      users: [],
      isFetching: false,
      isStatusShow: true,
      comment: null,
      isSocketHit: false,
      isFull: false,
      // upload
      fileUrl: null,
      isUploadingFile: false,
      kanbanColumns: [],
      dataKanban: {
        "boards": [{
            "title": "Kanban Desidia",
            "columns": [
              { "title": "Back-log", "tasks": [] },
              { "title": "Pre Production", "tasks": [] },
              { "title": "Production", "tasks": [] },
              { "title": "Post Production", "tasks": [] },
              { "title": "Revision", "tasks": [] },
              { "title": "Complete", "tasks": [] }
            ]
          }
        ]
      },
    };
  },
  created() {
    this.isFetching = true;
    this.isStatusShow = false;
    this.fetchUsers()
    this.$soketio.on('project_update', (data) => {
      if (this.project.id === data.id) this.setDataProject(data); 
    });
    this.$soketio.on('kanban_update', (kanbanData) => {
      if (!this.getActiveColumn) {
        this.$store.state.application.task.type = kanbanData?.name;
        return;  // Exit once a match is found
      }
    });
    this.$soketio.on('task_update', (task) => {
      if (this.workData?.id === task?.id) {
        this.isSocketHit = true
        setTimeout(() => {
          this.workData = task
          this.isSocketHit = false
        }, 200);
      }
    });
    this.$soketio.on('attachment_add', (attachment) => {
      this.workData.attachments.push(attachment);
    });
  },
  computed: {
    ...mapGetters({
      getOnlineUsers: 'application/getOnlineUsers',
      getActiveProject: 'application/getActiveProject',
      getActiveColumn: 'application/getActiveColumn',
      getActiveTask: 'application/getActiveTask',
      getBoardColsLength: 'application/getBoardColsLength',
      user: 'auth/user',
      isAdmin: 'auth/isAdmin',
      getActiveBoard: 'application/getActiveBoard',
    }),
    project() {
      return this.getActiveProject;
    },
    task() {
      return this.getActiveTask;
    }
  },
  watch: {
    task() {
      if (this.task) {
        setTimeout(() => {
          let taskActive = JSON.parse(JSON.stringify(toRaw(this.getActiveTask)));
          this.getTask(taskActive?.id, taskActive)
        }, 500);
      }
    },
    // Watch for route changes to determine if it's in fullscreen mode
    '$route.name': function(newRoute) {
      this.isFull = newRoute === 'TaskAloneFull' || newRoute === 'TaskAlone' || newRoute === 'TaskDetailFull' || newRoute === 'TaskDetail';
    },

    async $route(to, from) {
      try {
        await this.getKanbanColumn();
        await this.getTask(atob(this.$route.params.slug));

        this.isFetching = true;
        this.isSocketHit = true;

        await new Promise(resolve => setTimeout(resolve, 200));
        
        this.isSocketHit = false;
      } catch (error) {
        console.error('Error in route navigation:', error);
        // Handle the error appropriately
      } finally {
        this.isFetching = false;
      }
    }
  },
  methods: {
    ...mapActions({
      clearOnlineUsers: 'application/clearOnlineUsers',
      setData: 'application/setData',
      changeStatus: 'application/changeStatus',
      updateTaskInStore: 'application/updateTaskInStore',
      resetStoreTask: 'application/resetStoreTask',
      resetStore: 'application/resetStore',
      setDataProject: 'application/setDataProject',
    }),
    getProject() {
      const callback = (response) => {
        const data = response.data;
        console.log(data);
        this.setDataProject(data);
        const roomId = data?.slug;
        this.$soketio.emit('join', roomId);
      };
      const errCallback = (err) => {
        console.log(err);
      };
      projectApi.get(atob(this.$route.params.id), callback, errCallback);
    },
    getKanbanColumn() {
      const callback = (response) => {
        const data = response.data;
        this.populateKanban(data);
      };
      const errCallback = (err) => {
        console.log(err);
      };
      const params = {
        projectId: atob(this.$route.params.id),
        limit: 99,
      }
      kanbanApi.getList(params, callback, errCallback);
    },
    populateKanban(dataFromAPI) {
      // Reset the columns array
      this.dataKanban = {
        boards: [{
          title: "Kanban Planlagt",
          columns: []
        }]
      };
      for (let index = 0; index < dataFromAPI.length; index++) {
        const column = dataFromAPI[index];
        column.tasks = []
        column.title = column.name
        this.dataKanban.boards[0].columns.push(column);
      }
    },
    async mapServerDataToKanban(serverData, kanbanBoard) {
      // Process each task sequentially
      for await (const task of serverData) {
        const kanbanTask = {
          id: task.id,
          name: task.name,
          projectName: task.project.name,
          assign: task.assign,
          completed: task.completed,
          assignTo: task.assignTo,
          startDate: task.startDate,
          dueDate: task.dueDate,
          parentId: task.parentId,
          updatedAt: task.updatedAt,
          description: task.description,
          createdAt: task.createdAt,
          status: task.status,
          index: task.index,
          type: task.type,
          meta: task.meta,
          slug: task.slug,
          creator: task.creator,
          collaborator: task.collaborator,
          subTask: task.subTask ? task.subTask.map(subTask => ({
            name: subTask.name,
            isCompleted: subTask.status === 'completed'
          })) : [],
          // attributes for playing kanban
          activeIndex: -1, // index of active task
          active: '', // name of the active task (in case of edit)
          columnIndex: -1, // index of column of activeTask
          add: false,
          edit: false,
          delete: false,
          show: undefined, // holds the task object, in taskShow
        };

        // Find the correct column and add the task to it
        const column = kanbanBoard.find(col => col.title === task.type);
        if (column) {
          column.tasks.push(kanbanTask);
        }
      }

      // Set the data and update the state
      this.setData(this.dataKanban);

      setTimeout(() => {
        this.isStatusShow = true;
        this.isFetching = false
      }, 200);

      // Return the updated Kanban board
      return kanbanBoard;
    },
    toggleRouteFullscreen() {
      // Check if the current path already ends with "/f"
      if (this.$route.path.endsWith('/f')) {
        // If the current route ends with "/f", navigate back to the original path
        this.$router.push(this.$route.path.slice(0, -2)); // Remove the "/f"
      } else {
        // Otherwise, append "/f" to the current path
        this.$router.push(`${this.$route.path}/f`);
      }
    },
    getFileName(url) {
      // Split the URL by '/' and return the last part
      return url.split('/').pop();
    },
    downloadFile(url) {
      // Create a new anchor element
      const link = document.createElement("a");

      // Set the href to the file URL
      link.href = url;

      // Set the target to '_blank' to open in a new tab
      link.target = "_blank";

      // Set the download attribute (optional, use only if you want to prompt download)
      // link.download = this.fileUrl.split('/').pop(); // Uncomment if you want to force download

      // Append the link to the body (required for Firefox)
      document.body.appendChild(link);

      // Programmatically click the link to trigger the download or open in new tab
      link.click();

      // Remove the link from the document
      document.body.removeChild(link);
    },
    getFileExtension(url) {
      if (!url) return ''; // Return empty if no URL

      // Split the URL by '.' and get the last element
      const parts = url.split('.');
      const extension = parts.length > 1 ? parts.pop().toUpperCase() : ''; // Get extension and convert to uppercase

      // Append additional text based on the file type
      if (extension === 'JPG' || extension === 'JPEG' || extension === 'PNG' || extension === 'GIF') {
        return `${extension} (Image)`; // For image files
      } else if (extension) {
        return `${extension} (File)`; // For other file types
      }
      
      return ''; // Return empty if no extension found
    },
    createComments() {
      this.$refs.activity.createComments(this.comment)
      this.comment = null;
      const container = this.$refs.scrollContainerContent;
      setTimeout(() => {
        if (container) {
          container.scrollTo({
            top: container.scrollHeight,
            behavior: 'smooth', // Smooth scrolling
          });
        }
      }, 500);
    },
    assigneeSelected(event) {
      this.workData.assignTo = event?.id || this.workData?.assign?.id
      this.workData.assign = event || this.workData?.assign
      if (this.task?.id || this.workData?.id) {
        this.assignTask();
      }
    },
    assignTask() {
      const callback = (response) => {
        const data = response.data;
        let finalData = {
          ...this.task, // Fetch the existing task data from the store
          ...data // Merge with the new data from the API response
        };
        this.updateTaskInStore(finalData);
      };

      const errCallback = (err) => {
        const message = err.response?.data?.message || 'Error occurred while updating the task';
        this.__showNotif('error', 'Error', message);
        this.handleErrors(err.response.data);
      };

      const params = {
        assignTo: this.workData.assignTo,
      }

      tasksApi.updateTask(this.task?.id || this.workData?.id, params, callback, errCallback);
    },
    assigneeRemoved() {
      this.workData.assignTo = null
      if (this.task?.id || this.workData?.id) {
        this.assignTask();
      }
    },
    assigneeSelectedCollab(collabs) {
      let collabsFinal = collabs.map(item => item.id);
      this.workData.collaboratorIds = collabsFinal
      if (this.task?.id || this.workData?.id) {
        this.updateTask();
      }
    },
    assigneeRemovedCollab(collabs) {
      let collabsFinal = collabs.map(item => item.id);
      this.workData.collaboratorIds = collabsFinal
      if (this.task?.id || this.workData?.id) {
        this.updateTask();
      }
    },
    updateDatepicker(date) {
      this.workData.dueDate = date ? this.__dateFormatISO(date) : '';
    },

    datePickerSelect(date) {
      this.workData.dueDate = date ? this.__dateFormatISO(date) : '';
      if (this.task?.id || this.workData?.id) {
        this.updateTask();
      }
    },
    initializeData() {
      this.head = (this.$store.state.application.task.edit ? 'Edit' : 'Add New') + ' Task';
      this.ph = 'Task Name';
      this.mode = 'task';
      this.statusCaption = (this.$store.state.application.task.add ? '' : 'Current ') + 'Status';
      this.statusMode = store.task.add ? 'new' : 'edit';
      if (this.$store.state.application.task.edit) {
        let taskActive = JSON.parse(JSON.stringify(toRaw(this.getActiveTask)));
        this.getTask(taskActive?.id, taskActive)
      }
    },
    close() {
      this.resetStoreTask();
      this.$store.state.application.mutate = false;
      if (this.$route.name === 'TaskDetailFull' || this.$route.name === 'TaskDetail') {
        this.$router.push(`/tasks`)
      } else {
        this.$router.push(`/`)
      }
    },

    updateTextCKEditor(newValue) {
      this.workData.description = newValue || '';
      if ((this.task?.id || this.workData?.id) && newValue !== '<p> </p>') {
        this.updateTask();
      }
    },

    updateTask() {
      // Define the callback function that will be called after the API request is successful
      const callback = (response) => {
        const data = response.data;

        // Assuming 'data' contains the updated task details
        let finalData = {
          ...this.task, // Fetch the existing task data from the store
          ...data // Merge with the new data from the API response
        };
        this.updateTaskInStore(finalData);
      };

      // Define the error callback function to handle any errors during the API request
      const errCallback = (err) => {
        const message = err.response?.data?.message || 'Error occurred while updating the task';
        this.__showNotif('error', 'Error', message);
        this.handleErrors(err.response.data);
      };

      // Format the dueDate before sending it to the server
      this.workData.dueDate = this.workData.dueDate ? this.__dateFormatISO(this.workData.dueDate) : '';

      // Make the API request to update the task
      tasksApi.update(this.task?.id, this.workData, callback, errCallback);
    },

    inputRefs(el, index) {
      if (el === null) {
        return;
      }
      if (this.workData.name.length === 0) {
        if (this.validation && index === -1) {
          el.isError = true;
        }
      } else {
        el.isError = false;
      }
    },
    fetchUsers(keyword = null) {
      const callback = (response) => {
        const data = response.data;
        this.users = data
        this.getKanbanColumn()
        this.getProject()
        setTimeout(() => {
          this.initializeData();
        }, 500);
      }
      const errCallback = (err) => {
        const message = err?.response?.data?.message;
        this.__showNotif('error', 'Error', message);
      }
      setTimeout(() => {
        const params = {
        limit: 9999,
        ownerId: this.getActiveProject?.userId,
        isAvailable: 1,
        orderBy: "fullName",
        sortBy: "asc",
      }
        userApi.getList(params, callback, errCallback)
      }, 500);
    },

    deepMerge(target, source) {
      for (const key in source) {
        if (Array.isArray(source[key])) {
          // Concatenate arrays
          target[key] = (target[key] || []).concat(source[key]);
        } else if (source[key] !== null && typeof source[key] === 'object') {
          // Recursively merge objects
          target[key] = this.deepMerge(target[key] || {}, source[key]);
        } else {
          // Overwrite primitives
          target[key] = source[key];
        }
      }
      return target;
    },

    deletetask(item) {
      const callback = (response) => {
        const data = response.data;
        this.$store.state.application.mutate = false;
        this.close()
        this.resetStoreTask();
      }
      const errCallback = (err) => {
        const message = err?.response?.data?.message;
        this.__showNotif('error', 'Error', message);
      }
      tasksApi.delete(this.workData.id, callback, errCallback)
    },
    
    getTask(id, taskActive) {
      // this.isFetching = true
      const callback = (response) => {
        const data = response.data;
        this.workData = data
        this.$store.state.application.task.type = data.type;
        const tasks = []
        this.workData.description = data.description || '';
        tasks.push(this.$store.state.application.task)
        this.mapServerDataToKanban(tasks, this.dataKanban.boards[0].columns)
      }
      const errCallback = (err) => {
        const message = err?.response?.data?.message;
        this.__showNotif('error', 'Error', message);
        this.isFetching = false
      }

      tasksApi.get(id || atob(this.$route.params.slug), callback, errCallback)
    },
    openTask(item) {
      // need to add new component to show detail subtask
    },
    updateComplete(task) {
      const callback = (response) => {
        const data = response.data;

        // Merge the updated data with the existing task data
        let finalData = {
          ...this.task, // Fetch the existing task data from the store
          ...data // Merge with the new data from the API response
        };
        this.updateTaskInStore(finalData);
      };
      let params = {
        status: !task.status || task.status === 'incomplete' ? 'complete' : 'incomplete'
      }

      // Make the API call to update the task on the server
      tasksApi.compeleteTask(task?.id, params, callback, (error) => {
        console.error('Error updating task:', error);
      });
    },

    // upload
    upload(e) {
      const files = e.target.files;
      if (files.length > 0) {
        const fileType = files[0].type;
        this.isUploadingFile = true;
        const file = files[0];
        if (!file) {
          this.__showNotif('warning', 'Upload File', this.$t('Sorry, currently we can\'t upload the file'));
          return;
        }
        const params = new FormData();
        params.append('file', file);
        const callback = (response) => {
          const File = response.data;
          this.fileUrl = File;
          this.createAttachment(this.fileUrl);
        };
        const errorCallback = () => {
          this.isUploadingFile = false;
          this.__showNotif('warning', 'Upload File', this.$t('Sorry, currently we can\'t upload the file'));
        };
        fileApi.upload(params, callback, errorCallback);
      } else {
        this.__showNotif('warning', 'Upload File', this.$t('Unsupported File'));
        return;
      }
    },

    createAttachment(fileUrl) {
      this.isUploadingFile = true;
      const callback = (response) => {
        const data = response.data;
        const message = response.message;
        this.__showNotif('success', 'Success', message);
        // Trigger the drawer open using data-hs-overlay
        this.isUploadingFile = false;
        this.$refs.activity.createComments(this.fileUrl, data.id)
        this.fileUrl = null;
      }
      const errCallback = (err) => {
        const message = err.response.data.message;
        this.__showNotif('error', 'Error', message || 'Error User');
        this.handleErrors(err.response.data)
        this.isUploadingFile = false;
      }
      let params = {
        fileUrl,
        projectId: this.project?.id,
        taskId: this.task?.id
      };
      if (fileUrl) attachmentApi.create(params, callback, errCallback)
    },

    copyURL() {
     const baseURL = import.meta.env.VITE_APP_URL; // Get the base URL from environment variables
      const fullURL = baseURL + `/e/${btoa(this.project.id)}/${this.$route.params.slug}/f`; // Combine base URL and current route
      // Copy the full URL to the clipboard
      navigator.clipboard.writeText(fullURL)
        .then(() => {
          // Optional: Notify the user that the URL has been copied
          this.__showNotif('success', 'success', 'URL Copied to clipboard');
        })
        .catch(err => {
          console.error("Failed to copy the URL: ", err);
        });
    },

    goBackParent() {
      if (this.$route.name === 'TaskDetailKanbanFull' || this.$route.name === 'TaskDetailKanban') {
        this.$router.push(`/e/kanban/${this.__encryptProjectData(this.getActiveProject?.id, this.getActiveProject.slug)}/${btoa(this.workData?.parent?.slug)}`)
      } else if (this.$route.name === 'TaskDetailFull' || this.$route.name === 'TaskDetail') {
        this.$router.push(`/tasks/${btoa(this.workData?.project?.id)}/${btoa(this.workData?.parent?.slug)}`)
      } else if (this.$route.name === 'TaskAloneFull') {
        this.$router.push(`/e/${btoa(this.getActiveProject.id)}/${btoa(this.workData?.parent?.slug)}/f`)
      } else if (this.$route.name === 'TaskAlone') {
        this.$router.push(`/e/${btoa(this.getActiveProject.id)}/${btoa(this.workData?.parent?.slug)}`)
      }
    },
  },
  mounted() {
    onKeyStroke('Escape', this.close);
    // fullscreen check
    if (this.$route.name === 'TaskAloneFull' || this.$route.name === 'TaskAlone' || this.$route.name === 'TaskDetailFull' || this.$route.name === 'TaskDetail') {
      this.isFull = true;
    }
  },
  beforeUnmount () {
    this.resetStore()
    const roomId = this.workData?.slug;
    const roomIdProj = this.getActiveProject?.slug;
    this.$soketio.emit('leave', roomId);
    this.$soketio.emit('leave', roomIdProj);
  }
};
</script>

<style scoped></style>
