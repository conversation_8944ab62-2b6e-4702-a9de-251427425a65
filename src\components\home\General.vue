<template>
	<div class="space-y-6">
		<!-- Stats Cards Section -->
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6">
			<!-- Stats Card 1 -->
			<div class="bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
				<div class="flex items-center justify-between">
					<div>
						<p class="text-sm font-medium text-gray-600 mb-1">{{ statsCards[0].title }}</p>
						<p class="text-3xl font-bold text-gray-900">{{ statsCards[0].value }}</p>
						<p class="text-sm text-gray-500 mt-1">{{ statsCards[0].subtitle }}</p>
					</div>
				</div>
			</div>

			<!-- Stats Card 2 -->
			<div class="bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
				<div class="flex items-center justify-between">
					<div>
						<p class="text-sm font-medium text-gray-600 mb-1">{{ statsCards[1].title }}</p>
						<p class="text-3xl font-bold text-gray-900">{{ statsCards[1].value }}</p>
						<p class="text-sm text-gray-500 mt-1">{{ statsCards[1].subtitle }}</p>
					</div>
				</div>
			</div>

			<!-- Stats Card 3 -->
			<div class="bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
				<div class="flex items-center justify-between">
					<div>
						<p class="text-sm font-medium text-gray-600 mb-1">{{ statsCards[2].title }}</p>
						<p class="text-3xl font-bold text-gray-900">{{ statsCards[2].value }}</p>
						<p class="text-sm text-gray-500 mt-1">{{ statsCards[2].subtitle }}</p>
					</div>
				</div>
			</div>
		</div>
    <t-button
      :color="'primary-solid'"
      class="inline-flex items-center gap-2"
      @click="handleAddCase"
    >
      <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
      </svg>
      Add Case
    </t-button>
		<!-- Action Button and Table Section -->
		<div class="bg-white rounded-lg border border-gray-200 shadow-sm">
			<!-- Header with Add Button -->
			<div class="flex flex-col sm:flex-row sm:items-center sm:justify-between p-6 border-b border-gray-200">
				<div>
					<h2 class="text-lg font-semibold text-gray-900">All Cases</h2>
				</div>
				<div class="mt-4 sm:mt-0 flex flex-col sm:flex-row gap-3">
					<div class="relative">
						<t-input
							v-model="searchQuery"
							:type="'text'"
							:placeholder="'Search case'"
							class="pl-10 pr-4 py-2 w-full sm:w-64"
							@input="handleSearch"
						/>
						<div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
							<svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
							</svg>
						</div>
					</div>
					<t-button
						:color="'secondary-outline'"
						class="inline-flex items-center gap-2"
						@click="handleFilter"
					>
						<svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707v4.586a1 1 0 01-.293.707L9 21.414a1 1 0 01-.707.293H8a1 1 0 01-1-1v-5.586a1 1 0 00-.293-.707L.293 7.707A1 1 0 010 7V4z" />
						</svg>
						Filter
					</t-button>
				</div>
			</div>

			<!-- Table Section -->
			<div class="overflow-x-auto">
				<div class="min-w-full inline-block align-middle">
					<!-- Loading State -->
					<div v-if="isLoading" class="flex justify-center items-center py-12">
						<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
					</div>

					<!-- Table -->
					<table v-else class="min-w-full divide-y divide-gray-200">
						<thead class="bg-gray-50">
							<tr>
								<th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									#
								</th>
								<th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Case ID
								</th>
								<th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Title
								</th>
								<th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Status
								</th>
								<th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Priority
								</th>
								<th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Assigned To
								</th>
								<th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Created Date
								</th>
								<th scope="col" class="relative px-6 py-3">
									<span class="sr-only">Actions</span>
								</th>
							</tr>
						</thead>
						<tbody class="bg-white divide-y divide-gray-200">
							<tr v-for="(caseItem, index) in filteredCases" :key="caseItem.id" class="hover:bg-gray-50">
								<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
									{{ index + 1 }}
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
									{{ caseItem.caseId }}
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
									{{ caseItem.title }}
								</td>
								<td class="px-6 py-4 whitespace-nowrap">
									<span :class="getStatusClass(caseItem.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
										{{ caseItem.status }}
									</span>
								</td>
								<td class="px-6 py-4 whitespace-nowrap">
									<span :class="getPriorityClass(caseItem.priority)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
										{{ caseItem.priority }}
									</span>
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
									{{ caseItem.assignedTo }}
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
									{{ formatDate(caseItem.createdDate) }}
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
									<div class="flex items-center gap-2">
										<button @click="viewCase(caseItem)" class="text-primary-600 hover:text-primary-900">
											View
										</button>
										<button @click="editCase(caseItem)" class="text-gray-600 hover:text-gray-900">
											Edit
										</button>
									</div>
								</td>
							</tr>
						</tbody>
					</table>

					<!-- Empty State -->
					<div v-if="!isLoading && filteredCases.length === 0" class="text-center py-12">
						<svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
						</svg>
						<h3 class="mt-2 text-sm font-medium text-gray-900">No cases found</h3>
						<p class="mt-1 text-sm text-gray-500">Get started by creating a new case.</p>
						<div class="mt-6">
							<t-button :color="'primary-solid'" @click="handleAddCase">
								<svg class="h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
								</svg>
								Add Case
							</t-button>
						</div>
					</div>
				</div>
			</div>

			<!-- Pagination -->
			<div v-if="!isLoading && filteredCases.length > 0" class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
				<div class="flex-1 flex justify-between sm:hidden">
					<button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
						Previous
					</button>
					<button @click="nextPage" :disabled="currentPage === totalPages" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
						Next
					</button>
				</div>
				<div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
					<div>
						<p class="text-sm text-gray-700">
							Showing <span class="font-medium">{{ startIndex }}</span> to <span class="font-medium">{{ endIndex }}</span> of <span class="font-medium">{{ totalCases }}</span> results
						</p>
					</div>
					<div>
						<nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
							<button @click="prevPage" :disabled="currentPage === 1" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
								<span class="sr-only">Previous</span>
								<svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
									<path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
								</svg>
							</button>
							<span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
								Page {{ currentPage }} of {{ totalPages }}
							</span>
							<button @click="nextPage" :disabled="currentPage === totalPages" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
								<span class="sr-only">Next</span>
								<svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
									<path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
								</svg>
							</button>
						</nav>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
	import { mapGetters } from 'vuex';
	import TButton from '@/components/global/Button.vue';
	import TInput from '@/components/form/Input.vue';

	export default {
		components: {
			TButton,
			TInput,
		},
		props: {},
		data() {
			return {
				isLoading: false,
				searchQuery: '',
				currentPage: 1,
				itemsPerPage: 10,
				statsCards: [
					{
						title: 'Stats Title 1',
						value: '1234',
						subtitle: 'Sub sentence information'
					},
					{
						title: 'Stats Title 2',
						value: '76',
						subtitle: 'Sub sentence information'
					},
					{
						title: 'Stats Title 3',
						value: '456',
						subtitle: 'Sub sentence information'
					}
				],
				cases: [
					{
						id: 1,
						caseId: 'CASE-001',
						title: 'Website Performance Issue',
						status: 'Open',
						priority: 'High',
						assignedTo: 'John Doe',
						createdDate: new Date('2024-01-15')
					},
					{
						id: 2,
						caseId: 'CASE-002',
						title: 'Database Connection Error',
						status: 'In Progress',
						priority: 'Critical',
						assignedTo: 'Jane Smith',
						createdDate: new Date('2024-01-14')
					},
					{
						id: 3,
						caseId: 'CASE-003',
						title: 'User Authentication Bug',
						status: 'Resolved',
						priority: 'Medium',
						assignedTo: 'Mike Johnson',
						createdDate: new Date('2024-01-13')
					},
					{
						id: 4,
						caseId: 'CASE-004',
						title: 'Mobile App Crash',
						status: 'Open',
						priority: 'High',
						assignedTo: 'Sarah Wilson',
						createdDate: new Date('2024-01-12')
					},
					{
						id: 5,
						caseId: 'CASE-005',
						title: 'Payment Gateway Integration',
						status: 'In Progress',
						priority: 'Medium',
						assignedTo: 'David Brown',
						createdDate: new Date('2024-01-11')
					}
				]
			};
		},
		computed: {
			...mapGetters({
				user: 'auth/user'
			}),
			filteredCases() {
				if (!this.searchQuery) {
					return this.paginatedCases;
				}
				const filtered = this.cases.filter(caseItem =>
					caseItem.title.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
					caseItem.caseId.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
					caseItem.assignedTo.toLowerCase().includes(this.searchQuery.toLowerCase())
				);
				return this.paginateArray(filtered);
			},
			paginatedCases() {
				return this.paginateArray(this.cases);
			},
			totalCases() {
				return this.searchQuery ? this.filteredCases.length : this.cases.length;
			},
			totalPages() {
				return Math.ceil((this.searchQuery ? this.cases.filter(caseItem =>
					caseItem.title.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
					caseItem.caseId.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
					caseItem.assignedTo.toLowerCase().includes(this.searchQuery.toLowerCase())
				).length : this.cases.length) / this.itemsPerPage);
			},
			startIndex() {
				return (this.currentPage - 1) * this.itemsPerPage + 1;
			},
			endIndex() {
				const end = this.currentPage * this.itemsPerPage;
				return end > this.totalCases ? this.totalCases : end;
			}
		},
		watch: {
			searchQuery() {
				this.currentPage = 1;
			}
		},
		created() {
			this.loadData();
		},
		mounted() {},
		beforeUnmount() {},
		methods: {
			loadData() {
				this.isLoading = true;
				// Simulate API call
				setTimeout(() => {
					this.isLoading = false;
				}, 1000);
			},
			paginateArray(array) {
				const start = (this.currentPage - 1) * this.itemsPerPage;
				const end = start + this.itemsPerPage;
				return array.slice(start, end);
			},
			handleSearch() {
				// Search is handled by computed property
			},
			handleFilter() {
				this.__showNotif('info', 'Filter', 'Filter functionality to be implemented');
			},
			handleAddCase() {
				this.__showNotif('info', 'Add Case', 'Add case functionality to be implemented');
			},
			viewCase(caseItem) {
				this.__showNotif('info', 'View Case', `Viewing case: ${caseItem.title}`);
			},
			editCase(caseItem) {
				this.__showNotif('info', 'Edit Case', `Editing case: ${caseItem.title}`);
			},
			getStatusClass(status) {
				const classes = {
					'Open': 'bg-yellow-100 text-yellow-800',
					'In Progress': 'bg-blue-100 text-blue-800',
					'Resolved': 'bg-green-100 text-green-800',
					'Closed': 'bg-gray-100 text-gray-800'
				};
				return classes[status] || 'bg-gray-100 text-gray-800';
			},
			getPriorityClass(priority) {
				const classes = {
					'Critical': 'bg-red-100 text-red-800',
					'High': 'bg-orange-100 text-orange-800',
					'Medium': 'bg-yellow-100 text-yellow-800',
					'Low': 'bg-green-100 text-green-800'
				};
				return classes[priority] || 'bg-gray-100 text-gray-800';
			},
			formatDate(date) {
				return new Intl.DateTimeFormat('en-US', {
					year: 'numeric',
					month: 'short',
					day: 'numeric'
				}).format(date);
			},
			nextPage() {
				if (this.currentPage < this.totalPages) {
					this.currentPage++;
				}
			},
			prevPage() {
				if (this.currentPage > 1) {
					this.currentPage--;
				}
			}
		},
	};
</script>