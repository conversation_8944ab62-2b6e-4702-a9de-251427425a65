<template>
	<LoaderCircle v-if="isFetching" />
	<div>
		<!-- Stats Cards Section -->
		<StatsCards class="mt-4" :statsCards="statsCards" />

		<!-- Cases Table Section -->
		<CasesTable
			:items="items"
			:isFetching="isFetching"
			:page="page"
			:maxPage="maxPage"
			:total="total"
			:limit="limit"
			@search="onInputSearch"
			@sort="handleSort"
			@next-page="nextPage"
			@prev-page="prevPage"
			@add-case="handleAddCase"
			@view-case="viewCase"
			@edit-case="editCase"
			@delete-case="deleteCase"
		/>
		
	</div>
</template>

<script>
	import { mapGetters } from 'vuex';
	import StatsCards from '@/components/dashboard/StatsCards.vue';
	import CasesTable from '@/components/dashboard/CasesTable.vue';
	import LoaderCircle from '@/components/loader/LoaderCircle.vue';
	import { HSStaticMethods } from "preline";

	export default {
		name: 'General',
		components: {
			StatsCards,
			CasesTable,
			LoaderCircle,
		},
		data() {
			return {
				orderBy: 'title',
				sortBy: 'asc',
				page: 1,
				total: 0,
				maxPage: 1,
				limit: 10,
				keyword: "",
				isFetching: false,
				debounceGetAll: null,
				statsCards: [
					{
						title: 'Stats Title 1',
						value: '1234',
						subtitle: 'Sub sentence information'
					},
					{
						title: 'Stats Title 2',
						value: '76',
						subtitle: 'Sub sentence information'
					},
					{
						title: 'Stats Title 3',
						value: '456',
						subtitle: 'Sub sentence information'
					}
				],
				items: [
					{
						id: 1,
						title: 'Website Performance Issue',
						status: 'Open',
						priority: 'High',
						assignedTo: 'John Doe',
						createdDate: new Date('2024-01-15')
					},
					{
						id: 2,
						title: 'Database Connection Error',
						status: 'In Progress',
						priority: 'Critical',
						assignedTo: 'Jane Smith',
						createdDate: new Date('2024-01-14')
					},
					{
						id: 3,
						title: 'User Authentication Bug',
						status: 'Resolved',
						priority: 'Medium',
						assignedTo: 'Mike Johnson',
						createdDate: new Date('2024-01-13')
					},
					{
						id: 4,
						title: 'Mobile App Crash',
						status: 'Open',
						priority: 'High',
						assignedTo: 'Sarah Wilson',
						createdDate: new Date('2024-01-12')
					},
					{
						id: 5,
						title: 'Payment Gateway Integration',
						status: 'In Progress',
						priority: 'Medium',
						assignedTo: 'David Brown',
						createdDate: new Date('2024-01-11')
					}
				]
			};
		},
		computed: {
			...mapGetters({
				user: 'auth/user'
			})
		},
		created() {
			this.debounceGetAll = this.debounce(this.getAll, 300);
			this.getAll();
		},
		methods: {
			handleSort(sortBy) {
				this.sortBy = sortBy;
				this.getAll();
			},
			nextPage() {
				this.page = this.page + 1;
				this.getAll();
			},
			prevPage() {
				this.page = this.page - 1;
				this.getAll();
			},
			debounce(func, wait) {
				let timeout;
				return function (...args) {
					clearTimeout(timeout);
					timeout = setTimeout(() => {
						func.apply(this, args);
					}, wait);
				};
			},
			onInputSearch(keyword) {
				this.debounceGetAll(keyword);
			},
			handleAddCase() {
				this.__showNotif('info', 'Add Case', 'Add case functionality to be implemented');
			},
			viewCase(caseItem) {
				this.__showNotif('info', 'View Case', `Viewing case: ${caseItem.title}`);
			},
			editCase(caseItem) {
				this.__showNotif('info', 'Edit Case', `Editing case: ${caseItem.title}`);
			},
			deleteCase(caseItem) {
				this.__showNotif('info', 'Delete Case', `Delete case: ${caseItem.title}`);
			},
			getAll(keyword = null) {
				this.isFetching = true;

				// Simulate API call - replace with actual API endpoint
				setTimeout(() => {
					// Filter items based on keyword if provided
					let filteredItems = [...this.items];
					if (keyword) {
						filteredItems = this.items.filter(item =>
							item.title.toLowerCase().includes(keyword.toLowerCase()) ||
							item.assignedTo.toLowerCase().includes(keyword.toLowerCase())
						);
					}

					// Sort items
					filteredItems.sort((a, b) => {
						if (this.sortBy === 'asc') {
							return a[this.orderBy] > b[this.orderBy] ? 1 : -1;
						} else {
							return a[this.orderBy] < b[this.orderBy] ? 1 : -1;
						}
					});

					// Pagination
					this.total = filteredItems?.length;
					this.maxPage = Math.ceil(this.total / this.limit);
					const start = (this.page - 1) * this.limit;
					const end = start + this.limit;
					this.items = filteredItems.slice(start, end);

					if (keyword) {
						this.page = 1;
					}

					this.isFetching = false;

					setTimeout(() => {
						if (typeof HSStaticMethods !== 'undefined') {
							HSStaticMethods.autoInit();
						}
					}, 500);
				}, 500);
			}
		},
	};
</script>