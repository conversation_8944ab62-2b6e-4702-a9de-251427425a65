<template>
	<div class="md:flex md:gap-x-8 mt-6">
		<MyTask></MyTask>
		<MyProject @emptyDaya="checkData" class="md:mt-0 mt-4"></MyProject>
	</div>
</template>

<script>
	import MyTask from '@/components/home/<USER>';
	import MyProject from '@/components/home/<USER>';
	import { mapGetters } from 'vuex';
	/* eslint-disable vue/html-closing-bracket-spacing */
	export default {
		components: {
			MyTask,
			MyProject,
		},
		props: {},
		data() {
			return {
				isEmpty: false,
			};
		},
		computed: {
			...mapGetters({
				user: 'auth/user'
			})
		},
		watch: {},
		created() {
			// if (this.user?.isClient) window.location.href = `${import.meta.env.VITE_APP_URL}/event`
			// else this.isEmpty = false
		},
		mounted() {},
		beforeUnmount() {},
		methods: {
			checkData() {
				this.isEmpty = true
			}
		},
	};
</script>