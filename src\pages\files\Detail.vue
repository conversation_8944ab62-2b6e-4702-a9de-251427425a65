<template>
  <div>
    <!-- Header -->
    <div class="px-4 w-full h-[60px] bg-white flex justify-between items-center border-b-2 border-gray-300">
      <div>
        <div>{{ selectedFile?.user?.fullName }}</div>
        <div>{{ __dateFormatDateFile(selectedFile?.created_at) }}</div>
      </div>
      <div>{{ selectedFile?.name }}</div>
      <div class="flex items-center">
        <t-button
          :color="`primary-white`"
          class="text-black py-2 px-4"
          @click="__downloadFile(selectedFile?.fileUrl)"
        >
          <DownloadIcon class="h-5 w-5" />
          {{ $t('Download') }}
        </t-button>
        <XIcon class="h-5 w-5 ml-4 pointer" @click="closeDetail"/>
      </div>
    </div>

    <!-- Content -->
    <div class="h-[80vh] flex items-center justify-center">
      <!-- Image Preview -->
      <div v-if="isImageFile(fileType)" class="file-preview">
        <img :src="selectedFile.fileUrl" alt="Image Preview" class="max-h-[70vh]" />
      </div>

      <!-- Video Preview -->
      <div v-else-if="isVideoFile(fileType)" class="file-preview">
        <video controls :src="selectedFile.fileUrl" class="max-h-[70vh]"></video>
      </div>

      <!-- Other File Preview (icon + file name) -->
      <div v-else class="flex items-center space-x-3">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-archive w-32 h-32">
          <path d="M10 12v-1"/><path d="M10 18v-2"/><path d="M10 7V6"/><path d="M14 2v4a2 2 0 0 0 2 2h4"/><path d="M15.5 22H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v16a2 2 0 0 0 .274 1.01"/><circle cx="10" cy="20" r="2"/>
        </svg>
        <span>{{ selectedFile?.name }}</span>
      </div>
    </div>
  </div>
</template>


<script>
/* eslint-disable vue/html-closing-bracket-spacing */
import {
  DownloadIcon,
  XIcon
} from "@heroicons/vue/solid";

export default {
  components: {
    DownloadIcon,
    XIcon
  },
  props: {
    files: {},  // list of files
    selectedFile: {}  // the file selected for preview
  },
  computed: {
    // Extract the file type from the selected file
    fileType() {
      return this.selectedFile?.fileUrl?.split('.').pop().toLowerCase();
    }
  },
  methods: {
    closeDetail() {
      console.log('test');
      this.$emit('closeDetail');
    },
    // Method to check if the file is an image
    isImageFile(type) {
      return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(type);
    },
    // Method to check if the file is a video
    isVideoFile(type) {
      return ['mp4', 'webm', 'ogg', 'avi', 'mov', 'wmv'].includes(type);
    },
    getFileName(url) {
      // Split the URL by '/' and return the last part
      return url.split('/').pop();
    },
  }
};
</script>
