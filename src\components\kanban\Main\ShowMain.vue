<template>
  <div style="height: calc(100vh - 220px);">
    <div class="relative ml-6 flex flex-row gap-4">
      <template v-if="getBoardColsLength > 0 && !isShow" >
        <TaskColumn
          ref="columns"
          v-if="users"
          :users="users"
          :column="col"
          v-for="(col, index) in cols"
          :colIndex="index"
          :colTitle="col.title"
          :key="col.title"
          :activeCol="activeCol"
          @dragEnd="handleDragEnd"
          @dragStart="handleDragStart"
          @afterTaskAdded="handleAfterTaskAdded"
          @columnRemove="columnRemove"
          @columnChange="columnChange"
          />
      </template>
      <template v-if="getBoardColsLength > 0 && isShow" >
        <TaskColumn
          ref="columns"
          v-if="users"
          :users="users"
          :column="col"
          v-for="(col, index) in cols"
          :colIndex="index"
          :colTitle="col.title"
          :key="col.title"
          :activeCol="activeCol"
          @dragEnd="handleDragEnd"
          @dragStart="handleDragStart"
          @afterTaskAdded="handleAfterTaskAdded"
          @columnRemove="columnRemove"
          @columnChange="columnChange"
          />
      </template>
    <TaskColumn
      :column="columnAdd"
      :colIndex="cols?.length + 1"
      :isAddColumn="true"
      @columnChange="columnChange"
      @columnRemove="columnRemove"
      ></TaskColumn>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
import userApi from "@/api/user";
import tasksApi from "@/api/tasks";
import { delay } from '@/libraries/helper';

export default {

  data() {
    return {
      users: [],
      currentActiveTask: {},
      currentActiveCol: -1,
      columnAdd: {
        title: 'Add Section New Planlagt',
        tasks: [],
      },
      isShow: true,
    };
  },
  computed: {
    ...mapGetters({
      getActiveBoard: 'application/getActiveBoard',
      getBoardColsLength: 'application/getBoardColsLength',
      getActiveProject: 'application/getActiveProject',
      getActiveTask: 'application/getActiveTask',
    }),
    cols() {
      return this.getActiveBoard?.columns;
    },
    activeCol() {
      return this.currentActiveCol
    }
  },
  watch: {
    
  },
  methods: {
    ...mapActions({
      updateTaskInStore: 'application/updateTaskInStore',
      updateTaskInStoreSocket: 'application/updateTaskInStoreSocket',
      updateOneTaskInStoreSocket: 'application/updateOneTaskInStoreSocket',
      deleteTaskInStore: 'application/deleteTaskInStore',
      showDetailTask: 'application/showDetailTask',
    }),
    handleAfterTaskAdded(dataForOrdering) {
      const tasks = this.cols.map((col) => {
        return {
          type: col.title,
          taskIds: col.tasks.map(task => task.id)
        };
      });

      let taskGroup = tasks.find(task => task.type === dataForOrdering.type);

      if (taskGroup) {
        if (dataForOrdering.position === 'top') {
          taskGroup.taskIds.unshift(dataForOrdering.id);
        } else {
          taskGroup.taskIds.push(dataForOrdering.id);
        }
      }
      const finalParams = {
        type: taskGroup.type,
        taskIds: taskGroup.taskIds,
        taskId: dataForOrdering.id,
      };
      this.reorderTask(finalParams);
    },
    getTaskIdsByType(type) {
      const taskGroup = tasks.find(task => task.type === type);
      return taskGroup ? taskGroup.taskIds : [];
    },

    fetchUsers(keyword = null) {
      const callback = (response) => {
        const data = response.data;
        this.users = data
      }
      const errCallback = (err) => {
        console.log(err)
      }
        const params = {
        limit: 9999,
        orderBy: "fullName",
        sortBy: "asc",
        ownerId: this.getActiveProject?.userId,
        isAvailable: 1,
      }
        userApi.getList(params, callback, errCallback)
      
    },
    handleDragEnd(payloadIdAndType) {
      // Collect task IDs from all columns
      const payload = this.cols.map((col) => {
        return {
          type: col.title,  
          taskIds: col.tasks.map(task => task.id)
        };
      });
      let indexColumn = parseInt(payloadIdAndType?.payload?.indexColumn)
      let finalParams = payload[indexColumn]
      finalParams.taskId = payloadIdAndType?.payload?.taskId
      // delay(() => {
        this.reorderTask(finalParams);
      // }, 500);
    },
    reorderTask(paramsFinal) {
      // Update the task properties in the store
      const callback = (response) => {
        const data = response.data;
      }
      const errCallback = (err) => {
        const message = err.response.data.message;
        this.__showNotif('error', 'Error', message || 'Error User');
      }
      tasksApi.reorder(paramsFinal, callback, errCallback)
    },
    columnChange(data, additionalInfoAddingColumn) {
      this.$emit('columnChange', data, additionalInfoAddingColumn)
    },
    columnRemove(columnId) {
      this.$emit('columnRemove', columnId)
    },
  },
  mounted() {
    if (this.$route.params.slug) {
      this.showDetailTask(atob(this.$route.params.slug));
    }
  },
  beforeUnmount () {
    const roomId = this.getActiveProject?.slug;
    this.$soketio.emit('leave', roomId);
  },
  created() {
    this.fetchUsers();
    const roomId = this.getActiveProject?.slug;
    this.$soketio.emit('join', roomId);
    // main logic of socket, the core fo the core, si kodingnya ada di application store ya maniez
    this.$soketio.on('task_reorder', (reorderData) => {
      this.isShow = false
      delay(() => {
        this.isShow = true
      }, 200);
      for (let index = 0; index < reorderData?.newTasks.length; index++) {
        const element = { 
          task: reorderData?.newTasks[index],
        }
        if (index + 1 === reorderData?.newTasks.length) {
          const oldTaskExistsInNewTasks = reorderData.newTasks.some(newTask => 
            newTask.id === reorderData.oldTask.id && newTask.type === reorderData.oldTask.type
          );
          if (!oldTaskExistsInNewTasks) {
            element.oldTask = reorderData?.oldTask
            this.updateTaskInStoreSocket(element);
          }
        }
        this.updateTaskInStoreSocket(element);
      }
    });

    // delete task
    this.$soketio.on('task_delete', (task) => {
      delay(() => {
        this.deleteTaskInStore(task)
      }, 500);
    });

    // update task
    this.$soketio.on('task_update', (task) => {
      this.updateOneTaskInStoreSocket(task)
    });
  }
};
</script>

<style scoped></style>
