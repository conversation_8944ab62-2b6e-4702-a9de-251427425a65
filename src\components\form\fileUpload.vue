<template>
    <div class="file-upload">
        <input type="file" @change="handleFileUpload" id="file" style="display: none;" ref="fileInput" multiple />
        <label for="file" class="text-sm flex text-gray-400"><PaperClipIcon class="w-4 mr-2"/> Attach a file</label>
    </div>
</template>

<script>
    import fileApi from "@/api/files"; // Adjust the import path based on your project structure.
    import { PaperClipIcon } from "@heroicons/vue/outline";
    import { PlusIcon } from "@heroicons/vue/solid";
    
    export default {
        components: {
            PlusIcon,
            PaperClipIcon
        },
        data() {
            return {
                fileName: '',
                fileUrl: [], // Adjust to handle multiple URLs if needed
                isUploadingPicture: false // Flag to show uploading status
            };
        },
        props: {
            attchmentLength: {
                type: String,
                default: 'No file chosen',
            },
        },
        created() {
            console.log(this.attchmentLength)
            this.fileName = this.fileName ? this.fileName : this.attchmentLength
        },
        methods: {
            handleFileUpload(event) {
                const files = event.target.files;
                const fileLength = files.length;

                if (fileLength === 0) {
                    this.fileName = 'No file chosen';
                    return;
                }

                // Update file name display based on the number of selected files
                this.fileName = fileLength > 1 ? `${fileLength} files chosen` : files[0].name;

                // Upload files one by one
                Array.from(files).forEach(file => {
                    if (file.size > 200 * 1024 * 1024) {
                        this.__showNotif('error', 'Error', 'Cannot upload, file size limit is 200MB');
                        return;
                    }
                    this.uploadFile(file);
                });
            },
            uploadFile(file) {
                this.isUploadingPicture = true;
                const params = new FormData();
                params.append('file', file);

                const callback = (response) => {
                    this.fileUrl.push(response.data); // Store each file URL
                    this.isUploadingPicture = false;
                    this.__showNotif('success', 'Success', `${file.name} uploaded successfully!`);
                    this.$emit('update:fileUrl', this.fileUrl);
                };

                const errorCallback = () => {
                    this.isUploadingPicture = false;
                    this.__showNotif('error', 'Error', this.$t('Sorry, currently we can\'t upload the file'));
                };

                fileApi.upload(params, callback, errorCallback);
            },
        }
    };
</script>

<style scoped>
    .file-upload {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .btn {
        
        color: #ffffff;
        padding: 6px 6px;
        cursor: pointer;
        border: none;
        border-radius: 4px;
    }
</style>