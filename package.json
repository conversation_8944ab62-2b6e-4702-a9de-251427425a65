{"name": "desidia", "version": "0.0.0", "scripts": {"serve": "vite preview", "build": "NODE_OPTIONS='--max-old-space-size=4096' vite build", "deploy": "aws s3 sync ./dist s3://desidia", "dev": "vite --host"}, "dependencies": {"@ckeditor/ckeditor5-vue": "^7.0.0", "@ckpack/vue-color": "^1.1.5", "@fortawesome/fontawesome-svg-core": "^1.3.0", "@fortawesome/free-regular-svg-icons": "^5.15.4", "@fortawesome/free-solid-svg-icons": "^5.15.4", "@fortawesome/vue-fontawesome": "^3.0.0-5", "@grafikri/vue-middleware": "^1.0.0", "@kyvg/vue3-notification": "^2.3.4", "@preline/input-number": "^2.4.1", "@primevue/themes": "^4.0.5", "@vueform/slider": "^2.0.8", "@vuepic/vue-datepicker": "^9.0.1", "axios": "^0.23.0", "check-password-strength": "^2.0.4", "ckeditor5": "43.1.0", "codemirror-editor-vue3": "^1.0.1", "date-fns": "^3.6.0", "dayjs": "^1.10.7", "gradient-parser": "^1.0.2", "html2pdf.js": "^0.9.3", "jquery": "^3.6.0", "localforage": "^1.10.0", "moment": "^2.29.1", "preline": "^2.4.1", "primevue": "^4.0.5", "randomcolor": "^0.6.2", "register-service-worker": "^1.7.1", "secure-ls": "^1.2.6", "uuid": "^10.0.0", "v-tooltip": "https://github.com/paulshen/v-tooltip.git#vue3", "velocity-animate": "^1.5.2", "video-metadata-thumbnails": "^1.0.22", "videojs-abloop": "^1.2.0", "vue": "^3.5.6", "vue-3-slider-component": "^0.1.0", "vue-content-loader": "^2.0.1", "vue-cropperjs": "^5.0.0", "vue-datepicker-next": "^1.0.2", "vue-i18n": "^9.2.0-beta.30", "vue-masonry": "^0.16.0", "vue-meta": "^3.0.0-alpha.10", "vue-multiselect": "^3.0.0-alpha.2", "vue-router": "^4.4.3", "vue3-colorpicker": "^2.0.4", "vue3-google-login": "^2.0.25", "vue3-google-oauth2": "^1.0.6", "vue3-tour": "^0.3.3", "vuedraggable": "^4.1.0", "vuex": "^4.0.2", "vuex-persistedstate": "^4.1.0", "xlsx": "^0.16.5"}, "devDependencies": {"@headlessui/vue": "^1.4.3", "@heroicons/vue": "^1.0.6", "@intlify/vite-plugin-vue-i18n": "^2.5.0", "@playwright/test": "^1.47.1", "@tailwindcss/aspect-ratio": "^0.3.0", "@tailwindcss/forms": "^0.4.0", "@tailwindcss/line-clamp": "^0.2.2", "@tailwindcss/typography": "^0.5.1", "@types/node": "^22.5.5", "@types/tailwindcss": "^2.2.4", "@vitejs/plugin-vue": "^1.10.2", "@vue/cli-plugin-pwa": "^4.5.15", "autoprefixer": "^10.4.19", "babel-eslint": "^10.1.0", "eslint": "^8.9.0", "eslint-plugin-vue": "^8.4.1", "postcss": "^8.4.40", "sass": "^1.49.7", "socket.io-client": "^4.4.1", "tailwindcss": "^3.4.7", "unplugin-auto-import": "^0.11.4", "unplugin-vue-components": "^0.22.9", "v-tooltip": "^2.1.2", "video.js": "^8.3.0", "vite": "^2.8.1", "vite-plugin-pwa": "^0.11.13"}}