<template>
  <loader-circle v-if="isFetchingQuotation" />
  <div v-show="!isFetchingQuotation" id="invoiceId" class="min-h-screen font-sans p-[-10px]">
    <!-- Main Content -->
    <main class="max-w-8xl mx-auto p-4 px-8">
      <div class="bg-white rounded-lg ">
        <div class="px-12 pt-12">
          <div class="grid grid-cols-1 md:grid-cols-[1fr_auto] gap-4 items-center pb-4 mb-4">
          <!-- Content Area -->
          <!-- truncate responsive -->
          <div class="min-w-0"> <!-- min-w-0 ensures that the container will shrink -->
            <h1 class="text-xl font-bold min-h-8">
              {{ project?.name }}
            </h1>
            <p class="text-gray-600 truncate min-h-8">
              {{ project?.packages?.studio ?? 'Off-site event' }}:
              {{ __dateFormatQuotation(project?.startDate, project?.duration) }}
              <span v-if="project?.booking?.invoiceNumber"> | {{ project?.booking?.invoiceNumber }}</span>
            </p>
            <t-button v-if="!isPrint && projectStatus === 'pre' || projectStatus === 'waiting_for_client' || projectStatus === 'review' " :color="`primary-white`" :isLoading="isContinue" class="no-print px-4 py-1 "
                @click="onStartEditEvent">
                <PencilAltIcon class="h-4 w-4 mr-1" aria-hidden="true"></PencilAltIcon>
                {{  $t('Edit Event') }}
              </t-button>
          </div>

          <!-- Logo Area -->
          <div v-show="!isPrint" class="no-print items-center justify-center bg-white-200 border rounded-full w-20 h-20 text-2xl font-bold md:flex hidden">
            {{ __generateInitial(project?.user?.company) }}
          </div>
        </div>

          <div class="flex items-center mb-4">
            <div>
              <p class="text-gray-600">Quote Amount:</p>
              <p class="text-2xl font-bold">{{ __convertCurrency(printTotalPrice,'NOK') }} </p>
            </div>
            <div class="ml-16">
              <p class="text-gray-600">Invoice Recipient:</p>
              <p class="text-2xl font-bold">{{ project?.user?.fullName }} - {{ project?.user?.company }}</p>
            </div>
          </div>

          <div class="flex justify-between mb-2 items-center " v-if="(projectStatus === 'pre' || projectStatus === 'review' || projectStatus === 'active' || projectStatus === 'active_revision')">
            <div class="text-sm font-semibold">{{ $t('Item Requested Summary') }}</div>
            <div>
              <t-button v-show="!isPrint && isEditQuo" :color="`primary-white`" :isLoading="isContinue"
                class="no-print px-4 py-1 mr-2" @click="cancelEdit" >
                <XIcon v-if="isEditQuo" class="h-4 w-4 mr-1" aria-hidden="true"></XIcon>
                {{ $t('Cancel Edit') }}
              </t-button>
              <t-button v-show="!isPrint && isEditQuo" :color="`primary-white`" :isLoading="isContinue" 
                class="no-print px-4 py-1 mr-2" @click="onAddItem" >
                <PlusIcon v-if="isEditQuo" class="h-4 w-4 mr-1" aria-hidden="true"></PlusIcon>
                {{ $t('Add Item') }}
              </t-button>

              <t-button v-if="!isPrint" :color="`primary-white`" :isLoading="isContinue" class="no-print px-4 py-1 "
                @click="onStartEdit">
                <PencilAltIcon v-if="!isEditQuo" class="h-4 w-4 mr-1" aria-hidden="true"></PencilAltIcon>
                <LockClosedIcon v-if="isEditQuo" class="h-4 w-4 mr-1" aria-hidden="true"></LockClosedIcon>
                {{ !isEditQuo ? $t('Edit Quotation') : $t('Close Edit') }}
              </t-button>
            </div>
          </div>

          <div class="rounded-t-lg max-h-[500px] overflow-auto" :class="{'bg-[#F4F4F4]': !isEditQuo}">
            <div class="border rounded-md overflow-hidden relative">
              <div id="mainContainer" v-for="(item, index) in bookingItems" :key="item.id"
                class="flex justify-between items-center p-4 border-b min-h-[57px]" :class="{'mt-[-10px]': isDownloadPdf}">
                <div class="w-60">
                  <p class="text-xs font-semibold ">{{ item.name }} </p>
                </div>
                <div class="flex items-center w-40 justify-end">
                  <span class="text-gray-700 mr-1" v-if="!isEditQuo">{{ item.quantity }}</span> <span v-if="!isEditQuo"> pcs</span>
                  <div v-if="!isPrint && isEditQuo && item.price > 0" class="mb-4  flex-1 flex flex-col overflow-y-auto overflow-hidden max-w-[150px]
                      [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 
                      [&::-webkit-scrollbar-thumb]:bg-gray-300">
                          <div class="flex">
                              <!-- Input Number -->
                              <div class="mr-2 bg-white border border-gray-200 rounded-lg dark:bg-neutral-700 dark:border-neutral-700"
                                  data-hs-input-number="">
                                  <div class="w-full flex justify-between items-center gap-x-1">
                                      <div class="grow py-2 px-3">
                                          <input class="w-full p-0 bg-transparent border-0 text-gray-800 focus:ring-0 
                                  [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none
                                  dark:text-white" style="-moz-appearance: textfield;" type="number"
                                              aria-roledescription="Number field" data-hs-input-number-input=""
                                              v-model="item.quantity" :value="item.quantity" @input="onInputChange(item)">
                                      </div>
                                      <div
                                          class="flex items-center -gap-y-px divide-x divide-gray-200 border-s border-gray-200 dark:divide-neutral-700 dark:border-neutral-700">
                                          <button @click="decreaseCount(item)" type="button"
                                              class="size-10 inline-flex justify-center items-center gap-x-2 text-sm font-medium last:rounded-e-lg bg-white text-gray-800 hover:bg-gray-50 focus:outline-none focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:text-white dark:hover:bg-neutral-800 dark:focus:bg-neutral-800"
                                              aria-label="Decrease">
                                              <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24"
                                                  height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                  stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                  <path d="M5 12h14"></path>
                                              </svg>
                                          </button>
                                          <button @click="increaseCount(item)" type="button"
                                              class="size-10 inline-flex justify-center items-center gap-x-2 text-sm font-medium last:rounded-e-lg bg-white text-gray-800 hover:bg-gray-50 focus:outline-none focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:text-white dark:hover:bg-neutral-800 dark:focus:bg-neutral-800"
                                              aria-label="Increase">
                                              <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24"
                                                  height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                  stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                  <path d="M5 12h14"></path>
                                                  <path d="M12 5v14"></path>
                                              </svg>
                                          </button>
                                      </div>
                                  </div>
                              </div>
                              <!-- End Input Number -->
                          </div>

                  </div>
                </div>
                <div class="w-20 text-right">
                  <span class="text-gray-700 mr-1" >{{ item.price }}</span> 
                </div>
              </div>
            </div>
          </div>
          <div class="flex justify-between items-center p-4 bg-gray-200 rounded-b-lg">
            <div>Total Amount before tax :</div>
            <div class="text-xl font-bold">{{ __convertCurrency(printTotalPrice,'NOK') }}</div>
          </div>
          <div class="flex mt-4  no-print" v-if="!isPrint ">
            <t-switch v-model="isEnableClientEditable" :value="isEnableClientEditable" @change="onAllowModify" />
            <label for=""> Allow client to modify quotation item </label>
          </div>
          <!-- Actions -->
          <div class="flex justify-between items-center mt-4   no-print" v-if="!isPrint ">
            <div class="flex space-x-2">
              <t-button @click="downloadPdf()" :color="`primary-white`" :isLoading="isContinue" class="px-4 py-2">
                <DocumentIcon class="h-4 w-4 mr-1" aria-hidden="true"></DocumentIcon>
                {{ $t('PDF') }}
              </t-button>
              <t-button @click="printPreview()" :color="`primary-white`" :isLoading="isContinue" class="px-4 py-2">
                <PrinterIcon class="h-4 w-4 mr-1" aria-hidden="true"></PrinterIcon>
                {{ $t('Print Quotation') }}
              </t-button>
              <t-button :color="`primary-white`" @click="onEmailAdmin" :isLoading="isContinue" class="px-4 py-2" data-hs-overlay="#confirm">
                <MailIcon class="h-4 w-4 mr-1" aria-hidden="true"></MailIcon>
                {{ $t('Email User') }}
              </t-button>
            </div>
            <span class="text-gray-500 font-semibold" v-if="projectStatus === 'waiting_for_client'"> {{ $t('Waiting response from client') }} </span>  
            <t-button @click="sendQuotation('send')" :color="`primary-solid`" v-if="projectStatus === 'pre' ||  projectStatus === 'review'"
              :is-disabled="projectStatus === 'waiting_for_client' || projectStatus === 'active' || isEditQuo">
              {{ $t('Send Quotation') }}
            </t-button>
            <t-button @click="sendQuotation('revision')" :color="`primary-solid`" v-if="projectStatus === 'active' || projectStatus === 'active_revision'" :is-disabled="isEditQuo">
              {{ $t('Send Revised Quotation') }}
            </t-button>
          </div>
        </div>
        <div class="border-t-[1px] border-gray-200 mt-6">
          <div class="px-12 text-sm text-gray-600 text-left py-4 font-medium">
            {{ $t('Personnel and equipment are available on selected date and venue.') }}
          </div>
        </div>
      </div>
    </main>

    <iframe ref="printFrame" style="display: none;"></iframe>
  </div>
    <DrawerRight :id="`drawer-right`" ref="drawerRight">
      <template #header>
        <div class="">
          <div class="flex mt-4 pb-2">
            
            <p id="hs-pro-dutoo-label" class="text-sm font-semibold text-gray-800 ml-6 ">
              Add Offer Item
            </p>
          </div>
        </div>
      </template>
      <template #body>
        <div class="mt-2 mb-2 px-5 flex-1 flex flex-col overflow-y-auto overflow-hidden 
                        [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 
                        [&::-webkit-scrollbar-thumb]:bg-gray-300">
          <!-- List Item -->
          <div class="mb-4 mt-4">
            <label class="block text-sm font-semibold text-gray-700 mb-1">Item Type</label>
            <div class="relative">
              <select @change="onOnTypeChange" v-model="selectedType"
                class="block w-full px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option key="service" value="service">Service</option>
                <option key="product" value="product" selected="true">Product</option>
              </select>
            </div>
          </div>
          <div class="mb-4" v-if="selectedType === 'product'">
            <label class="block text-sm font-semibold text-gray-700 mb-1">Product</label>
            <div class="relative">
              <select v-model="selectedProduct"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="" disabled selected>Select Product item</option>
                <option v-for="(product, index) in products" :key="product.id" :value="product.id">
                  {{  product.name  }}</option>
              </select>
            </div>
          </div>
          <div class="mb-4" v-if="selectedType === 'service'">
            <label class="block text-sm font-semibold text-gray-700 mb-1">Service</label>
            <div class="relative">
              <select v-model="selectedService"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="" disabled selected>Select service item</option>
                <option v-for="(service, index) in services" :key="service.id" :value="service.id">
                  {{  service.name  }}</option>
              </select>
            </div>
          </div>
          <div class="mb-4 flex-1 flex flex-col overflow-y-auto overflow-hidden w-[150px]
                    [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 
                    [&::-webkit-scrollbar-thumb]:bg-gray-300">
            <label class="text-sm font-semibold mb-2"> Quantity </label>
            <div class="flex">
              <!-- Input Number -->
              <div class="mr-2 bg-white border border-gray-200 rounded-lg dark:bg-neutral-700 dark:border-neutral-700"
                data-hs-input-number="">
                <div class="w-full flex justify-between items-center gap-x-1">
                  <div class="grow py-2 px-3">
                    <input class="w-full p-0 bg-transparent border-0 text-gray-800 focus:ring-0 
                                [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none
                                dark:text-white" style="-moz-appearance: textfield;" type="number"
                      aria-roledescription="Number field" data-hs-input-number-input="" v-model="quantity"
                      :value="quantity">
                  </div>
                  <div
                    class="flex items-center -gap-y-px divide-x divide-gray-200 border-s border-gray-200 dark:divide-neutral-700 dark:border-neutral-700">
                    <button @click="this.quantity = this.quantity-1" type="button"
                      class="size-10 inline-flex justify-center items-center gap-x-2 text-sm font-medium last:rounded-e-lg bg-white text-gray-800 hover:bg-gray-50 focus:outline-none focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:text-white dark:hover:bg-neutral-800 dark:focus:bg-neutral-800"
                      aria-label="Decrease">
                      <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round">
                        <path d="M5 12h14"></path>
                      </svg>
                    </button>
                    <button @click="this.quantity = this.quantity+1" type="button"
                      class="size-10 inline-flex justify-center items-center gap-x-2 text-sm font-medium last:rounded-e-lg bg-white text-gray-800 hover:bg-gray-50 focus:outline-none focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:text-white dark:hover:bg-neutral-800 dark:focus:bg-neutral-800"
                      aria-label="Increase">
                      <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round">
                        <path d="M5 12h14"></path>
                        <path d="M12 5v14"></path>
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
              <!-- End Input Number -->
            </div>

          </div>
          <div class="grid grid-cols-1 gap-x-4  border-gray-200">
            <label class="block text-sm font-semibold text-gray-700">Description</label>
            <t-input v-model="description" :value="description" :type="`area`" placeholder="Typein service description">
            </t-input>
          </div>
        </div>

      </template>
      <template #footer>
        <div class="p-5 border-t border-gray-200">
          <div class="flex items-center gap-x-2 justify-end">
            <!-- Button -->
            <t-button :color="`secondary-solid`" @click="$refs.drawerRight.visibleRight = false">
              Cancel
            </t-button>
            <!-- End Button -->
            <!-- Button -->
            <t-button :color="`primary-solid`" @click="save()" :isLoading="isSaving" :isDisabled="isSaving"
              data-hs-overlay="#drawer-right">
              {{ $t('Save Changes') }}
            </t-button>
            <!-- End Button -->
          </div>
        </div>
      </template>
    </DrawerRight>
    <DrawerRight :id="`drawer-right`" ref="drawerEvent">
      <template #header>
        <div class="">
          <div class="flex mt-4 pb-2">
            <p id="hs-pro-dutoo-label" class="text-sm font-semibold text-gray-800 ml-6 ">
              Edit Event {{ this.project?.name }}
            </p>
          </div>
        </div>
      </template>
      <template #body>
        <div class="mt-2 mb-2 px-5 flex-1 flex flex-col overflow-y-auto overflow-hidden 
                        [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 
                        [&::-webkit-scrollbar-thumb]:bg-gray-300">
          <!-- List Item -->
          <div class="grid grid-cols-1 gap-x-4  border-gray-200 mt-4">
            <label class="block text-sm font-semibold text-gray-700">Event Name</label>
            <t-input v-model="eventName" :value="eventName" :type="`input`" placeholder="Typein event name">
            </t-input>
          </div>
          <!-- <div class="mb-4 mt-4" >
            <label class="block text-sm font-semibold text-gray-700 mb-1">Select Offer</label>
            <div class="relative">
              <VueMultiselect @select="reInitType()" v-model="typeEvent" :options="packages"
              :multiple="false" :closeOnSelect="true" placeholder="What is the type of your event" label="name"
              trackBy="id" />
            </div>
          </div> -->
          <div class="w-full md:w-[373px] pointer">
            <div class="text-sm font-bold mb-2">{{ $t('When is the event?') }}</div>
            <datepicker ref="datePickerBooking" class=" bg-white" :schedules="schedules" :isBooking="isBooking" :isShowEstimated="true" :isButtonAction="false" @actionLeft="customizeEvent"
              @actionRight="bookingEvent" @updateDatepicker="updateDatepicker" @updateDuration="updateDuration" @fetchShcedules="getProjectsSchedule"/>
          </div>
        </div>

      </template>
      <template #footer>
        <div class="p-5 border-t border-gray-200">
          <div class="flex items-center gap-x-2 justify-end">
            <!-- Button -->
            <t-button :color="`secondary-solid`" @click="$refs.drawerEvent.visibleRight = false">
              Cancel
            </t-button>
            <!-- End Button -->
            <!-- Button -->
            <t-button :color="`primary-solid`" @click="updateEvent" :isLoading="isSaving" :isDisabled="isSaving"
              data-hs-overlay="#drawer-right">
              {{ $t('Save Changes') }}
            </t-button>
            <!-- End Button -->
          </div>
        </div>
      </template>
    </DrawerRight>
    <Confirmation :id="`confirm`">
            <template #header>
                <p class="text-base font-bold text-gray-800 dark:text-white">
                    Email: {{ subject }}
                </p>
            </template>
            <template #body>
                <!-- <div class="text-xs text-gray-800 dark:text-neutral-400"> -->
                  <div
                    class="h-full flex-1 flex flex-col overflow-y-auto overflow-hidden [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300">
                    <!-- List Item -->
                    <div class="pb-4 grid grid-cols-1 gap-x-4 border-gray-200">
                        <label class="block text-sm font-semibold text-gray-700 mb-2">Subject</label>
                        <t-input :type="`text-input`" v-model="subject" :value="subject" placeholder="Type in email subject">
                        </t-input>
                    </div>
                    <!-- End List Item -->

                    <!-- List Item -->
                    <div class="pb-4 grid grid-cols-1 gap-x-4 border-gray-200">
                        <t-input :type="`area`" v-model="message" :value="message"
                            placeholder="Type your message here"> </t-input>
                    </div>
                    <!-- End List Item -->
                </div>
                <!-- </div> -->
            </template>
            <template #footer>
                <t-button :color="`primary-white`" class="px-4 py-2" @click="onSendEmail" data-hs-overlay="#confirm">
                  {{ $t('Send Email') }}
                </t-button>
            </template>
    </Confirmation>
</template>

<script>
  import TButton from '@/components/global/Button.vue';
  import TSwitch from '@/components/form/Switch.vue';
  import TInput from '@/components/form/Input.vue';

  import packageApi from "@/api/package";
  import projectApi from "@/api/project";
  import bookingApi from "@/api/booking";
  import productApi from "@/api/product";
  import serviceApi from "@/api/service";
  import {
    PencilAltIcon,
    DocumentIcon,
    PrinterIcon,
    XIcon,
    PlusIcon,
    MailIcon,
    UserCircleIcon, 
    LockClosedIcon
  } from '@heroicons/vue/outline';
  import html2pdf from 'html2pdf.js';
  import DrawerRight from "@/components/form/DrawerRight.vue";
  import Confirmation from "@/components/modal/Confirmation.vue";
  import { duplicateVar } from '@/libraries/helper';
  import datepicker from "@/components/form/Datepicker.vue";
  import moment from 'moment';
  import VueMultiselect from 'vue-multiselect';
  import 'vue-multiselect/dist/vue-multiselect.css';

  export default {
    components: {
      TSwitch,
      TInput,
      TButton,
      PencilAltIcon,
      DocumentIcon,
      PrinterIcon,
      XIcon,
      PlusIcon,
      MailIcon,
      UserCircleIcon,
      DrawerRight,
      LockClosedIcon,
      Confirmation,
      datepicker,
      VueMultiselect,
    },
    data() {
      return {
        project: null,
        isEditQuo: false,
        totalPrice: 0,
        isPrint: false,
        isEnableClientEditable: false,
        selectedType: "product",
        selectedProduct: "",
        selectedService: "",
        quantity: 1,
        description: "",
        deliveryTime: 1,
        deliveryType: "hour",
        isSaving: false,
        drawerId: 'drawer-right',
        services: {},
        products: {},
        isEdit: false,
        selectedItem: {},
        subject: '',
        message: '',
        previousBookingItems: null,
        previousTotalPrice: 0,
        isFetchingQuotation: false,
        schedules: [],
        isBooking: false,
        isCustomize: false,
        eventName: "",
        packages: [],
        typeEvent: null,
        isGetSchedule: false,
        isShowSelectType: false,
        isDownloadPdf: false,
        model: {
          name: null,
          description: null,
          packageId: null,
          startDate: null,
          endDate: null,
          duration: 2,
          fileUrl: '',
          notes: '',
          customItems: null,
        },
      };
    },
    mounted() {
      // Check overflow after the component mounts
      this.checkOverflow();

      // Add resize event listener to handle dynamic changes
      window.addEventListener('resize', this.checkOverflow);
    },
    computed: {
      bookingItems() { 
        console.log(this.project?.booking?.items)
        return this.project?.booking?.items;
      },
      printTotalPrice() {
        return this.totalPrice;
      },
      projectStatus() {
        return this.project?.status;
      },
    },
    watch: {
      project: {
        handler(newVal, oldVal) {
          // Deeply compare the newVal and oldVal to see the changes
          let booking = newVal.booking
        },
        deep: true, // Enable deep watching
      },
    },
    created() {
      this.isPrint = this.$route.name === 'PrintEventQuotation'
      this.getProject();
      this.getAllPackage();
      // const roomId = 
      // this.$soketio.emit('join', roomId);
      this.$soketio.on('project_quotation', (data) => {
        this.project = data.project;
        this.totalPrice = this.project?.booking?.totalPrice;
        setTimeout(() => {
          HSStaticMethods.autoInit();
        }, 500);
        this.getProjectsStarred()
			});
    },
    methods: {
      updateEvent() {
        this.isBooking = true;
        const callback = (response) => {
          const data = response.data;
          this.project = data;
          const message = response.message;
          this.isBooking = false;
        }
        const errCallback = (err) => {
          this.isBooking = false;
          const message = err.response.data.message;
          this.__showNotif('error', 'Error', message );
          this.resetModel();
        }
        this.model.name = this.eventName;
        projectApi.update(this.project.id, this.model, callback, errCallback)
      },
      getAllPackage() {
        const callback = (response) => {
          const data = response.data;
          this.packages = data;
        }
        const errCallback = (err) => {
          console.log(err)
        }

        const params = {
          orderBy: 'createdAt',
          sortBy: 'desc',
          page: 1,
          limit: 9999,
        }
        packageApi.getList(params, callback, errCallback)
      },
      reInitType() {
        this.isShowSelectType = false;
        // this.getAllPackageItem();
        this.getProjectsSchedule();
      },
      customizeEvent() {
        this.isCustomize = true;
      },
      resetModel() {
        this.model = {
          name: null,
          description: null,
          packageId: null,
          startDate: null,
          endDate: null,
          duration: 2,
          fileUrl: '',
          notes: '',
          customItems: null,
        }
      },
      getProjectsSchedule(event) {
        this.isGetSchedule = true;
        const callback = (response) => {
          const data = response.data;
          this.schedules = data;
          this.isGetSchedule = false;
        }
        const errCallback = (err) => {
          console.log(err)
          this.isGetSchedule = false;
        }
        let studioFinal = this.typeEvent?.studio || this.typeEventCustom?.studio
        const params = {
          month: event?.month + 1 || new Date().getMonth() + 1,
          years: event?.year || new Date().getFullYear(),
          studio: studioFinal || ''
        }
        projectApi.getSchedule(params, callback, errCallback)
      },
      updateDatepicker(date) {
        this.model.startDate = this.__dateFormatDateBooking(date)
        this.addingHours(this.model.duration)
      },
      updateDuration(hour) {
        this.model.duration = hour
        this.addingHours(hour)
      },
      addingHours(hour) {
        // Parse the date with moment
        let momentDate = moment(this.model.startDate, "YYYYMM-DD HH:mm");
        // Add 2 hours
        let newDate = momentDate.add(hour, 'hours');
        // Format the new date
        let formattedNewDate = newDate.format("YYYY-MM-DD HH:mm");
        this.model.endDate = formattedNewDate
      },
      cancelEdit() {
        // this.previousBookingItems = duplicateVar(this.bookingItems)
        this.project.booking.items = this.previousBookingItems;
        this.totalPrice = this.previousTotalPrice;
        this.isEditQuo = !this.isEditQuo;
      },
      onStartEditEvent() {
        this.$refs.drawerEvent.visibleRight = true;
        // this.typeEvent = this.packages.find((p) => p.id === this.project?.packageId)
        this.eventName = `Quotation: ${this.project.name}`
      },
      onStartEdit() {
        this.previousBookingItems = duplicateVar(this.bookingItems)
        this.previousTotalPrice = duplicateVar(this.totalPrice)
        this.isEditQuo = !this.isEditQuo
      },
      onEmailAdmin() {
        this.subject = `Quotation - ${this.project?.packages?.name}`;
      },
      onSendEmail() {
        const params = {
          subject: this.subject,
          question: this.message,
          clientId: this.project?.user?.id,
        }
        bookingApi.sendEmail(params, (response)=>{
          this.__showNotif('success', 'Success', 'Your email has been sent!');
        },(err)=>{
          console.log(err)
        })
      },
      onAddItem() {
        this.$refs.drawerRight.visibleRight = true;
        this.getAllService()
        this.getAllProduct()
      },
      getAllService() {
        const callback = (response) => {
          const data = response.data;
          this.services = data;
        }
        const errCallback = (err) => {
          console.log(err)
        }
        const params = {
          orderBy: "name",
          sortBy: "asc",
          page: 1,
          limit: 1000,
        }
        serviceApi.getList(params, callback, errCallback)
      },
      getAllProduct() {
        const callback = (response) => {
          const data = response.data;
          this.products = data;
        }
        const errCallback = (err) => {
          console.log(err)
        }

        const params = {
          orderBy: "name",
          sortBy: "asc",
          page: 1,
          limit: 9999,
        }
        productApi.getList(params, callback, errCallback)
      },
      save() {
        this.isSaving = true;
        const payload = {
          selectedType: this.selectedType,
          productId: this.selectedProduct,
          serviceItemId: this.selectedService,
          quantity: this.quantity,
          description: this.description,
          projectId: this.project.id
        };
        const callback = (response) => {
          const data = response.data;
          const message = response.message;
          this.__showNotif('success', 'Success', message);
          this.description = "";
          this.selectedProduct = "";
          this.selectedService = "";
          this.quantity = 1;
          this.totalPrice = this.totalPrice + data.totalPrice
          this.project.booking.items.push(data);
          this.previousBookingItems.push(data);
          this.isSaving = false;
          this.$refs.drawerRight.visibleRight = false;
        }
        const errCallback = (err) => {
          const message = err.response.data.message
          this.__showNotif('success', 'Success', message);
          this.isSaving = false;
        }
        bookingApi.create(payload, callback, errCallback)
        this.isSaving = false;
      },
      onOnTypeChange() {
        // Handle type change logic
      },
      printPreview() {
        // Create a hidden iframe
        const iframe = this.$refs.printFrame;
        const url =
          import.meta.env.VITE_APP_URL
        const urlToPrint = url + '/print/quotation/' + this.project.id // Replace with your URL

        // Set the iframe's src attribute to the URL
        iframe.src = urlToPrint;

        // Listen for the iframe to load, then trigger the print
        iframe.onload = () => {
          setTimeout(() => {
            iframe.contentWindow.focus();
            iframe.contentWindow.print();
          }, 1000);
        };
      },
      downloadPdf() {
        this.isDownloadPdf = true;
        // this.$nextTick(()=>{
          setTimeout(() => {
            // Clone the content to avoid affecting the actual DOM
            const element = document.getElementById('invoiceId');
            const clonedElement = element.cloneNode(true);
            // const mainContainer = document.getElementById('mainContainer');
            // Hide elements with the no-print class in the cloned element
            const elementsToHide = clonedElement.querySelectorAll('.no-print');
            elementsToHide.forEach(el => {
              el.style.display = 'none';
            });
    
            // Create a container to hold the cloned content
            const container = document.createElement('span');
            container.appendChild(clonedElement);
            const fileName = this.project?.booking?.invoiceNumber ? `Invoice-${this.project?.booking?.invoiceNumber}` : `Invoice`
    
            const opt = {
              margin: [0, 0, 0, 0],
              filename: fileName,
              image: {
                type: 'jpeg',
                quality: 1
              },
              html2canvas: {
                scale: 1,
              },
              jsPDF: {
                unit: 'mm',
                format: 'letter',
                orientation: 'portrait'
              },
              pagebreak: {
                mode: ['avoid-all', 'css', 'legacy']
              }
            };
    
            // Generate the PDF from the cloned content
            html2pdf().from(container).set(opt).save().then(() => {
              // Remove the cloned container after generating the PDF
              container.remove();
              //remove class to main container of mt-[-10] here
              // setTimeout(() => {
              //   mainContainer.classList.remove('mt-[-10px]');
              // }, 500);
              this.isDownloadPdf = false;
              // Add this line to check the HTML content before saving the PDF
            });
          }, 20);
        // })
        
      },
      sendQuotation(status) {
        const id = this.project.id;
        const callback = (response) => {
          const data = response.data;
          this.project = data.project;
          this.__showNotif('success', 'Success', 'Your Quotation has been sent!');
        }
        const errCallback = (error) => {
          const data = error.response.data.data;
          const names = data.map(item => item.name)
          const message = `${error.response.data.message} - ${names}`;
          this.__showNotif('error', 'Error', message);
        }
        const params = {
          customItems: this.project.booking.items.map(item => ({
            id: item.id,
            quantity: item.quantity,
            isEnabled: item.isEnabled ? 1 : 0, // Convert boolean to 0 or 1
          }))
        }
        projectApi.sendQuote(id, status, params, callback, errCallback)
      },
      onInputChange(item) {
        this.status = 'review';
        this.totalPrice = this.bookingItems.reduce((total, booking) => {
          return total + (booking.quantity * booking.price);
        }, 0);
        this.updateItemStatus(item);
      },
      increaseCount(item) {
        item.quantity++;
        this.totalPrice = this.totalPrice + item.price;
        this.updateItemStatus(item);
      },
      decreaseCount(item) {
        if (item.quantity > 0) {
          item.quantity--;
          this.totalPrice = this.totalPrice - item.price;
          this.updateItemStatus(item);
        }
      },
      updateItemStatus(item) {
        item.isEnabled = item.quantity > 0;
      },
      onAllowModify() {
        bookingApi.changeEditableQuotation(this.project.booking.id, (response) => {
          console.log(response.data)
        }, (err) => {
          console.log(err)
        })
      },
      onEnabledChange(item, index) {
        if (item.isEnabled) {
          this.totalPrice = this.totalPrice + (this.project.booking.items[index].quantity * this.project.booking.items[index].price)
        }
        if (!item.isEnabled) {
          this.totalPrice = this.totalPrice - (this.project.booking.items[index].quantity * this.project.booking.items[index].price)
        }
      },
      getProject() {
        this.isFetchingQuotation = true;
        const callback = (response) => {
          const data = response.data;
          this.project = data;
          const roomId = this.project.slug;
          this.$soketio.emit('join', roomId);
          this.totalPrice = this.project?.booking?.totalPrice;
          this.isEnableClientEditable = this.project.booking.isEnableClientEditable;
          // this.initializeItemStatus();
          this.isFetchingQuotation = false;

        };
        const errCallback = (err) => {
          console.log(err);
          this.isFetchingQuotation = false;
        };
        projectApi.get(this.$route.params.id, callback, errCallback);
      },
      initializeItemStatus() {
        this.project.booking.items.forEach(item => {
          this.updateItemStatus(item);
        });
      },
      checkOverflow() {
        const element = this.$refs.truncateText;
        if (element?.scrollWidth > element?.clientWidth) {
          element?.classList.add('truncate');
        } else {
          element?.classList.remove('truncate');
        }
      },
      getProjectsStarred() {
				const callback = (response) => {
					const data = response.data;
					// this.setDatastarredProject(data)
				}
				const errCallback = (err) => {
					console.log(err)
				}

				const params = {
					orderBy: 'createdAt',
					sortBy: 'desc',
					page: 1,
					limit: 100,
				}
				projectApi.getListStarred(params, callback, errCallback)
			},
    },
  };
</script>

<style>
  #main-dtpicker .dp__calendar_item {
    padding: calc(1px ) 0 0 calc(1px ) !important;
  }
</style>