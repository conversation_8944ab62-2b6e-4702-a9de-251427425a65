<template>
  <!-- Loader -->
  <loader-circle v-if="isFetching" />
  <!-- Users Table Card -->
  <div class="px-5 py-2 space-y-4 flex flex-col rounded-sm">
    <!-- Filter Group -->
    <div class="grid md:grid-cols-2 gap-y-2 md:gap-y-0 md:gap-x-5">
      <div class="font-medium">{{ $t('User List') }}</div>
      <div class="flex justify-end items-center gap-x-2">
        <div class="relative">
          <div class="absolute inset-y-0 start-0 flex items-center pointer-events-none z-20 ps-3.5">
            <svg class="shrink-0 size-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
              viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
              stroke-linejoin="round">
              <circle cx="11" cy="11" r="8" />
              <path d="m21 21-4.3-4.3" /></svg>
          </div>
          <form autocomplete="off">
            <input type="text" name="search" v-model="keyword" v-value="keyword" @input="onInputSearch"
              autocomplete="off"
              class="py-[7px] ps-10 pe-8 block w-full bg-white border-gray-200 rounded-md text-sm focus:bg-white focus:border-primary-500 focus:ring-primary-500 disabled:opacity-50 disabled:pointer-events-none"
              placeholder="Search">
          </form>
        </div>

        <!-- button add new -->
        <div>
          <t-button :color="`primary-solid`" @click="addNew()" >
            <PlusIcon class="h-5 w-5 text-white" />
            <span class="text-sm text-white">Add New</span>
          </t-button>
        </div>
      </div>
      <!-- End Col -->
    </div>
    <!-- End Filter Group -->

    <div v-if="!isFetching && items.length">
      <!-- Tab Content -->
      <div id="hs-pro-tabs-dut-all" role="tabpanel" aria-labelledby="hs-pro-tabs-dut-item-all">
        <!-- Table Section -->
        <div
          class="overflow-x-auto [&::-webkit-scrollbar]:h-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300">
          <div class="min-w-full inline-block align-middle">
            <!-- Table -->
            <table class="min-w-full">
              <thead>
                <tr class="">
                  <th scope="col" class="px-3 py-2.5 text-start">
                  </th>

                  <th scope="col" class="min-w-80">
                    <!-- Sort Dropdown -->
                    <div class="hs-dropdown relative inline-flex w-full cursor-pointer">
                      <button id="hs-pro-dutnms" type="button"
                        class="px-5 py-2.5 text-start w-full flex items-center gap-x-1 text-sm text-nowrap font-normal text-gray-500 focus:outline-none focus:bg-gray-100"
                        aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                        Full Name
                        <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                          viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                          stroke-linejoin="round">
                          <path d="m7 15 5 5 5-5" />
                          <path d="m7 9 5-5 5 5" /></svg>
                      </button>

                      <!-- Dropdown -->
                      <div
                        class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-40 transition-[opacity,margin] duration opacity-0 hidden z-10 bg-white rounded-sm shadow-[0_10px_40px_10px_rgba(0,0,0,0.08)]"
                        role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-dutnms">
                        <div class="p-1">
                          <button type="button"
                            class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                            <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                              viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                              stroke-linecap="round" stroke-linejoin="round">
                              <path d="m5 12 7-7 7 7" />
                              <path d="M12 19V5" /></svg>
                            Sort ascending
                          </button>
                          <button type="button"
                            class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                            <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                              viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                              stroke-linecap="round" stroke-linejoin="round">
                              <path d="M12 5v14" />
                              <path d="m19 12-7 7-7-7" /></svg>
                            Sort descending
                          </button>
                        </div>
                      </div>
                      <!-- End Dropdown -->
                    </div>
                    <!-- End Sort Dropdown -->
                  </th>

                  <th scope="col" class="min-w-40">
                    <!-- Sort Dropdown -->
                    <div class="hs-dropdown relative inline-flex w-full cursor-pointer">
                      <button id="hs-pro-dutads" type="button"
                        class="px-5 py-2.5 text-start w-full flex items-center gap-x-1 text-sm text-nowrap font-normal text-gray-500 focus:outline-none focus:bg-gray-100"
                        aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                        Job Title
                        <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                          viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                          stroke-linejoin="round">
                          <path d="m7 15 5 5 5-5" />
                          <path d="m7 9 5-5 5 5" /></svg>
                      </button>

                      <!-- Dropdown -->
                      <div
                        class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-40 transition-[opacity,margin] duration opacity-0 hidden z-10 bg-white rounded-sm shadow-[0_10px_40px_10px_rgba(0,0,0,0.08)]"
                        role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-dutads">
                        <div class="p-1">
                          <button type="button"
                            class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                            <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                              viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                              stroke-linecap="round" stroke-linejoin="round">
                              <path d="m5 12 7-7 7 7" />
                              <path d="M12 19V5" /></svg>
                            Sort ascending
                          </button>
                          <button type="button"
                            class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                            <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                              viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                              stroke-linecap="round" stroke-linejoin="round">
                              <path d="M12 5v14" />
                              <path d="m19 12-7 7-7-7" /></svg>
                            Sort descending
                          </button>
                        </div>
                      </div>
                      <!-- End Dropdown -->
                    </div>
                    <!-- End Sort Dropdown -->
                  </th>

                  <th scope="col" class="min-w-80">
                    <!-- Sort Dropdown -->
                    <div class="hs-dropdown relative inline-flex w-full cursor-pointer">
                      <button id="hs-pro-dutsgs" type="button"
                        class="px-5 py-2.5 text-start w-full flex items-center gap-x-1 text-sm text-nowrap font-normal text-gray-500 focus:outline-none focus:bg-gray-100"
                        aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                        Current Project
                        <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                          viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                          stroke-linejoin="round">
                          <path d="m7 15 5 5 5-5" />
                          <path d="m7 9 5-5 5 5" /></svg>
                      </button>

                      <!-- Dropdown -->
                      <div
                        class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-40 transition-[opacity,margin] duration opacity-0 hidden z-10 bg-white rounded-sm shadow-[0_10px_40px_10px_rgba(0,0,0,0.08)]"
                        role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-dutsgs">
                        <div class="p-1">
                          <button type="button"
                            class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                            <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                              viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                              stroke-linecap="round" stroke-linejoin="round">
                              <path d="m5 12 7-7 7 7" />
                              <path d="M12 19V5" /></svg>
                            Sort ascending
                          </button>
                          <button type="button"
                            class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                            <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                              viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                              stroke-linecap="round" stroke-linejoin="round">
                              <path d="M12 5v14" />
                              <path d="m19 12-7 7-7-7" /></svg>
                            Sort descending
                          </button>
                        </div>
                      </div>
                      <!-- End Dropdown -->
                    </div>
                    <!-- End Sort Dropdown -->
                  </th>

                  <th scope="col" class="min-w-40">
                    <!-- Sort Dropdown -->
                    <div class="hs-dropdown relative inline-flex w-full cursor-pointer">
                      <button id="hs-pro-dutems" type="button"
                        class="px-5 py-2.5 text-start w-full flex justify-center items-center gap-x-1 text-sm text-nowrap font-normal text-gray-500 focus:outline-none focus:bg-gray-100"
                        aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                        Availability
                        <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                          viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                          stroke-linejoin="round">
                          <path d="m7 15 5 5 5-5" />
                          <path d="m7 9 5-5 5 5" /></svg>
                      </button>

                      <!-- Dropdown -->
                      <div
                        class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-40 transition-[opacity,margin] duration opacity-0 hidden z-10 bg-white rounded-sm shadow-[0_10px_40px_10px_rgba(0,0,0,0.08)]"
                        role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-dutems">
                        <div class="p-1">
                          <button type="button"
                            class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                            <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                              viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                              stroke-linecap="round" stroke-linejoin="round">
                              <path d="m5 12 7-7 7 7" />
                              <path d="M12 19V5" /></svg>
                            Sort ascending
                          </button>
                          <button type="button"
                            class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                            <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                              viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                              stroke-linecap="round" stroke-linejoin="round">
                              <path d="M12 5v14" />
                              <path d="m19 12-7 7-7-7" /></svg>
                            Sort descending
                          </button>
                        </div>
                      </div>
                      <!-- End Dropdown -->
                    </div>
                    <!-- End Sort Dropdown -->
                  </th>

                  <th scope="col" class="min-w-40">
                    <!-- Sort Dropdown -->
                    <div class="hs-dropdown relative inline-flex justify-center w-full cursor-pointer">
                      <button id="hs-pro-dutphs" type="button"
                        class="px-5 py-2.5 text-center w-full flex justify-center items-center gap-x-1 text-sm text-nowrap font-normal text-gray-500 focus:outline-none focus:bg-gray-100"
                        aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                        Rating
                        <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                          viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                          stroke-linejoin="round">
                          <path d="m7 15 5 5 5-5" />
                          <path d="m7 9 5-5 5 5" /></svg>
                      </button>

                      <!-- Dropdown -->
                      <div
                        class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-40 transition-[opacity,margin] duration opacity-0 hidden z-10 bg-white rounded-sm shadow-[0_10px_40px_10px_rgba(0,0,0,0.08)]"
                        role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-dutphs">
                        <div class="p-1">
                          <button type="button"
                            class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                            <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                              viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                              stroke-linecap="round" stroke-linejoin="round">
                              <path d="m5 12 7-7 7 7" />
                              <path d="M12 19V5" /></svg>
                            Sort ascending
                          </button>
                          <button type="button"
                            class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                            <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                              viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                              stroke-linecap="round" stroke-linejoin="round">
                              <path d="M12 5v14" />
                              <path d="m19 12-7 7-7-7" /></svg>
                            Sort descending
                          </button>
                        </div>
                      </div>
                      <!-- End Dropdown -->
                    </div>
                    <!-- End Sort Dropdown -->
                  </th>
                </tr>
              </thead>

              <tbody>
                <tr class="border-b hover:bg-slate-100 border-gray-200 group" v-for="(item, index) in items" :key="item.id" @dblclick="editInit(item)">
                  <td class="whitespace-nowrap w-[50px] py-4">
                    {{ (page- 1) * 10 + (index + 1) }}
                  </td>
                  <td class="px-4 py-1 w-full relative cursor-pointer">
                    <div class="w-full flex justify-between items-center gap-x-3">
                      <div class="flex items-center" data-hs-overlay="#drawer-right-detail" @click="selectedItem=__duplicateVar(item)">
                        <img v-if="item && item.imgUrl" class="rounded-full size-9 object-cover" :src="item.imgUrl" alt="avatar-image"
                          referrerpolicy="no-referrer" @error="handleAvatarError(item)">
                        <div v-else>
                          <div
                            class="text-sm rounded-full h-9 w-9 font-medium pt-[6px] text-center bg-white text-black uppercase border-2 border-black">
                            {{ __generateInitial(item.fullName) }}
                          </div>
                        </div>
                        <div class="grow">
                          <span class="text-sm font-medium text-gray-800 ml-2">
                            {{ item.fullName }}
                          </span>
                        </div>
                      </div>
                      <!-- dropdown menu -->
                      <div class="hs-dropdown w-[32px] opacity-0 group-hover:opacity-100 group-hover:opacity-100 transition-opacity">
                        <button id="hs-pro-dutems" type="button"
                          class="max-w-[32px] h-[30px] text-start w-full flex items-center text-sm text-nowrap font-normal pl-1 text-gray-800 shadow outline-gray-200 border border-gray-200 rounded focus:bg-gray-100"
                          aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
                            class="size-5">
                            <path stroke-linecap="round" stroke-linejoin="round"
                              d="M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z" />
                          </svg>
                        </button>
                        <!-- Dropdown -->
                        <div
                          class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-40 transition-[opacity,margin] duration opacity-0 hidden z-10 bg-white rounded-sm shadow-[0_10px_40px_10px_rgba(0,0,0,0.08)]"
                          role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-dutems">
                          <div class="p-1">
                            <button type="button" data-hs-overlay="#drawer-right-detail" @click="selectedItem=__duplicateVar(item)"
                              class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                              {{ $t('View Detail') }}
                            </button>
                            <button type="button"
                              class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                              {{ $t('Call') }}
                            </button>
                            <button type="button"
                              class="border-b border-gray-200 w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                              {{ $t('Message') }}
                            </button>
                            <button type="button" 
                              @click="editInit(item)"
                              class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                              {{ $t('Edit') }}
                            </button>
                            <button @click="selectedItem = __duplicateVar(item)" type="button" data-hs-overlay="#confirm-delete"
                              class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                              {{ $t('Delete') }}
                            </button>
                          </div>
                        </div>
                        <!-- End Dropdown -->
                      </div>
                    </div>
                  </td>
                  <td class="whitespace-nowrap px-6 py-1">
                    <span class="text-sm text-gray-600">
                      {{item?.jobTitle?.name || '-'}}
                    </span>
                  </td>
                  <td class="whitespace-nowrap px-6 py-1">
                    <div class="text-sm text-gray-600 truncate max-w-[300px]">
                      {{ printSingleProject(item) }} <span class="text-xs text-black-400">{{ printProjects(item) }}</span>
                    </div>
                  </td>
                  <td class="whitespace-nowrap px-4 py-1 text-center">
                    <span class="py-1 pb-[6px] px-4 inline-flex items-center text-xs font-medium text-white rounded-md"
                      :class="{'bg-[#14b8a6]': item.isAvailable , 'bg-[#F05b5b]': !item.isAvailable, 'bg-[#FAB312]': item.status === 'onLeave'}">
                      {{ item.isAvailable ? 'Available' : 'Not Available' }}
                    </span>
                  </td>
                  <td class="whitespace-nowrap px-4 py-1 text-center">
                    <span class="text-sm text-gray-600 ">
                      {{item.rating}}
                    </span>
                  </td>
                  <td class="whitespace-nowrap px-4 py-1">
                    <div class="flex">
                    </div>
                  </td>
                </tr>
              </tbody>

            </table>
            <!-- End Table -->
          </div>
        </div>
        <!-- End Table Section -->

        <!-- Footer -->
        <div class="mt-5 flex flex-wrap justify-between items-center gap-2">
          <p class="text-sm ml-4">
            <span class="font-medium text-stone-800 mr-1">{{ total }}</span>
            <span class="font-medium text-stone-800">Results</span>
          </p>
          <!-- Pagination -->
          <nav class="flex justify-end items-center gap-x-1" aria-label="Pagination">
                <button type="button" v-if="page > 1" @click="prevPage()"
                    class="py-2 inline-flex justify-center items-center gap-x-2 text-sm rounded-mdtext-stone-800 hover:bg-stone-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none"
                    aria-label="Previous">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round">
                        <path d="m15 18-6-6 6-6"></path>
                    </svg>
                    <span class="sr-only">Previous</span>
                </button>
                <div class="flex items-center gap-x-1 mr-2 ml-2">
                    <span
                        class="h-[26px] w-[26px] flex justify-center items-center bg-gray-300 py-2 text-sm rounded-md disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-700"
                        aria-current="page">{{ page }}</span>
                    <span
                        class="min-h-[38px] flex justify-center items-center text-stone-500 py-2 px-1.5 text-sm dark:text-neutral-500">of</span>
                    <span
                        class="min-h-[38px] flex justify-center items-center text-stone-500 py-2 px-1.5 text-sm dark:text-neutral-500">{{ maxPage }}</span>
                </div>
                <button type="button" v-if="page < maxPage" @click="nextPage()"
                    class="py-2 inline-flex justify-center items-center gap-x-2 text-sm rounded-mdtext-stone-800 hover:bg-stone-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none"
                    aria-label="Next">
                    <span class="sr-only">Next</span>
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round">
                        <path d="m9 18 6-6-6-6"></path>
                    </svg>
                </button>
            </nav>
          <!-- End Pagination -->
        </div>
        <!-- End Footer -->
      </div>
      <!-- End Tab Content -->
    </div>
  </div>
  <div v-if="!isFetching && !items.length" class="font-bold text-center w-[300px] top-0 bottom-0 my-auto mx-auto right-0 left-0 absolute h-10 w-10 z-50">
    {{ $t('No records found') }}
  </div>
  <!-- End Users Table Card -->

  <!-- user add user -->
    <DrawerRight :id="'drawer-right-add'" ref="drawerRight" @close="closeEdit">
      <template #header>
        <div class="">
          <div class="flex mt-4 pb-2">
            
            <p id="hs-pro-dutoo-label" class="text-sm font-semibold text-gray-800 ml-6 ">
              {{ model.id ? $t('Edit') : $t('Add') }} {{ $t('User') }}
            </p>
          </div>
        </div>
      </template>
      <template #body>
        <div class="mt-5 pb-10 h-full flex-1 flex flex-col [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300">
          <!-- photo -->
          <div class="flex justify-center items-center">
            <img v-if="model && model.imgUrl" class="rounded-full size-16 mb-2 object-cover" :src="model.imgUrl" alt="avatar-image"
              referrerpolicy="no-referrer" @error="handleAvatarError(model)" :tabIndex="1">
            <div v-else class=" mb-2">
              <div class="rounded-full text-2xl h-16 w-16 font-medium pt-[15px] text-center bg-white text-black uppercase border-2 border-black">
                {{ __generateInitial(model?.fullName) }}
              </div>
            </div>
            <button id="hs-pro-dutems" type="button" @click="removeImage" v-if="model && model.imgUrl"
              class="ml-4 min-w-[30px] max-w-[32px] h-[30px] text-start w-full flex items-center text-sm text-nowrap font-normal pl-1 text-gray-800 shadow outline-gray-200 border border-gray-200 rounded focus:bg-gray-100"
              :tabIndex="2">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                stroke="currentColor" class="size-5">
                <path stroke-linecap="round" stroke-linejoin="round"
                  d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0" />
              </svg>
            </button>
          </div>
          <div class="flex justify-center mb-4">
            <label for="fileUploadInput"
              class="relative cursor-pointer rounded-md font-medium text-primary-600 hover:text-primary-500 focus-within:outline-none" :tabIndex="3">
              <span class="font-medium" for="fileUploadInput">{{ $t('Upload Photo') }}</span>
              <input id="fileUploadInput" ref="file" accept="image/*" name="fileUploadInput" type="file" class="sr-only"
                @change="upload">
            </label>
          </div>


          <!-- Profile-->
          <div v-if="selectedItem" class="px-5 font-medium py-1 border-b-[1px] border-gray-200">{{$t('Profile')}}</div>
          <div class="px-5 mt-4">
            <!-- Full name -->
            <div class="py-2 grid grid-cols-1 gap-x-4 border-gray-200">
              <div class="col-span-1">
                <label class="block text-sm font-semibold">{{ $t('Full name') }}</label>
                <t-input v-model="model.fullName" :value="model.fullName" :type="`text`"
                  :placeholder="$t('Type in user full name')" :tabIndex="4"> </t-input>
                <span v-if="errors.fullName" class="text-red-500 text-sm">{{ errors.fullName }}</span>
              </div>
            </div>
            <!-- email -->
            <div class="py-2 grid grid-cols-1 gap-x-4 border-gray-200">
              <div class="col-span-1">
                <label class="block text-sm font-semibold">{{ $t('Email') }}</label>
                <t-input v-model="model.email" :value="model.email" :type="`email`"
                  :placeholder="$t('Type in email address')" :tabIndex="5"> </t-input>
                <span v-if="errors.email" class="text-red-500 text-sm">{{ errors.email }}</span>
              </div>
            </div>
            <!-- phone -->
            <div class="py-2 grid grid-cols-1 gap-x-4 border-gray-200">
              <div class="col-span-1">
                <label class="block text-sm font-semibold">{{ $t('Phone Number') }}</label>
                <t-input v-model="model.phone" :value="model.phone" :type="`number`"
                  :placeholder="$t('Enter phone number')" :tabIndex="6"> </t-input>
                <span v-if="errors.phone" class="text-red-500 text-sm">{{ errors.phone }}</span>
              </div>
            </div>
            <!-- location -->
            <div class="py-2 grid grid-cols-1 gap-x-4 border-gray-200">
              <div class="col-span-1">
                <label class="block text-sm font-semibold">{{ $t('Location') }}</label>
                <t-input v-model="model.location" :value="model.location" :type="`text`"
                  :placeholder="$t('Where is this user located')" :tabIndex="7"> </t-input>
                <span v-if="errors.location" class="text-red-500 text-sm">{{ errors.location }}</span>
              </div>
            </div>
            <!-- company -->
            <div class="py-2 grid grid-cols-1 gap-x-4 border-gray-200">
              <div class="col-span-1">
                <label class="block text-sm font-semibold">{{ $t('Company') }}</label>
                <t-input v-model="model.company" :value="model.company" :type="`text`"
                  :placeholder="$t('Type in user company')" :tabIndex="8"> </t-input>
                <span v-if="errors.company" class="text-red-500 text-sm">{{ errors.company }}</span>
              </div>
            </div>
            <!-- role -->
            <div class="py-2 grid grid-cols-1 gap-x-4 border-gray-200">
              <div class="col-span-1">
                <label class="block text-sm font-semibold">{{ $t('User Role') }}</label>
                <VueMultiselect v-model="model.roleId" :options="roles" :multiple="false" :closeOnSelect="true"
                  placeholder="Select Role" label="name" trackBy="id" :tabIndex="9" />
                <span v-if="errors.roleId" class="text-red-500 text-sm">{{ errors.roleId }}</span>
              </div>
            </div>
            <!-- job -->
            <div class="py-2 grid grid-cols-1 gap-x-4 border-gray-200">
              <div class="col-span-1">
                <label class="block text-sm font-semibold">{{ $t('Job Title') }}</label>
                <VueMultiselect v-model="model.jobTitle" :options="jobs" :multiple="false" :closeOnSelect="true"
                  placeholder="Select Job Title" label="name" trackBy="id" :tabIndex="10" />
                <span v-if="errors.jobTitle" class="text-red-500 text-sm">{{ errors.jobTitle }}</span>
              </div>
            </div>
            <!-- Short Bio -->
            <div class="py-2 grid grid-cols-1 gap-x-4 border-gray-200">
              <div class="col-span-1">
                <label class="block text-sm font-semibold">{{ $t('Short Bio / Description') }}</label>
                <t-textarea v-model="model.shortBio" :value="model.shortBio"
                  :placeholder="$t('Type in short description about this user')" :tabIndex="11"> </t-textarea>
                <span v-if="errors.shortBio" class="text-red-500 text-sm">{{ errors.shortBio }}</span>
              </div>
            </div>
          </div>

          <!-- expertise -->
          <div v-if="selectedItem">
          <div class="mt-12 px-5 font-medium py-1 border-b-[1px] border-gray-200">{{$t('Expertise')}}</div>
          <div class="pb-4 px-5 mt-4">
            <div v-for="(expertise, index) in selectedItem?.expertiseUsers" :key="expertise.id" class="flex mb-2 items-center justify-between border-2 rounded-md border-gray-200 shadow-sm py-6 px-4 font-medium text-[14px]">
              <!-- content -->
              <div class="flex items-center">
                <div>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="size-4">
                    <path d="M21 11H3V9H21V11M21 13H3V15H21V13Z" />
                  </svg>
                </div>
                <div class="ml-2 truncate max-w-[250px]">{{ expertise?.expertise?.name }} - {{ expertise?.level }}</div>
              </div>
              <!-- delete button -->
              <div class="pointer" @click="deleteExpertise(index)">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-4">
                  <path stroke-linecap="round" stroke-linejoin="round" d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0" />
                </svg>
              </div>
            </div>
          </div>
        </div>


        </div>
      </template>
      <template #footer>
        <div class="p-5 border-t border-gray-200">
          <div class="flex items-center gap-x-2 justify-end">
            <!-- Button -->
            <t-button :color="`secondary-solid`" data-hs-overlay="#drawer-right-add" @click="closeAdd()" :tabIndex="12">
              {{ $t('Cancel') }}
            </t-button>
            <!-- End Button -->

            <!-- Button -->
            <t-button :color="`primary-solid`" :isLoading="isSaving"
            :isDisabled="!isValid || isSaving" type="button" @click="submit('add')" :tabIndex="13">
              {{ $t('Save Changes') }}
            </t-button>
            <!-- End Button -->

          </div>
        </div>
      </template>
    </DrawerRight>


  <!-- User Table detail user -->
  <PrelineDrawer :id="'drawer-right-detail'" ref="drawerViewDetails">
    <template #header>
      <div class="">
        <div class="flex mt-4 pb-2">
          
          <p id="hs-pro-dutoo-label" class="text-sm font-semibold text-gray-800 ml-6 ">
            {{ $t('Detail User') }}
          </p>
        </div>
      </div>
      <div class="mt-5 p-5 flex flex-col justify-center items-center text-center">
        <img v-if="selectedItem && selectedItem.imgUrl" class="rounded-full size-16 mb-2 object-cover" :src="selectedItem.imgUrl"
          alt="avatar-image" referrerpolicy="no-referrer" @error="handleAvatarError(selectedItem)">
        <div v-else class=" mb-2">
          <div
            class="rounded-full text-2xl h-16 w-16 font-medium pt-[15px] text-center bg-white text-black uppercase border-2 border-black">
            {{ __generateInitial(selectedItem?.fullName) }}
          </div>
        </div>
        <div class="flex justify-center items-center">
          <span
            :class="{'bg-[#14b8a6]': selectedItem?.isAvailable , 'bg-[#F05b5b]': !selectedItem?.isAvailable, 'bg-[#FAB312]': selectedItem?.isAvailable === 'onLeave'}"
            class="py-1.5 ps-4 pe-4 inline-flex items-center gap-x-1.5 text-xs font-medium text-white rounded-lg">
            {{ selectedItem?.isAvailable ? 'Available' : 'Not Available' }}
          </span>
          <span class="text-xs m-2">
            {{ $t('Last seen') }} 4 hours ago
          </span>
        </div>
        <div class="mt-2 flex">
          <button type="button"
            class="mr-2 h-[30px] text-center px-2 text-start w-full flex items-center text-sm text-gray-800 shadow outline-gray-200 border border-gray-200 rounded focus:bg-gray-100">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
              stroke="currentColor" class="size-4">
              <path stroke-linecap="round" stroke-linejoin="round"
                d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z" />
            </svg>
            <div class="font-medium ml-2">{{ $t('Call') }}</div>
          </button>
          <button type="button"
            class="mr-2 h-[30px] px-2 text-start w-full flex items-center text-sm text-gray-800 shadow outline-gray-200 border border-gray-200 rounded focus:bg-gray-100">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
              stroke="currentColor" class="size-4">
              <path stroke-linecap="round" stroke-linejoin="round"
                d="M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75" />
            </svg>
            <div class="font-medium ml-2">{{ $t('Message') }}</div>
          </button>
          <button type="button" data-hs-overlay="#drawer-right-add" @click="editInit(selectedItem)"
            class="mr-2 h-[30px] px-2 text-start w-full flex items-center text-sm text-gray-800 shadow outline-gray-200 border border-gray-200 rounded focus:bg-gray-100">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
              stroke="currentColor" class="size-4">
              <path stroke-linecap="round" stroke-linejoin="round"
                d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10" />
            </svg>
            <div class="font-medium ml-2">{{ $t('Edit') }}</div>
          </button>
          <button type="button" data-hs-overlay="#confirm-delete"
            class="mr-2 h-[30px] px-2 text-start w-full flex items-center text-sm text-gray-800 shadow outline-gray-200 border border-gray-200 rounded focus:bg-gray-100">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
              stroke="currentColor" class="size-4">
              <path stroke-linecap="round" stroke-linejoin="round"
                d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0" />
            </svg>
          </button>
        </div>
      </div>
    </template>
    <template #body>
      <div
        class="mt-5 h-full flex-1 flex flex-col [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300">
        <div class="hs-accordion-group">
          <!-- accordion -->
          <div class="hs-accordion active bg-white border -mt-px" id="hs-bordered-heading-one">
            <button
              class="hs-accordion-toggle hs-accordion-active:text-blue-600 inline-flex items-center justify-between justify-between gap-x-3 w-full font-semibold text-start text-gray-800 py-2 px-5 hover:text-gray-500 disabled:opacity-50 disabled:pointer-events-none"
              aria-expanded="true" aria-controls="hs-basic-bordered-collapse-one">
              {{ $t('User Profile') }}
              <svg class="hs-accordion-active:hidden block size-3.5" xmlns="http://www.w3.org/2000/svg" width="24"
                height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                stroke-linecap="round" stroke-linejoin="round">
                <path d="M5 12h14"></path>
                <path d="M12 5v14"></path>
              </svg>
              <svg class="hs-accordion-active:block hidden size-3.5" xmlns="http://www.w3.org/2000/svg" width="24"
                height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                stroke-linecap="round" stroke-linejoin="round">
                <path d="M5 12h14"></path>
              </svg>
            </button>
            <div id="hs-basic-bordered-collapse-one"
              class="hs-accordion-content w-full overflow-hidden transition-[height] duration-300"
              role="region"
              aria-labelledby="hs-bordered-heading-one">
            <div class="pb-4 px-5 space-y-2">
              <!-- Row for each label-value pair -->
              <div class="flex">
                <div class="w-5/12 text-gray-600 text-sm">{{ $t('Fullname') }}</div>
                <div class="w-7/12 font-medium text-sm">{{ selectedItem?.fullName || '-' }}</div>
              </div>
              <div class="flex">
                <div class="w-5/12 text-gray-600 text-sm">{{ $t('Email') }}</div>
                <div class="w-7/12 font-medium text-sm">{{ selectedItem?.email || '-' }}</div>
              </div>
              <div class="flex">
                <div class="w-5/12 text-gray-600 text-sm">{{ $t('Phone number') }}</div>
                <div class="w-7/12 font-medium text-sm">{{ selectedItem?.phone || '-' }}</div>
              </div>
              <div class="flex">
                <div class="w-5/12 text-gray-600 text-sm">{{ $t('Location') }}</div>
                <div class="w-7/12 font-medium text-sm">{{ selectedItem?.location || '-' }}</div>
              </div>
              <div class="flex">
                <div class="w-5/12 text-gray-600 text-sm">{{ $t('Company') }}</div>
                <div class="w-7/12 font-medium text-sm">{{ selectedItem?.company || '-' }}</div>
              </div>
              <div class="flex">
                <div class="w-5/12 text-gray-600 text-sm">{{ $t('User role') }}</div>
                <div class="w-7/12 font-medium text-sm">{{ selectedItem?.role?.name || '-' }}</div>
              </div>
              <div class="flex">
                <div class="w-5/12 text-gray-600 text-sm">{{ $t('Job title') }}</div>
                <div class="w-7/12 font-medium text-sm">{{ selectedItem?.jobTitle?.name || '-' }}</div>
              </div>
              <div class="flex items-start">
                <div class="w-5/12 text-gray-600 text-sm">{{ $t('Current project') }}</div>
                <div class="w-7/12 font-medium text-sm">
                  {{ selectedItem ? `${printSingleProject(selectedItem)} ` : '-' }}
                  <span class="text-xs text-gray-400">{{ selectedItem ? printProjects(selectedItem) : '' }}</span>
                </div>
              </div>
              <div class="flex">
                <div class="w-5/12 text-gray-600 text-sm">{{ $t('Short bio') }}</div>
                <div class="w-7/12 font-medium text-sm">{{ selectedItem?.shortBio || '-' }}</div>
              </div>
            </div>
            </div>
          </div>

          <div v-show="selectedItem?.expertiseUsers?.length" class="hs-accordion bg-white border -mt-px " id="hs-bordered-heading-two">
            <button
              class="hs-accordion-toggle hs-accordion-active:text-blue-600 inline-flex items-center justify-between gap-x-3 w-full font-semibold text-start text-gray-800 py-2 px-5 hover:text-gray-500 disabled:opacity-50 disabled:pointer-events-none"
              aria-expanded="false" aria-controls="hs-basic-bordered-collapse-two">
              {{ $t('Expertise') }}
              <svg class="hs-accordion-active:hidden block size-3.5" xmlns="http://www.w3.org/2000/svg" width="24"
                height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                stroke-linecap="round" stroke-linejoin="round">
                <path d="M5 12h14"></path>
                <path d="M12 5v14"></path>
              </svg>
              <svg class="hs-accordion-active:block hidden size-3.5" xmlns="http://www.w3.org/2000/svg" width="24"
                height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                stroke-linecap="round" stroke-linejoin="round">
                <path d="M5 12h14"></path>
              </svg>
            </button>
            <div id="hs-basic-bordered-collapse-two"
              class="hs-accordion-content hidden w-full overflow-hidden transition-[height] duration-300" role="region"
              aria-labelledby="hs-bordered-heading-two">
              <div class="pb-4 px-5">
                <div v-for="expertise in selectedItem?.expertiseUsers" :key="expertise" class="flex mb-2 items-center justify-between border-2 rounded-md border-gray-200 shadow-sm py-6 px-4 font-medium text-[14px]">
                  <!-- content -->
                  <div class="flex items-center">
                    <div>
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="size-4"><path d="M21 11H3V9H21V11M21 13H3V15H21V13Z" /></svg>
                    </div>
                    <div class="ml-2 truncate max-w-[250px]">{{ expertise?.expertise?.name }} - {{ expertise?.level }}</div>
                  </div>
                  <!-- delete button -->
                  <!-- <div>
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                      stroke="currentColor" class="size-4">
                      <path stroke-linecap="round" stroke-linejoin="round"
                        d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0" />
                    </svg>
                  </div> -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template #footer>
      <div class="p-5 border-t border-gray-200">
        <div class="flex items-center gap-x-2 justify-end">
          <!-- Button -->
          <t-button :color="`secondary-solid`" data-hs-overlay="#drawer-right-detail">
            {{ $t('Cancel') }}
          </t-button>
          <!-- End Button -->

          <!-- Button -->
          <t-button :color="`primary-solid`" @click="editInit(selectedItem)" data-hs-overlay="#drawer-right-add" class="px-6">
            {{ $t('Edit') }}
          </t-button>
          <!-- End Button -->
        </div>
      </div>
    </template>
  </PrelineDrawer>
  <!-- End User Table User Details Offcanvas -->

  <!-- confirm delete -->
  <Confirmation :id="'confirm-delete'">
    <template #header>
      <p class="text-base font-medium text-gray-800 dark:text-white">
        {{$t('Delete User')}}
      </p>
    </template>
    <template #body>
      <!-- <div class="text-xs text-gray-800 dark:text-neutral-400"> -->
      <p class="text-sm text-gray-800 dark:text-neutral-400"> {{$t('Are you sure want to delete this user?')}} </p>
      <!-- </div> -->
    </template>
    <template #footer>
      <t-button :color="`secondary-solid`" data-hs-overlay="#confirm-delete">
        {{ $t('Cancel') }}
      </t-button>
      <t-button :color="`red-solid`" @click="confirmDelete()" data-hs-overlay="#confirm-delete">
        {{ $t('Delete') }}
      </t-button>
    </template>
  </Confirmation>
</template>

<script>
  import PrelineDrawer from '@/components/form/PrelineDrawer.vue';
  import TInput from '@/components/form/Input.vue';
  import TTextarea from '@/components/form/Textarea.vue';
  import 'vue-multiselect/dist/vue-multiselect.css';
  import VueMultiselect from 'vue-multiselect';
  import userApi from "@/api/user";
  import rolesApi from "@/api/roles";
  import jobsApi from "@/api/jobs";
  import Confirmation from "@/components/modal/Confirmation.vue";
  import fileApi from '@/api/files';
  import { delay } from '@/libraries/helper';
  import DrawerRight from '@/components/form/DrawerRight.vue';
  import {
    BriefcaseIcon,
    DownloadIcon,
    UserGroupIcon,
    XIcon,
    PlusIcon,
    TrashIcon,
    UserCircleIcon,
  } from '@heroicons/vue/solid';

  import {
    mapGetters,
    mapActions
  } from 'vuex';
  import TModal from '@/components/global/Modal.vue';
  import TButton from '@/components/global/Button.vue';
  import {
    HSStaticMethods
  } from "preline";

  const ITEM_DEFAULT = {
    fullName: null,
    phone: null,
    email: null,
    username: null,
    password: null,
    shortBio: null,
    location: null,
    company: null,
    language: null,
    jobTitle: null,
    jobTitleId: null,
    roleId: null,
    isClient: null,
    isInternal: null,
    isActive: null,
    isAvailable: null,
    imgUrl: null,
    rating: 0,
    userExpertise: null,
    password: null,
    username: null,
  }

  export default {
    components: {
      BriefcaseIcon,
      DownloadIcon,
      UserGroupIcon,
      XIcon,
      TModal,
      PlusIcon,
      TrashIcon,
      PrelineDrawer,
      UserCircleIcon,
      TInput,
      TTextarea,
      VueMultiselect,
      Confirmation,
      TButton,
      DrawerRight
    },
    data() {
      return {
        isFetching: false,
        model: this.__duplicateVar(ITEM_DEFAULT),
        errors: {
          fullName: '',
          username: '',
          email: '',
          password: '',
          phone: '',
          location: '',
          company: '',
          roleId: '',
          jobTitleId: '',
          shortBio: ''
        },
        isUploadingPicture: false,
        orderBy: 'fullName',
        sortBy: 'asc',
        page: 1,
        total: 0,
        maxPage: 1,
        limit: 10,
        selectedItem: null,
        itemId: null,
        items: [],
        meta: null,
        isEdit: false,
        keyword: "",
        jobs: [],
        roles: [],
        isSaving: false,
        parsedExpertise: null,
        debounceGetAll: null,
      };
    },
    watch: {},
    computed: {
      ...mapGetters({
        getToken: 'auth/getToken',
        user: 'auth/user',
        isFetchingUser: 'auth/isFetchingUser',
        isAdmin: 'auth/isAdmin',
      }),
      isPackageMax() {
        return this.user && this.user.userCredit && this.user.userCredit.credit ? this.user.userCredit.credit :
          'Unlimited';
      },
      isValid() {
        if (this.selectedItem?.id) return this.model.fullName && this.model.phone && this.model.email
        return this.model.fullName && this.model.phone && this.model.email
      },
    },
    created() {
      this.debounceGetAll = this.debounce(this.fetchData, 300);
      this.getRoles();
      this.getJobs();
      this.fetchData();
    },
    mounted() {
      setTimeout(() => {
        HSStaticMethods.autoInit();
      }, 500);
    },
    methods: {
      ...mapActions({
        fetchUser: 'auth/fetchUser',
      }),
      printSingleProject(item) {
        // if (item?.currentTasks && item.currentTasks.length >= 1) {
          if (item.currentTasks.length >= 1) return `${item?.currentTasks[0]?.project?.name} `
          else return '-'
        // }
      },
      printProjects(item) {
          if (item?.currentTasks && item.currentTasks.length >= 2) {
            return `& ${item.currentTasks.length-1} more `
          }  
      },
      handleErrors(errorResponse) {
        // Clear existing errors
        this.errors = {
          fullName: '',
          username: '',
          email: '',
          password: '',
          phone: '',
          location: '',
          company: '',
          roleId: '',
          jobTitle: '',
          shortBio: ''
        };
        // Set new errors
        errorResponse.errors.forEach(error => {
          this.errors[error.field] = error.message;
        });
      },
      onInputSearch() {
        this.debounceGetAll(this.keyword);
      },
      nextPage() {
        this.page = this.page + 1;
        this.fetchData()
        setTimeout(() => {
          HSStaticMethods.autoInit();
        }, 500);
      },
      prevPage() {
        this.page = this.page - 1;
        this.fetchData()
        setTimeout(() => {
          HSStaticMethods.autoInit();
        }, 500);
      },
      debounce(func, wait) {
        let timeout;
        return function (...args) {
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                func.apply(this, args);
            }, wait);
        };
      },
      fetchData(keyword = null) {
        this.isFetching = true;
        const callback = (response) => {
          const data = response.data;
          this.items = data
          const meta = response.meta;
          this.meta = meta;
          this.page = meta.currentPage;
          this.maxPage = meta.lastPage;
          this.total = meta.total;
          this.isFetching = false;
          setTimeout(() => {
              HSStaticMethods.autoInit();
          }, 500);
        }
        const errCallback = (err) => {
          console.log(err)
          this.isFetching = false;
        }

        const params = {
          orderBy: this.orderBy,
          sortBy: this.sortBy,
          page: this.page,
          limit: this.limit,
          excludeRoleIds: '[1, 6]',
        }
        if (keyword) params.keyword = keyword;
        userApi.getList(params, callback, errCallback)
      },
      resetForm() {
        this.model = this.__duplicateVar(ITEM_DEFAULT);
      },
      closeAdd() {
        this.$refs.drawerRight.visibleRight = false;
      },
      addNew() {
        this.$refs.drawerRight.visibleRight = true;
        this.isEdit = false;
      },
      editInit(item = null) {
        this.$refs.drawerRight.visibleRight = true;
        if (item) {
          this.isEdit = true;
          this.selectedItem = this.__duplicateVar(item);
          this.model = this.selectedItem;
          if (this.model?.role?.id) {
            let roleObject = this.roles.find(item => item.id === this.model.role.id);
            this.model.roleId = roleObject;
          }
        } else {
          this.resetForm();
        }
      },
      closeEdit() {
        this.$refs.drawerRight.visibleRight = false;
        this.resetModel()
      },
      deleteExpertise(index) {
        // Remove the selected expertise from the array
        this.selectedItem.expertiseUsers.splice(index, 1);
      },

      async submit(status) {
        this.isSaving = true;
        const callback = (response) => {
          const data = response.data;
          const message = response.message;
          this.closeEdit();
          this.fetchData();
          this.isSaving = false;
          this.$refs.drawerRight.visibleRight = false;
          this.__showNotif('success', 'Success', message);
          this.resetModel();

        }
        const errCallback = (err) => {
          this.isSaving = false;
          const message = err.response.data.message;
          this.__showNotif('error', 'Error', message || 'Error User');
          this.handleErrors(err.response.data)
        }
        if (this.model.roleId?.id) this.model.roleId = this.model.roleId.id
        if (this.model.jobTitle?.id) this.model.jobTitleId = this.model.jobTitle.id
        if (!this.isEdit) userApi.create(this.model, callback, errCallback)
        if (this.isEdit) {
          let model = this.__duplicateVar(this.model)
          model.expertiseUsers = this.selectedItem.expertiseUsers.map(userExpertise => ({
            id: userExpertise.expertise.id,
            level: userExpertise.level
          }));
          userApi.update(this.selectedItem.id, model, callback, errCallback)
        }
      },
      confirmDelete() {
        if (this.selectedItem) {
          const callback = (response) => {
            const data = response.data;
            const message = response.message;
            const isDelete = true;
            this.total = this.total - 1;
            this.selectedItem = null;
            this.fetchData();
            this.__showNotif('success', 'Success', message);
          }
          const errCallback = (err) => {
            const message = err?.response?.data?.message;
            this.__showNotif('error', 'Error', message || 'Error User');
          }
          const id = this.selectedItem.id;
          userApi.delete(id, callback, errCallback)
        }
      },
      handleAvatarError(itemWithError) {
        // Find the index of the item in the items array
        const index = this.items.findIndex(item => item.email === itemWithError.email);
        // If the item is found, update its avatarError property
        if (index !== -1) {
          const item = this.items[index];
          item.imgUrl = null;
          Object.assign(this.items[index], item);
        }
      },
      getRoles() {
        const callback = (response) => {
          const roles = response.data;
          this.roles = roles;
        }
        const errCallback = (err) => {
          console.log(err)
        }
        const params = {
          page: 1,
          limit: 1000,
        }
        rolesApi.getList(params, callback, errCallback)
      },
      getJobs() {
        const callback = (response) => {
          const jobs = response.data;
          this.jobs = jobs;
        }
        const errCallback = (err) => {
          console.log(err)
        }
        const params = {
          page: 1,
          limit: 1000,
        }
        jobsApi.getList(params, callback, errCallback)
      },
      upload(e) {
        const files = e.target.files;
        if (files.length > 0) {
          if (files[0].type === 'image/jpeg' || files[0].type === 'image/gif' || files[0].type === 'image/png' || files[0].type === 'image/jpg') {
            this.isUploadingPicture = true;
            const file = files[0];
            if (!file) {
              this.__showNotif('warning', 'Upload File', this.$t('Sorry, currently we can\'t upload the file'));
              return;
            }

            this.compressAndUploadImage(file); // Compress and upload the image
          } else {
            this.__showNotif('warning', 'Upload File', this.$t('Unsupported File'));
            return;
          }
        }
      },
      compressAndUploadImage(file) {
        const reader = new FileReader();

        reader.onload = (e) => {
          const img = new Image();
          img.src = e.target.result;

          img.onload = () => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            // Set desired maximum width and height
            const maxWidth = 100;
            const maxHeight = 100;
            let width = img.width;
            let height = img.height;

            // Calculate aspect ratio
            if (width > height) {
              if (width > maxWidth) {
                height *= (maxWidth / width);
                width = maxWidth;
              }
            } else {
              if (height > maxHeight) {
                width *= (maxHeight / height);
                height = maxHeight;
              }
            }

            // Set canvas size to the new dimensions
            canvas.width = width;
            canvas.height = height;

            // Draw the image onto the canvas with the new size
            ctx.drawImage(img, 0, 0, width, height);

            // Convert the canvas to a Blob or base64 data URL
            canvas.toBlob((blob) => {
              const compressedFile = new File([blob], file.name, { type: file.type });
              this.uploadFile(compressedFile);
            }, file.type, 0.9); // Adjust compression level (0-1)
          };
        };

        reader.readAsDataURL(file); // Read the file as a data URL for the Image object
      },

      uploadFile(file) {
        const params = new FormData();
        params.append('file', file);

        const callback = (response) => {
          const picture = response.data;
          this.model.imgUrl = picture;
          this.isUploadingPicture = false;
          this.__showNotif('success', 'Upload File', 'Photo uploaded successfully!');
        };

        const errorCallback = () => {
          this.isUploadingPicture = false;
          this.__showNotif('warning', 'Upload File', this.$t('Sorry, currently we can\'t upload the file'));
        };

        fileApi.upload(params, callback, errorCallback);
      },
      removeImage() {
        this.model.imgUrl = '';
      },
      resetModel() {
        this.model = {
          fullName: null,
          phone: null,
          email: null,
          username: null,
          password: null,
          shortBio: null,
          location: null,
          company: null,
          language: null,
          jobTitleId: null,
          roleId: null,
          isClient: null,
          isInternal: null,
          isActive: null,
          isAvailable: null,
          imgUrl: null,
          rating: 0,
          userExpertise: null,
        }
      },
    },
  };
</script>