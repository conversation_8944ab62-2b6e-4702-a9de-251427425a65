<template>
	<aside id="hs-pro-sidebar" class="hs-overlay [--auto-close:lg]
	hs-overlay-open:translate-x-0
	-translate-x-full transition-all duration-300 transform
	w-[260px] h-full
	hidden
	fixed inset-y-0 start-0 z-[10]
	bg-white border-e border-gray-200
	lg:block lg:translate-x-0 lg:end-auto lg:bottom-0
	" tabindex="-1" aria-label="Sidebar">
		<div class="relative flex flex-col h-full max-h-full pt-3">
			<header class="h-[46px] px-8">
				<!-- Logo -->
				<a class="flex-none rounded-md text-xl inline-block font-semibold focus:outline-none focus:opacity-80"
					href="/" aria-label="Preline">
					<img class="mx-auto h-10 mb-5 w-auto mb-6" src="@/assets/images/android-chrome-512x512.png"
						alt="Planlagt">
				</a>
				<!-- End Logo -->
			</header>

			<!-- Content -->
			<div
				class="mt-1.5 h-full overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300">
				<!-- Nav -->
				<nav class="hs-accordion-group pb-3  w-full flex flex-col flex-wrap" data-hs-accordion-always-open>
					<ul class="flex flex-col gap-y-1">
						<SideBarItem v-for="(item, index) in navItems" :key="index" :item="item" />

						<!-- Divider -->
						<div class="pl-8 text-sm font-medium text-gray-500 mt-6 mb-4">
							{{ $t('Projects') }}
						</div>
						<div v-for="project in projects" :key="project"
							class="pl-8 font-medium flex items-center mb-3 pointer"
							@click="$router.push(`/e/kanban/${encryptProjectData(project.id, project.slug)}`)">
							<div
								class="border-2 rounded-lg border-gray-300 w-[25px] h-[25px] flex items-center pl-[5px] text-sm text-gray-400">
								{{ __generateInitial(project.name) }}
							</div>
							<div class="text-sm ml-2">
								{{ project.name }}
							</div>
						</div>
						<div v-if="projects.length >= 4" @click="goTo('/projects')" class="pl-8 text-gray-500 font-medium text-sm pointer">
							{{ $t('See all Projects') }}
						</div>
						<!-- End Divider -->
					</ul>
				</nav>
				<!-- End Nav -->
			</div>
			<!-- End Content -->

			<footer class="hidden lg:block mb-2">
				<!-- Project Dropdown -->
				<div class="pl-8 flex items-center">
					<a class="flex items-center gap-x-3 py-2 text-sm text-gray-800 rounded-lg "
						href="../../pro/dashboard/empty-states.html">
						<svg class="shrink-0 size-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" width="24"
							height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
							stroke-linecap="round" stroke-linejoin="round">
							<path
								d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z" />
							<circle cx="12" cy="12" r="3" />
						</svg>
						<div class="text-md font-medium">{{ $t('Settings') }}</div>
					</a>
				</div>
				<!-- End Project Dropdown -->
			</footer>

			<div class="lg:hidden absolute top-3 -end-3 z-10">
				<!-- Sidebar Close -->
				<button type="button"
					class="w-6 h-7 inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-md border border-gray-200 bg-white text-gray-500 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none"
					data-hs-overlay="#hs-pro-sidebar">
					<svg class="shrink-0 size-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
						viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
						stroke-linejoin="round">
						<polyline points="7 8 3 12 7 16" />
						<line x1="21" x2="11" y1="12" y2="12" />
						<line x1="21" x2="11" y1="6" y2="6" />
						<line x1="21" x2="11" y1="18" y2="18" /></svg>
				</button>
				<!-- End Sidebar Close -->
			</div>
		</div>
	</aside>
</template>

<script>
	import projectApi from "@/api/project";
	import SideBarItem from '@/layouts/partials/SideBarItem.vue';
	export default {
		components: {
			SideBarItem,
		},
		created() {
			this.getProjects();
		},
		data() {
			return {
				navItems: [{
						path: '/',
						label: 'Home',
						iconPath1: 'M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z',
						iconPath2: '9 22 9 12 15 12 15 22'
					},
					// {
					// 	path: '/task',
					// 	label: 'My Task',
					// 	iconPath1: 'M6 6.878V6a2.25 2.25 0 0 1 2.25-2.25h7.5A2.25 2.25 0 0 1 18 6v.878m-12 0c.235-.083.487-.128.75-.128h10.5c.263 0 .515.045.75.128m-12 0A2.25 2.25 0 0 0 4.5 9v.878m13.5-3A2.25 2.25 0 0 1 19.5 9v.878m0 0a2.246 2.246 0 0 0-.75-.128H5.25c-.263 0-.515.045-.75.128m15 0A2.25 2.25 0 0 1 21 12v6a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 18v-6c0-.98.626-1.813 1.5-2.122'
					// },
					{
						path: '/inbox',
						label: 'Inbox',
						iconPath1: 'M5.45 5.11L2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z',
						iconPath2: '22 12 16 12 14 15 10 15 8 12 2 12'
					},
					// {
					// 	path: '/client',
					// 	label: 'Client',
					// 	iconPath1: 'M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z'
					// }
				],
				projects: [],
			};
		},
		methods: {
			goTo(route) {
				this.$router.push(route);
			},
			getProjects() {
				const callback = (response) => {
					const data = response.data;
					this.projects = data;
				}
				const errCallback = (err) => {
					console.log(err)
				}
				const params = {
					orderBy: 'createdAt',
					sortBy: 'desc',
					page: 1,
					limit: 5,
				}
				projectApi.getList(params, callback, errCallback)
			},
		}
	};
</script>