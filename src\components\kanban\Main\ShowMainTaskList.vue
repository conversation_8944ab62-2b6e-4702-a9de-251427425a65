<template>
  <div>
    <div v-if="getBoardColsLength > 0" class="relative">
      <!-- <TaskColumn
        ref="columns"
        v-if="users"
        :users="users"
        :column="col"
        v-for="(col, index) in cols"
        :colIndex="index"
        :key="col.title"
        :activeCol="activeCol"
        @dragEnd="handleDragEnd"
        @dragStart="handleDragStart"
        @onAddTask="handleAddTask"
        @afterTaskAdded="handleAfterTaskAdded"
      /> -->
      <!-- Table Section -->
      <div
          class="overflow-x-auto mt-6 [&::-webkit-scrollbar]:h-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300">
          <div class="min-w-full inline-block align-middle">
            <!-- Table -->
            <table class="min-w-full">
              <thead class="border-t border-b border-gray-200">
                <tr class="">
                  <th scope="col" class="px-3 py-2.5 text-start">
                  </th>

                  <th scope="col" class="min-w-80">
                    <!-- Sort Dropdown -->
                    <div class="hs-dropdown relative inline-flex w-full cursor-pointer">
                      <button id="hs-pro-dutnms" type="button"
                        class="px-2 py-2.5 text-start w-full flex items-center gap-x-1 text-sm text-nowrap font-normal text-gray-500 focus:outline-none focus:bg-gray-100"
                        aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                        Task Name
                      </button>
                    </div>
                    <!-- End Sort Dropdown -->
                  </th>

                  <th scope="col" class="min-w-40">
                    <!-- Sort Dropdown -->
                    <div class="hs-dropdown relative inline-flex w-full cursor-pointer">
                      <button id="hs-pro-dutads" type="button"
                        class="px-5 py-2.5 text-start w-full flex items-center gap-x-1 text-sm text-nowrap font-normal text-gray-500 focus:outline-none focus:bg-gray-100"
                        aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                        Due Date
                        <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                          viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                          stroke-linejoin="round">
                          <path d="m7 15 5 5 5-5" />
                          <path d="m7 9 5-5 5 5" /></svg>
                      </button>

                      <!-- Dropdown -->
                      <div
                        class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-40 transition-[opacity,margin] duration opacity-0 hidden z-10 bg-white rounded-sm shadow-[0_10px_40px_10px_rgba(0,0,0,0.08)]"
                        role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-dutads">
                        <div class="p-1">
                          <button type="button"
                            class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                            <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                              viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                              stroke-linecap="round" stroke-linejoin="round">
                              <path d="m5 12 7-7 7 7" />
                              <path d="M12 19V5" /></svg>
                            Sort ascending
                          </button>
                          <button type="button"
                            class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                            <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                              viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                              stroke-linecap="round" stroke-linejoin="round">
                              <path d="M12 5v14" />
                              <path d="m19 12-7 7-7-7" /></svg>
                            Sort descending
                          </button>
                        </div>
                      </div>
                      <!-- End Dropdown -->
                    </div>
                    <!-- End Sort Dropdown -->
                  </th>

                  <th scope="col" class="min-w-80">
                    <!-- Sort Dropdown -->
                    <div class="hs-dropdown relative inline-flex w-full cursor-pointer">
                      <button id="hs-pro-dutsgs" type="button"
                        class="px-5 py-2.5 text-start w-full flex items-center gap-x-1 text-sm text-nowrap font-normal text-gray-500 focus:outline-none focus:bg-gray-100"
                        aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                        Project
                      </button>
                    </div>
                    <!-- End Sort Dropdown -->
                  </th>
                </tr>
              </thead>

              <tbody class="">
                <tr class=" border-b border-gray-200 hover:bg-slate-100 pointer" v-for="(task, index) in tasks" :key="task.id" @click="showTask(task, index)">
                  <td class="pl-6  whitespace-nowrap py-4 w-[50px]">
                    {{ (page- 1) * 10 + (index + 1) }}
                  </td>
                  <td class=" whitespace-nowrap px-3 py-1">
                    <span class="text-sm text-gray-600">
                      {{task.name || '-'}}
                    </span>
                  </td>
                  <td class=" whitespace-nowrap px-6 py-1">
                    <span class="text-sm text-gray-600">
                      {{ task.dueDate ? __dateHumanizeText(task.dueDate) : '-'}}
                    </span>
                  </td>
                  <td class=" whitespace-nowrap px-4 py-1">
                    <div class="inline-flex items-center px-3 py-1 bg-[#DEDEDE] rounded-lg max-w-xs text-xs">
                      <span class="truncate">
                        {{ task.projectName }}
                      </span>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
            <!-- End Table -->
          </div>
        </div>
        <!-- End Table Section -->
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
import userApi from "@/api/user";
import tasksApi from "@/api/tasks";

export default {

  data() {
    return {
      users: [],
      currentActiveTask: {},
      currentActiveCol: -1
    };
  },
  computed: {
    ...mapGetters({
      getActiveBoard: 'application/getActiveBoard',
      getBoardColsLength: 'application/getBoardColsLength',
    }),
    tasks() {
      const taskData = this.getActiveBoard.columns
      // Extracting and flattening tasks into a single array
      const allTasks = taskData.reduce((acc, stage) => {
          // Merge all tasks into a single array
          return acc.concat(stage.tasks);
      }, []);
      // Sort the tasks based on the 'createdAt' field
      allTasks.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
      return allTasks;
    },
    activeCol() {
      return this.currentActiveCol
    }
  },
  watch: {
    
  },
  props: {
    page: {
      type: Number,
      default: 1,
    },
  },
  methods: {
    ...mapActions({
    }),
    showTask(task, index) {
      this.$store.state.application.task = task
      this.$store.state.application.task.active = task.id;
      this.$store.state.application.task.status = task.status;
      this.$store.state.application.task.type = task.type;
      this.$store.state.application.task.activeIndex = index;
      this.$store.state.application.task.columnIndex = this.colIndex;
      this.$store.state.application.task.addDirectly = false;
      this.$store.state.application.task.edit = true;
      this.$store.state.application.task.add = false;
      this.$store.state.application.mutate = true;
    },
    fetchUsers(keyword = null) {
      const params = {}; // Add any params needed here
      userApi.getList(params, (response) => {
        const data = response.data;
        this.users = data
      }, (err) => {
        console.log(err);
      });
    },
  },
  mounted() {
    this.fetchUsers();
  }
};
</script>

<style scoped></style>
