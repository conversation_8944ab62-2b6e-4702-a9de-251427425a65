<template>
  <!-- add column left -->
  <section v-if="isAddColumnLeft" class="mt-6 min-w-[280px]">
    <div class="mb-6 flex flex-row items-center">
      <div class="w-full flex justify-between items-center pointer">
        <div>
          <kbInput controlType="sectionColumnAddDirection" :ph="'Add Section +'" @blur="addEditSection('add', 'left')" @keyup.enter="addEditSection('add', 'left')" v-model="sectionNameAdd" class="z-1 ml-5 mt-2 w-[190px]">
          </kbInput>
        </div>
      </div>
    </div>
    <div style="height: calc(100vh - 200px);" class="bg-gray-100 rounded-lg hover:bg-gray-200 pointer">
    </div>
  </section>
  <!-- main column -->
  <section v-if="colIndex >= 0 && !isAddColumn" class="mt-6 min-w-[280px]">
    <div class="mb-6 flex flex-row items-center">
      <div v-if="colIndex === 0" class="w-[15px] h-[15px] bg-[#a8a8a8] rounded-full"></div>
      <div v-if="colIndex === 1" class="w-[15px] h-[15px] bg-[#12c789] rounded-full"></div>
      <div v-if="colIndex === 2" class="w-[15px] h-[15px] bg-[#67E2AE] rounded-full"></div>
      <div v-if="colIndex === 3" class="w-[15px] h-[15px] bg-[#debe0b] rounded-full"></div>
      <div v-if="colIndex === 4" class="w-[15px] h-[15px] bg-[#ff7b1a] rounded-full"></div>
      <div v-if="colIndex >= 5" class="w-[15px] h-[15px] bg-[#7610dd] rounded-full"></div>

      <div class="w-full flex justify-between items-center">
      <!-- column name -->
        <div>
          <kbInput ref="sectionInputName" controlType="sectionColumn" :ph="'Add Section +'" @blur="updateKanban()" @keyup.enter="updateKanban()" v-model="sectionName" class="z-1 ml-5 mt-2 w-[190px]">
          </kbInput>
        </div>
        <div class="flex">
          <PlusIcon @click="addTask(colIndex); isAddTop = true; isAddDown = false;" class="h-4 w-4 ml-2 mt-1 pointer" aria-hidden="true"></PlusIcon>
          <ButtonDropdown>
            <template #button>
              <DotsVerticalIcon class="h-4 w-4 ml-2 mt-[4px] pointer"></DotsVerticalIcon>
            </template>
            <template #items>
              <MenuItem as="div">
                <div @click="renameSection()"
                  class="w-full pointer flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100">
                  Rename Section
                </div>
              </MenuItem>
              <!-- add section -->
              <MenuItem as="div" @mouseover="showSubmenu = true" @mouseleave="showSubmenu = false">
                <div class="w-full pointer flex justify-between items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800">
                  <div>Add Section</div>
                  <div>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <!-- Submenu (Children) for Completed and Hold -->
                <transition name="fade">
                  <div v-if="showSubmenu" class="absolute top-[40px] left-full bg-white shadow-lg rounded-md z-50 w-48 p-2">
                    <div @click="addSection('left')"
                      class="w-full pointer justify-between flex items-center gap-x-3 py-1 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100">
                      <div>Add section to left</div>
                      <ArrowLeftIcon class="h-4 w-4 ml-2 pointer"></ArrowLeftIcon>
                    </div>
                    <div @click="addSection('right')"
                      class="w-full pointer flex justify-between items-center gap-x-3 py-1 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100">
                      <div>Add section to right</div>
                      <ArrowRightIcon class="h-4 w-4 ml-2 pointer"></ArrowRightIcon>
                    </div>
                  </div>
                </transition>
              </MenuItem>
              <MenuItem v-if="isAdmin" as="div" data-hs-overlay="#confirm-delete">
                <div @click="deleteSection()"
                  class="border-t-[1px] pointer w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100">
                  Delete
                </div>
              </MenuItem>
            </template>
          </ButtonDropdown>
        </div>
      </div>
    </div>
    <!-- show every task card of this column, enable drag and drop -->
    <div :class="{'is-dragging': isDragging}">
      <div>
        <AddTask @onAddTaskComplete="handleAddTaskComplete" v-if="task?.addDirectly && activeColumnIndex === colIndex && isAddTop" @close="hideAddTask" :position="'top'" :users="users" @afterTaskAdded="afterTaskAdded"></AddTask>
        <draggable
          class="min-w-[19px]"
          style="height: calc(100vh - 350px);"
          v-model="tasks"
          group="cols"
          item-key="s"
          :animation="150"
          @end="dragEnd"
          @start="dragStart"
          :data-index="colIndex"
          :data-type="colTitle"
          @change="onDragChange"
          drag-class="drag"
          ghost-class="ghost"
        >
          <template #item="{ element: task, index }" >
            <TaskCard :data-taskid="task.id" :data-task="task" :data-taskname="task.name" :data-tasktype="task.type" @click="showTask(task, index)" class="mb-2" :task="task" :key="task.id" :index="index" :users="users">
            </TaskCard>
          </template>

          <!-- add task -->
          <template #footer>
            <div class="my-2">
              <AddTask @onAddTaskComplete="handleAddTaskComplete" v-if="task?.addDirectly && activeColumnIndex === colIndex && isAddDown" @close="hideAddTask" :position="'bottom'" :users="users" @afterTaskAdded="afterTaskAdded"></AddTask>
              <article
                v-if="taskCount === 0"
                @click="addTask(colIndex); isAddDown = true; isAddTop = false;"
                :class="['w-[17.5rem] rounded-lg bg-white dark:bg-kb_dark_grey dark:text-white shadow-md cursor-pointer hover:scale-105 flex justify-center', { 'add-task': !isDragging, 'add-task-dragging': isDragging }]"
              >
                <div class="py-2 mx-4 flex items-center">
                  <PlusIcon class="h-4 w-4 ml-2" aria-hidden="true"></PlusIcon>
                  Add Task
                </div>
              </article>
              <div
                v-else
                :class="['flex justify-center items-center mb-8 cursor-pointer hover:scale-105', { 'add-task': !isDragging, 'add-task-dragging': isDragging }]"
                @click="addTask(colIndex); isAddDown = true; isAddTop = false;"
              >
                <PlusIcon class="h-4 w-4 ml-2" aria-hidden="true"></PlusIcon>
                Add Task
              </div>
            </div>
          </template>
        </draggable>
      </div>
    </div>
  </section>
  <!-- right columng -->
  <section v-if="isAddColumnRight" class="mt-6 min-w-[280px]">
    <div class="mb-6 flex flex-row items-center">
      <div class="w-full flex justify-between items-center pointer">
        <div>
          <kbInput controlType="sectionColumnAddDirection" :ph="'Add Section +'" @blur="addEditSection('add', 'right')" @keyup.enter="addEditSection('add', 'right')" v-model="sectionNameAdd" class="z-1 ml-5 mt-2 w-[190px]">
          </kbInput>
        </div>
      </div>
    </div>
    <div style="height: calc(100vh - 200px);" class="bg-gray-100 rounded-lg hover:bg-gray-200 pointer">
    </div>
  </section>
  <!-- stay column always visible -->
  <section v-if="isAddColumn" class="mt-6 min-w-[300px] pr-[20px]">
    <div class="mb-6 flex flex-row items-center">
      <div class="w-full flex justify-between items-center pointer">
        <div>
          <kbInput controlType="sectionColumn" :ph="'Add Section +'" @blur="addEditSection('add', 'right')" @keyup.enter="addEditSection('add', 'right')" v-model="sectionNameAdd" class="z-1 ml-5 mt-2 w-[190px]">
          </kbInput>
        </div>
      </div>
    </div>
    <div style="height: calc(100vh - 200px);" class="bg-gray-100 rounded-lg hover:bg-gray-200 pointer">
    </div>
  </section>
  <ModalGeneral :isShow="isShowModalDelete" @update:isShow="isShowModalDelete = $event">
      <template #header>
        <p class="text-base font-medium text-gray-800 dark:text-white">
          {{$t('Delete Section')}}
        </p>
      </template>
      <template #body>
        <!-- <div class="text-xs text-gray-800 dark:text-neutral-400"> -->
        <p class="text-sm text-gray-800 dark:text-neutral-400"> {{$t('Are you sure want to delete this section?')}} </p>
        <!-- </div> -->
      </template>
      <template #footer>
        <t-button class="px-6" :color="`secondary-solid`" @click="isShowModalDelete = false;">
          {{ $t('Cancel') }}
        </t-button>
        <t-button class="px-6" :color="`red-solid`" @click="confirmDelete">
          {{ $t('Delete') }}
        </t-button>
      </template>
  </ModalGeneral>
  <!-- modal rename -->
  <ModalGeneral :isShow="isModalEditOpen" @update:isShow="isModalEditOpen = $event">
      <template #header>
        <p class="text-base font-medium text-gray-800 dark:text-white">
          {{$t('Rename Section')}}
        </p>
      </template>
      <template #body>
        <!-- <div class="text-xs text-gray-800 dark:text-neutral-400"> -->
        <p class="text-sm text-gray-800 dark:text-neutral-400"> {{$t('Rename')}} {{ selectedColumn?.name }} </p>
        <t-input v-model="selectedSectionName" :value="selectedSectionName" :type="`text-input`" placeholder="Type in new name"> </t-input>
        <!-- </div> -->
      </template>
      <template #footer>
        <t-button :color="`secondary-solid`" @click="isModalEditOpen = false;">
          {{ $t('Cancel') }}
        </t-button>
        <t-button :isDisabled="!selectedSectionName" :color="`primary-solid`" @click="updateKanban()">
          {{ $t('Rename') }}
        </t-button>
      </template>
  </ModalGeneral>
</template>

<script>
import {
    mapGetters,
    mapActions
  } from 'vuex';
import { PlusIcon, DotsVerticalIcon, ArrowLeftIcon, ArrowRightIcon } from '@heroicons/vue/solid';
import draggable from "vuedraggable";
import ModalGeneral from "@/components/modal/ModalGeneral.vue";
import kbInput  from "@/components/kanban/kbInput.vue";
import kanbanApi from "@/api/kanban";

export default {
  components: {
    draggable,
    TaskCard: () => import('@/components/kanban/Main/TaskCard.vue'),
    AddTask: () => import('@/components/kanban/Boards/AddTask.vue'),
    PlusIcon,
    DotsVerticalIcon,
    ArrowRightIcon,
    ArrowLeftIcon,
    ModalGeneral,
    kbInput
  },
  props: {
    column: {
      type: Object,
      required: true,
    },
    colIndex: {
      type: Number,
      required: true,
    },
    colTitle: {
      type: String,
    },
    users: {
    },
    activeCol: {
      type: Number,
      required: false,
    },
    isAddColumn: {
      type: Boolean,
      required: false,
    }
  },
  data() {
    return {
      tasks: this.column.tasks,
      isDragging: false,
      activeColumnIndex: null, // Track the active column index
      currentActiveTask: null,
      currentActiveColumn: null,
      isAddTop: false,
      isAddDown: false,

      // crud section
      isShowModalDelete: false,
      sectionName: '',
      isProcessSection: false,
      selectedColumn: null,
      isModalEditOpen: false,
      selectedSectionName: '',
      sectionNameAdd: '',
      isSaving: false,
      isAddColumnLeft: false,
      isAddColumnRight: false,
    };
  },
  computed: {
    ...mapGetters({
        getOnlineUsers: 'application/getOnlineUsers',
        getActiveProject: 'application/getActiveProject',
        getActiveBoard: 'application/getActiveBoard',
        getActiveColumn: 'application/getActiveColumn',
        getActiveTask: 'application/getActiveTask',
        getBoardColsLength: 'application/getBoardColsLength',
        user: 'auth/user',
        isAdmin: 'auth/isAdmin',
      }),
    taskName() {
      return this.column.title;
    },
    taskCount() {
      return this.column.tasks.length;
    },
    task() {
      return this.$store.state.application.task;
    },
  },
  created() {
  },
  watch: {
    tasks: {
      handler(newTasks) {
        this.column.tasks = newTasks;
      },
      deep: true,
    },
  },
  methods: {
    ...mapActions({
        clearOnlineUsers: 'application/clearOnlineUsers',
        setData: 'application/setData',
        changeStatus: 'application/changeStatus',
        updateTaskInStore: 'application/updateTaskInStore',
        resetStoreTask: 'application/resetStoreTask',
      }),
    afterTaskAdded(dataForOrdering) {
      this.$emit('afterTaskAdded', dataForOrdering)
    },
    countSubTasks(task) {
      let count = 0;
      task.subTask.forEach((el) => {
        if (el.isCompleted) {
          count++;
        }
      });
      return `${count} of ${task.subTask.length} subTask`;
    },
    handleAddTaskComplete() {
      this.currentActiveColumn = null
    },
    checkAddTask(activeCol) {
      if ((activeCol) !== (this.colIndex)) {
        this.activeColumnIndex  = activeCol;
      }
    },
    addTask(indexColumn) {
      this.currentActiveColumn = indexColumn;
      // Check if there is an active AddTask component and close it
      if (this.$store.state.application.activeAddTaskColumnIndex !== null && this.$store.state.application.activeAddTaskColumnIndex !== indexColumn) {
        this.hideAddTask(); // Close the currently open AddTask
      }

      // Set the new active column index and open the AddTask component
      this.$store.state.application.task.type = this.getActiveBoard.columns[indexColumn].title;
      this.$store.state.application.task.addDirectly = true;
      this.activeColumnIndex = indexColumn;
      this.$store.state.application.activeAddTaskColumnIndex = indexColumn; // Update the store with the active column index
      this.$emit('onAddTask', indexColumn)
    },
    hideAddTask() {
      this.$store.state.application.task.addDirectly = false;
      this.activeColumnIndex = null;
      this.$store.state.application.activeAddTaskColumnIndex = null; // Reset the active column index
    },
    showTask(task, index) {
      this.$router.push(`/e/kanban/${this.__encryptProjectData(this.getActiveProject.id, this.getActiveProject.slug)}/${btoa(task?.slug)}`)
      this.$store.state.application.task = task
      this.$store.state.application.task.active = task.id;
      this.$store.state.application.task.status = task.status;
      this.$store.state.application.task.type = task.type;
      this.$store.state.application.task.activeIndex = index;
      this.$store.state.application.task.columnIndex = this.colIndex;
      this.$store.state.application.task.addDirectly = false;
      this.$store.state.application.task.edit = true;
      this.$store.state.application.task.add = false;
      this.$store.state.application.mutate = true;
    },
    dragEnd(e) {
      // // Get the index of the column to which tasks belong
      let index = e.to.getAttribute("data-index");
      let type = e.to.getAttribute("data-type");
      let id = e.item.getAttribute("data-taskid");
      
      // // Prepare the payload
      const payload = {
        taskId: this.currentActiveTask,
        type: type,
        indexColumn: index,
      };

      // // Send the payload to the server
      this.$emit('dragEnd', { payload });

    // this.isDragging = false;
    },
    onDragChange(event) {
      // You can add your logic here to handle the changes
    },
    dragUpdate(e) {
    },
    dragStart(evt) {
      const item = evt.item;
      const payload = {
        id: item.getAttribute('data-taskid')
      }
      this.currentActiveTask = item.getAttribute('data-taskid')
      this.$emit('dragStart', { payload });
      this.isDragging = true;
      this.hideAddTask(); // Hide AddTask when dragging starts
    },
    handleClickOutside(event) {
      if (!this.$el.contains(event.target)) {
        this.hideAddTask(); // Hide AddTask when clicking outside
      }
    },
    addSection(direction){
      if(direction === 'left') {
        this.isAddColumnLeft = true
        this.isAddColumnRight = false
      } else {
        this.isAddColumnRight = true
        this.isAddColumnLeft = false
      }
    },
    deleteSection() {
      this.isShowModalDelete = true;
    },
    confirmDelete() {
      if (this.column) {
        const callback = (response) => {
            const message = response.message;
            this.$emit('columnRemove', this.column.id)
            this.__showNotif('success', 'Success', message);
            this.isShowModalDelete = false
        }
        const errCallback = (err) => {
            console.log(err)
            this.isShowModalDelete = false
        }
        const id = this.column.id;
        kanbanApi.delete(id, callback, errCallback)
      }
    },

    addEditSection(status, direction) {
      this.isProcessSection = true;
      const callback = (response) => {
        const data = response.data;
        const message = response.message;
        const additionalInfoAddingColumn = {
          direction,
          selectedIndexColumn: this.colIndex
        }
        this.$emit('columnChange', data, additionalInfoAddingColumn)
        // Trigger the drawer open using data-hs-overlay
        this.isProcessSection = false
        this.isAddColumnRight = false
        this.isAddColumnLeft = false
        this.sectionNameAdd = ''
      }
      const errCallback = (err) => {
        const message = err?.response?.data?.message;
        this.__showNotif('error', 'Error', message);
        this.isProcessSection = false;
        this.sectionNameAdd = ''
      }
      let indexAdd = this.colIndex
      let params = {
        name: this.sectionNameAdd || this.sectionName,
        projectId: this.getActiveProject?.id,
        directions: direction,
        currentIndex: this.colIndex,
      };

      if (this.sectionNameAdd) {
        if (status === 'add') { kanbanApi.create(params, callback, errCallback); return; }
      } else {
        this.isAddColumnRight = false
        this.isAddColumnLeft = false
        return;
      }


      if (this.sectionName) {
        if (this.column?.id) kanbanApi.update(this.column?.id, params, callback, errCallback)
        if (!this.column.id) kanbanApi.create(params, callback, errCallback)
      } else {
        this.isAddColumnRight = false
        this.isAddColumnLeft = false
      }
    },

    renameSection() {
      this.selectedColumn = this.column
      this.$refs.sectionInputName.setFocusSection();
      // this.updateKanban()
    },

    updateKanban() {
      this.isSaving = true;
      const callback = (response) => {
        const data = response.data;
        const message = response.message;
        this.isSaving = false;
        this.isModalEditOpen = false;
        this.sectionName = data.name
      }
      const errCallback = (err) => {
        const message = err.response.data.message;
        this.__showNotif('error', 'Error', message || 'Error User');
        this.isSaving = false;
        this.isModalEditOpen = false;
      }
      const params = {
        name: this.sectionName
      }
      kanbanApi.update(this.column.id, params, callback, errCallback)
    },
  },
  mounted() {
    if (this.column.title !== 'Add Section New Desidia') this.sectionName = this.column.title || ''
    // document.addEventListener('click', this.handleClickOutside);
  },
  created() {
    const roomId = this.getActiveProject?.slug;
    this.$soketio.emit('join', roomId);
  },
  beforeUnmount() {
    // document.removeEventListener('click', this.handleClickOutside);
    const roomId = this.getActiveProject?.slug;
    this.$soketio.emit('leave', roomId);
  },
};
</script>

<style scoped>
.newCol {
  background: linear-gradient(180deg,
      #e9effa 0%,
      rgba(233, 239, 250, 0.5) 100%);
  border-radius: 6px;
}

.drag {
  background-color: rgba(99, 95, 199, 0.95);
  color: white;
}

.ghost {
  background-color: rgba(129, 129, 129, 0.3);
  border-radius: 6px;
}
</style>
