<template>
    <!-- Table Section -->
    <div
        class="m-2 overflow-x-auto [&::-webkit-scrollbar]:h-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300">
        <div class="min-w-full inline-block align-middle">
            <!-- Table -->
            <table class="min-w-full divide-y divide-gray-200">
                <thead>
                    <tr>
                        <th scope="col" class="min-w-5">
                            <div
                                class="px-4 py-3 text-start flex items-center gap-x-1 text-sm font-medium text-gray-800">
                            </div>
                        </th>
                        <th scope="col" class="min-w-[150px]">
                            <div
                                class="pe-4 py-3 text-start flex items-center gap-x-1 text-sm font-medium text-gray-800">
                                <span> Job Title </span>
                                <span>
                                    <svg class="flex-shrink-0 w-3.5 h-3.5 text-gray-500 dark:text-gray-500"
                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <path d="m7 15 5 5 5-5" />
                                        <path d="m7 9 5-5 5 5" />
                                    </svg>
                                </span>
                            </div>
                        </th>
                        <th scope="col" class="min-w-10">
                            <div
                                class="px-4 py-3 text-start flex items-center gap-x-1 text-sm font-medium text-gray-800">
                                <span> Description </span>
                                <span>
                                    <svg class="flex-shrink-0 w-3.5 h-3.5 text-gray-500 dark:text-gray-500"
                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <path d="m7 15 5 5 5-5" />
                                        <path d="m7 9 5-5 5 5" />
                                    </svg>
                                </span>
                            </div>
                        </th>
                        <th scope="col">
                            <div
                                class="px-4 py-3 text-start flex items-center gap-x-1 text-sm font-medium text-gray-800">

                            </div>
                        </th>
                    </tr>
                </thead>

                <tbody class="divide-y divide-gray-200">
                    <tr v-for="(member, index) in (members)" :key="member.email">
                        <td class="size-px whitespace-nowrap pe-4 py-3">
                            <div class="w-full flex items-center gap-x-3">
                                <div class="grow">
                                    <span class="text-sm font-medium text-gray-800">
                                        {{ index + 1 }}
                                    </span>
                                </div>
                            </div>
                        </td>
                        <td class="size-px whitespace-nowrap pe-4 py-3">
                            <div class="w-full flex items-center gap-x-3">
                                <div class="grow">
                                    <span class="text-sm font-medium text-gray-800">
                                        {{ member.name }}
                                    </span>
                                </div>
                            </div>
                        </td>
                        <td class="size-px whitespace-nowrap px-4 py-3">
                            <span class="text-sm text-gray-600">
                                {{ member.role }}
                            </span>
                        </td>
                        <td class="size-px whitespace-nowrap px-4 py-3">

                        </td>
                    </tr>
                </tbody>
            </table>
            <!-- End Table -->
        </div>
    </div>
    <!-- End Table Section -->
    <!-- Footer -->
    <div class="mt-5 flex flex-wrap justify-between items-center gap-2">
        <p class="text-sm ml-4">
            <span class="font-medium text-stone-700 mr-1">27</span>
            <span class="font-medium text-stone-700">Results</span>
        </p>
        <!-- Pagination -->
        <nav class="flex justify-end items-center gap-x-1" aria-label="Pagination">
            <button type="button"
                class="min-h-[38px] min-w-[38px] py-2 px-2.5 inline-flex justify-center items-center gap-x-2 text-sm rounded-mdtext-stone-800 hover:bg-stone-100 
                disabled:opacity-50 disabled:pointer-events-none focus:outline-none"
                aria-label="Previous">
                <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                    stroke-linejoin="round">
                    <path d="m15 18-6-6 6-6"></path>
                </svg>
                <span class="sr-only">Previous</span>
            </button>
            <div class="flex items-center gap-x-1">
                <span
                    class="min-h-[38px] min-w-[38px] flex justify-center items-center bg-stone-800 text-stone-800 py-2 px-3 text-sm rounded-lg disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-700 dark:text-white"
                    aria-current="page">1</span>
                <span
                    class="min-h-[38px] flex justify-center items-center text-stone-500 py-2 px-1.5 text-sm dark:text-neutral-500">of</span>
                <span
                    class="min-h-[38px] flex justify-center items-center text-stone-500 py-2 px-1.5 text-sm dark:text-neutral-500">3</span>
            </div>
            <button type="button"
                class="min-h-[38px] min-w-[38px] py-2 px-2.5 inline-flex justify-center items-center gap-x-2 text-sm rounded-mdtext-stone-800 hover:bg-stone-100 
                disabled:opacity-50 disabled:pointer-events-none focus:outline-none"
                aria-label="Next">
                <span class="sr-only">Next</span>
                <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                    stroke-linejoin="round">
                    <path d="m9 18 6-6-6-6"></path>
                </svg>
            </button>
        </nav>
        <!-- End Pagination -->
    </div>
    <!-- End Footer -->
</template>

<script>
    export default {
        name: "userRole",
        data() {
            return {
                members: [{
                        name: 'Admin',
                        role: 'finance',
                        email: 'internal ',
                        lastActivity: 'Today',
                        status: 'Active',
                        avatar: 'https://images.unsplash.com/photo-1659482633369-9fe69af50bfb?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=3&w=320&h=320&q=80',
                        initials: null,
                    },
                    {
                        name: 'Liza Shols',
                        role: 'Can view',
                        email: '<EMAIL>',
                        lastActivity: '2 days ago',
                        status: 'Inactive',
                        avatar: null,
                        initials: 'L',
                    },
                ],
            }
        },
    }
</script>

<style scoped>
    /* Add any scoped styles here if necessary */
</style>