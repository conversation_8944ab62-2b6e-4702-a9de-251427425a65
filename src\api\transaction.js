import { client } from '@/libraries/http-client';

const endpoint = '/v1/transactions';

export default {
	checkout(creds, cb, errorCb) {
		const url = `${endpoint}/checkout`;
		client.post(url, creds)
			.then((response) => {
				if (cb) cb(response.data);
			})
			.catch((e) => {
				if (errorCb) errorCb(e);
			});
	},
	process(params, cb, errorCb) {
		const url = `${endpoint}/process`;
		client.post(url, params)
			.then((response) => {
				if (cb) cb(response.data);
			})
			.catch((e) => {
				if (errorCb) errorCb(e);
			});
	},
	// Get List
	getList(params, cb, errorCb) {
		const responseHandler = (response) => {
			if (cb) cb(response.data);
		};
		const errorHandler = (e) => {
			if (errorCb) errorCb(e);
		};
		const url = endpoint;
		client.get(url, { params })
			.then(responseHandler)
			.catch(errorHandler);
	},
	// Retrieve Invoice
	getInvoice(id, cb, errorCb) {
		const url = `${endpoint}/invoice/${id}`;
		const responseHandler = (response) => {
			if (cb) cb(response.data);
		};
		const errorHandler = (e) => {
			if (errorCb) errorCb(e);
		};
		client.get(url)
			.then(responseHandler)
			.catch(errorHandler);
	},
	// Retrieve Invoice
	getUpcomingInvoice(id, cb, errorCb) {
		const url = `${endpoint}/invoice/upcoming/${id}`;
		const responseHandler = (response) => {
			if (cb) cb(response.data);
		};
		const errorHandler = (e) => {
			if (errorCb) errorCb(e);
		};
		client.get(url)
			.then(responseHandler)
			.catch(errorHandler);
	},
	successed(params, cb, errorCb) {
		const url = `${endpoint}/successed`;
		client.post(url, params)
			.then((response) => {
				if (cb) cb(response.data);
			})
			.catch((e) => {
				if (errorCb) errorCb(e);
			});
	},

	statistic(cb, errorCb) {
		const url = `${endpoint}/statistic`;
		client.get(url)
			.then((response) => {
				if (cb) cb(response.data);
			})
			.catch((e) => {
				if (errorCb) errorCb(e);
			});
	},
	generateInvoice(params, cb, errorCb) {
		const url = `${endpoint}/generate/invoice`;
		client.post(url, params)
			.then((response) => {
				if (cb) cb(response.data);
			})
			.catch((e) => {
				if (errorCb) errorCb(e);
			});
	},

	successPaddle(params, cb, errorCb) {
		const url = `${endpoint}/paddle/successed`;
		client.post(url, params)
			.then((response) => {
				if (cb) cb(response.data);
			})
			.catch((e) => {
				if (errorCb) errorCb(e);
			});
	},

	generateInvoice(params, cb, errorCb) {
		const url = `${endpoint}/invoice/generate`;
		client.post(url, params)
			.then((response) => {
				if (cb) cb(response.data);
			})
			.catch((e) => {
				if (errorCb) errorCb(e);
			});
	},

	sendInvoice(id, cb, errorCb) {
		const url = `${endpoint}/invoice/${id}/send`;
		client.post(url, id)
			.then((response) => {
				if (cb) cb(response.data);
			})
			.catch((e) => {
				if (errorCb) errorCb(e);
			});
	},

	getInvoiceById(id, cb, errorCb) {
		const url = `${endpoint}/invoice/${id}/custom`;
		client.get(url, id)
			.then((response) => {
				if (cb) cb(response.data);
			})
			.catch((e) => {
				if (errorCb) errorCb(e);
			});
	},
};
