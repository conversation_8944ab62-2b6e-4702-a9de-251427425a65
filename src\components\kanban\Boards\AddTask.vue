<template>
  <div :id="id" class=" bg-white dark:bg-kb_dark_grey rounded-md p-2 shadow-sm border-gray-100 border-[1px] my-2 relative">
    <!-- Name / Title -->
    <kbInput ref="inputTitle" @blur="blur" @keyup.enter="blur"  v-model="name" :ph="ph" class="w-full pl-4">
    </kbInput>
    <!-- end name -->

    <!-- assignee / due date -->
    <div class="pl-2 mt-4 flex items-center">
      <AssigneeDirect @remove="assigneeRemoved" @click.stop="" :users="users" @select="assigneeSelected" class="w-full"></AssigneeDirect>
      <div class="absolute left-[50px]"><DatePickerTask @updateDatepicker="updateDatepicker"></DatePickerTask></div>
    </div>
  </div>
</template>

<script>
import {
    mapGetters,
    mapActions
  } from 'vuex';
  import {
    CheckCircleIcon,
  } from '@heroicons/vue/solid';
  import {
    CalendarIcon
  } from '@heroicons/vue/outline';
import { ref, toRaw, onMounted } from 'vue';
import { onKeyStroke } from '@vueuse/core';
import kbInput  from "@/components/kanban/kbInput.vue";
import store from '../../../pages/kanban/store.js';
import TButton from '@/components/global/Button.vue';
import AssigneeDirect  from "@/components/kanban/AssigneeDirect.vue";
import DatePickerTask  from "@/components/kanban/DatePickerTask.vue";
import tasksApi from "@/api/tasks";



export default {
  name: 'YourComponentName',
  components: {
    kbInput,
    CheckCircleIcon,
    CalendarIcon,
    TButton,
    AssigneeDirect,
    DatePickerTask
  },
  props: {
    users: {
      type: Array,
      default: [],
    },
    id: {},
    addDirectly: {},
    colIndex: {},
    position: {
      type: String,
      default: 'top',
    },

  },
  data() {
    return {
      ph: 'Task name',
      dueDate: '',
      assignTo: null,
      name: '',
      task: null,
    };
  },
  created() {
  },
  computed: {
    ...mapGetters({
      getOnlineUsers: 'application/getOnlineUsers',
      getActiveProject: 'application/getActiveProject',
      getActiveColumn: 'application/getActiveColumn',
      getActiveTask: 'application/getActiveTask',
    }),
    project() {
      return this.getActiveProject
    },
  },
  methods: {
    ...mapActions({
      clearOnlineUsers: 'application/clearOnlineUsers',
      setData: 'application/setData',
      changeStatus: 'application/changeStatus',
      updateTaskInStore: 'application/updateTaskInStore',
      resetStore: 'application/resetStore',
      resetStoreTask: 'application/resetStoreTask',
    }),
    blur(input) {
      if (this.task?.id) {
        this.updateTask();
        return
      }
      this.createTask();
    },
    assigneeSelected(event) {
      this.assignTo = event?.id
      if (this.task?.id) {
        this.updateTask();
      }
    },
    assigneeRemoved() {
      this.assignTo = null
      if (this.task?.id) {
        this.updateTask();
      }
    },
    updateDatepicker(date) {
      this.dueDate = date ? this.__dateFormatISO(date) : '';
      if (this.task?.id) {
        this.updateTask();
      }
    },
    
    createTask() {
      if (this.name) {
        const callback = (response) => {
          const data = response.data;
          this.task = data; // Store the newly created task
          // Add the new task to the store
          const newTask = {
            ... this.$store.state.application.task,
            ...data, // All task properties from the API response
            type:  this.$store.state.application.task.type, // Ensure the task has the correct type
            position: this.position,
            projectName: this.project.name,
          };
          this.$emit('afterTaskAdded', newTask);
          this.updateTaskInStore(newTask);
          this.resetStoreTask()
          this.$store.state.application.task.addDirectly = false;
          this.$emit('hideTask', false);
          this.assignTask()
        };

        const errCallback = (err) => {
          const message = err?.response?.data?.message;
          this.__showNotif('error', 'Error', message);
          this.$store.state.application.task.addDirectly = false;
          this.$emit('hideTask', false);
        };

        let params = {
          name: this.name,
          dueDate: this.dueDate,
          assignTo: this.assignTo,
          projectId: this.project?.id,
          type: this.$store.state.application.task.type
        };

        tasksApi.create(params, callback, errCallback);
      }
    },
    assignTask() {
      // Define the callback function that will be called after the API request is successful
      const callback = (response) => {
        const data = response.data;
      };

      // Define the error callback function to handle any errors during the API request
      const errCallback = (err) => {
        const message = err?.response?.data?.message || 'Error occurred while updating the task';
        this.__showNotif('error', 'Error', message);
        // this.handleErrors(err.response.data); //TODO: please check this function, nowhere else to be found in this file/global
      };

      const params = {
        assignTo: this.assignTo,
      }
      // Make the API request to update the task
      tasksApi.updateTask(this.task?.id, params, callback, errCallback);
    },
    updateTask() {
      const callback = (response) => {
        const data = response.data;
        this.task = data
        this.$store.state.application.task.addDirectly = false;
        this.$emit('hideTask', false);
      }
      const errCallback = (err) => {
        const message = err.response.data.message;
        this.__showNotif('error', 'Error', message || 'Error User');
      }
      let params = {
        name: this.name,
        assignTo: this.assignTo,
        projectId: this.project?.id,
        type: this.$store.state.application.task.type
      }
      if (this.dueDate) params.dueDate = this.__dateFormatISO(this.dueDate)

      tasksApi.update(this.task?.id, params, callback, errCallback)
    },

    close() {
      this.$emit('hideTask', false);
    },
  },
  mounted() {
    onKeyStroke('Escape', this.close);
  },
  beforeUnmount() {
	},
};
</script>

<style scoped></style>
