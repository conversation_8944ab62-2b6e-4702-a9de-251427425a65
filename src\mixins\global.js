import moment from 'moment';
import localforage from 'localforage';
import {
	getMediaBlob,
	has
} from '@/libraries/helper';
export const __duplicateVar = value => JSON.parse(JSON.stringify(value));
export default {
	computed: {
		__isShowLeftNavigation() {
			return this.$store.getters['navigation/isShowLeftNavigation'];
		},
	},
	methods: {
		__duplicateVar,
		__checkCache(key) {
			return new Promise((resolve, reject) => {
				localforage.getItem(key).then(async (value) => {
					if (value) {
						resolve(value);
					} else {
						resolve(false);
					}
				});
			});
		},
		__cacheTemplate(key, url, isReset = false) {
			return new Promise((resolve, reject) => {
				localforage.getItem(key).then(async (value) => {
					if (value && !isReset) {
						// console.log(isReset, 'here is reset');
						resolve(value);
					} else {
						const data = await (await fetch(url)).json();
						localforage.setItem(key, data).then(() => localforage.getItem(key)).then((value) => {
							resolve(value);
						}).catch((err) => {
							reject(err);
						});
					}
				});
			});
		},
		__cacheFont(key, url, isReset = false) {
			return new Promise((resolve, reject) => {
				localforage.getItem(key).then(async (value) => {
					if (value && !isReset) {
						// console.log(isReset, 'here is reset');
						resolve(value);
					} else {
						const response = await fetch(url);
						const fontBlob = await response.blob();
						localforage.setItem(key, fontBlob).then(() => localforage.getItem(key)).then((value) => {
							resolve(value);
						}).catch((err) => {
							reject(err);
						});
					}
				});
			});
		},
		__resetCache(key) {
			return new Promise((resolve, reject) => {
				localforage.removeItem(key);
				resolve();
			});

		},
		__cacheMedia(key, url, isReset = false) {
			return new Promise((resolve, reject) => {
				localforage.getItem(key).then(async (value) => {
					if (value && !isReset) {
						const mediaSource = URL.createObjectURL(value);
						resolve(mediaSource);
					} else {
						const data = await getMediaBlob(url);
						localforage.setItem(key, data).then(() => localforage.getItem(key)).then((value) => {
							const mediaSource = URL.createObjectURL(value);
							resolve(mediaSource);
						}).catch((err) => {
							reject(err);
						});
					}
				});
			});
		},
		__cacheMediaVideo(key, url, isReset = false, blob, expirationDate) {
			return new Promise((resolve, reject) => {
				localforage.getItem(key).then(async (value) => {
					if (value && !isReset) {
						const mediaSource = URL.createObjectURL(value);
						resolve(mediaSource);
					} else {
						let data = null;
						if (blob) {
							data = blob;
						} else {
							data = await getMediaBlob(url);
						}
						localforage.setItem(key, {
							data: data && has(data, 'data') ? data.data : data,
							expires: expirationDate.getTime() // Store the expiration date as a timestamp
						}).then(() => localforage.getItem(key)).then((value) => {
							console.log(typeof (value), value && has(value, 'data'), value.data instanceof Blob);
							try {
								const mediaSource = URL.createObjectURL(value.data);
								resolve(mediaSource);
							} catch (error) {
								console.log(error);
							}
						}).catch((err) => {
							reject(err);
						});
					}
				});
			});
		},

		__toTitleCase(text) {
			const result = text ? text.toUpperCase() : '';
			return result;
		},
		__showLeftNavigation() {
			this.$store.dispatch("navigation/setShowLeftNavigation", !this.__isShowLeftNavigation);
		},
		__showNotif(type = 'info', title = '', message = '', duration = 5000) { //default duration 5 sec
			this.$notify({
				group: "app",
				type: type,
				title: title,
				text: message,
				duration: duration,
			});
		},
		__showNotifWButton(type = 'info', title = '', message = '', btnCaption = '', url = '') {
			this.$notify({
				group: "button",
				type: type,
				title: title,
				text: message,
				data: {
					btnCaption,
					url
				}
			});
		},
    __decryptProjectData(encodedString) {
      try {
        const decodedString = atob(encodedString); // Decode the Base64 string
        const data = JSON.parse(decodedString); // Parse JSON string back into an object
        return data; // Return the decrypted data object (id and slug)
      } catch (error) {
        console.error('Decryption error:', error); // Handle any errors
        return null;
      }
    },
    __urlify(text) {
        let description = '';
        if (text) {
            description = text;
        }
        const urlRegex = /(https?:\/\/[^\s]+)/g;
        return description.replace(urlRegex, url => `<a href="${url}" target="blank">${url}</a>`);
    },
    __encryptProjectData(id, slug) {
      const data = { id, slug }; // Combine id and slug into an object
      const jsonString = JSON.stringify(data); // Convert object to JSON string
      return btoa(jsonString); // Encode JSON string using Base64
    },
		__dateHumanizeText(dateTime) {
			return moment(dateTime).fromNow();
		},
		__dateFormat(dateTime) {
			return moment(dateTime).format('Do MMMM YYYY');
		},
		__dateFormatISO(dateTime) {
			return moment(dateTime).format('YYYY-MM-DD');
		},
		__dateFormatProject(dateTime) {
			return moment(dateTime).format('DD MMM YYYY');
		},
    __dateFormatProjectKanban(dateTime, duration) {
      const startDateTime = moment(dateTime, 'YYYY-MM-DD HH:mm');
      const hourBooking = duration; // Dynamic value between 2 and 8
      const endDateTime = startDateTime.clone().add(hourBooking, 'hours');
    
      // Format the date as "D MMM YYYY"
      const formattedDate = startDateTime.format('D MMM. YYYY');
      // Format the time as "HH:mm"
      const formattedStartTime = startDateTime.format('HH:mm');
      const formattedEndTime = endDateTime.format('HH:mm');
    
      // Combine everything into the final string
      const finalString = `${formattedDate}, ${formattedStartTime} - ${formattedEndTime}`;
      
      return finalString;
    },
    
    __dateFormatDateFile(dateTime) {
			return moment(dateTime).format('MMM D, YYYY [at] h:mm a');
		},
		__dateFormatDateBooking(dateTime) {
			return moment(dateTime).format('YYYY-MM-DD HH:mm');
		},
		__dateFormatHuman(dateTime) {
			return moment(dateTime, "YYYY-MM-DD HH:mm").format("DD MMMM YYYY");
		},
    __dateFormatQuotation(dateTime, duration) {
      const startDateTime = moment(dateTime, 'YYYY-MM-DD HH:mm');
      const hourBooking = duration; // Could be a dynamic value between 2 and 8
      const endDateTime = startDateTime.clone().add(hourBooking, 'hours');
      const formattedDate = startDateTime.format('D MMMM YYYY');
      const formattedStartTime = startDateTime.format('HH:mm');
      const formattedEndTime = endDateTime.format('HH:mm');
      const finalString = `${formattedDate} - ${formattedStartTime} - ${formattedEndTime}`;
			return finalString;
		},
    __dateFormatOverview(dateTime, duration) {
      const startDateTime = moment(dateTime, 'YYYY-MM-DD HH:mm');
      const hourBooking = duration; // Could be a dynamic value between 2 and 8
      const endDateTime = startDateTime.clone().add(hourBooking, 'hours');
      const formattedDate = startDateTime.format('D MMMM YYYY');
      const formattedStartTime = startDateTime.format('HH:mm');
      const formattedEndTime = endDateTime.format('HH:mm');
      const finalString = `${formattedDate} <span class='ml-2'>${formattedStartTime} - ${formattedEndTime}</span>`;
			return finalString;
		},
		__dateFormatHome() {
			const today = moment();
			return today.format('dddd, D MMMM');
		},
		__getGreet() {
			// Get the current hour
			const currentHour = moment().hour();

			// Determine the time of day
			let timeOfDay;

			if (currentHour >= 5 && currentHour < 12) {
				timeOfDay = 'Morning';
			} else if (currentHour >= 12 && currentHour < 17) {
				timeOfDay = 'Afternoon';
			} else if (currentHour >= 17 && currentHour < 21) {
				timeOfDay = 'Evening';
			} else {
				timeOfDay = 'Evening';
			}
			return timeOfDay
		},
		__dateFormatDefaultProject(dateTime) {
			return moment(dateTime).format('DD.MM.YY');
		},
		__dateTimeFormat(dateTime) {
			return moment(dateTime).format('Do MMMM YYYY, hh:mm:ss');
		},
		__dateTimeFormatISO(dateTime) {
			return moment(dateTime).format('YYYY-MM-DD hh:mm:ss');
		},
		__dateTimeFormatISO(dateTime) {
			return moment(dateTime).format('DD.MM.YY - hh.mm');
		},
		__parseStringToArray(text) {
			const items = text && typeof text === 'string' ? JSON.parse(text) : [];
			return items;
		},
		__intToTime(seconds) {
			if (seconds < 60) {
				return new Date(seconds * 1000).toISOString().slice(17, 19);
			} else if (seconds >= 60 && seconds < 3600) {
				return new Date(seconds * 1000).toISOString().slice(14, 19);
			} else {
				return new Date(seconds * 1000).toISOString().slice(11, 19);
			}
		},
		__timeFormat(seconds) {
			if (seconds < 3600) {
				return new Date(seconds * 1000).toISOString().slice(14, 19);
			} else {
				return new Date(seconds * 1000).toISOString().slice(11, 19);
			}
		},
		__timeToSecond(time) {
			const hmsDuration = time;
			// check if hmsDuration split by : is 3
			if (hmsDuration.split(':').length === 3) {
				const [hrsDu, minutesDu, secondsDu] = hmsDuration.split(':');
				return (+hrsDu) * 60 * 60 + (+minutesDu) * 60 + (+secondsDu);
			} else {
				const [minutesDu, secondsDu] = hmsDuration.split(':');
				return (+minutesDu) * 60 + (+secondsDu);
			}
		},
    __currencyWithoutSymbol(amount) {
      return amount.toLocaleString('en-US');
		},
		__convertCurrency(price, currency = 'USD') {
			return new Intl.NumberFormat('en-US', {
				style: 'currency',
				currency: currency
			}).format(price);
		},
		__convertCurrencyUSD(price) {
			return new Intl.NumberFormat('en-US', {
				style: 'currency',
				currency: 'USD'
			}).format(price);
		},
		__generateInitial(text) {
			const words = text ? text.split(' ') : ['AN'];
      let initial = '';

      const getFirstAlphabets = (wordsArray) => {
          let chars = [];
          wordsArray.forEach(word => {
              const match = word.match(/[A-Za-z]/);
              if (match) {
                  chars.push(match[0]);
              }
          });
          return chars;
      };

      const firstAlphabets = getFirstAlphabets(words);

      if (firstAlphabets.length >= 2) {
          initial = `${firstAlphabets[0]}${firstAlphabets[1]}`;
      } else if (firstAlphabets.length === 1) {
          initial = firstAlphabets[0];
      } else {
          initial = 'AN';
      }

      return initial.toUpperCase();
		},
		__getColorByInitial(char) {
			const colorMap = {
				A: '#65FDB7',
				B: '#C7E4B4',
				C: '#FFC0AE',
				D: '#FFF4DE',
				E: '#A1D0BE',
				F: '#FAD65D',
				G: '#7BFF81',
				H: '#DBFFAA',
				I: '#F7F788',
				J: '#D8C5F3',
				K: '#00D8E5',
				L: '#FF9DFF',
				M: '#EBB287',
				N: '#FFC9CE',
				O: '#E2FF00',
				P: '#ACFFEF',
				Q: '#A6F8B5',
				R: '#10EAB9',
				S: '#F8D2A4',
				T: '#A2FF69',
				U: '#E7FFC5',
				V: '#CED29C',
				W: '#A4B4FA',
				X: '#22CDDB',
				Y: '#FBFFAA',
				Z: '#FFAB0D'
			};

			// Convert the first character to uppercase
			const upperChar = char.toUpperCase();

			// Return the color from the map or a default color if not found
			return colorMap[upperChar] || '#FFFFFF'; // Default to white if not found
		},
		__generateInitialCanvas(name) {
			const init = this.__generateInitial(name)
			const initials = init;
			let bgColor = this.__getColorByInitial(name[0]);

			// Set canvas size and DPI
			const canvas = document.createElement("canvas");
			const size = 100; // Logical size of the canvas
			const dpi = window.devicePixelRatio || 1; // Use device pixel ratio for sharper images
			canvas.width = size * dpi;
			canvas.height = size * dpi;
			canvas.style.width = `${size}px`;
			canvas.style.height = `${size}px`;

			const ctx = canvas.getContext("2d");

			// Scale the context to handle high DPI displays
			ctx.scale(dpi, dpi);

			// Background
			ctx.fillStyle = bgColor;
			ctx.fillRect(0, 0, size, size);

			// Text
			ctx.fillStyle = "#000000";
			ctx.font = `${size / 2.2}px Arial`; // Font size set relative to canvas size
			ctx.textAlign = "center";
			ctx.textBaseline = "middle";

			// Use sub-pixel rendering by adding fractional pixel offsets
			ctx.fillText(initials, size / 2 + 0.5, size / 2 + 0.5);

			return canvas.toDataURL();
		},

    __downloadFile(url) {
      // Create a temporary anchor element
      const link = document.createElement('a');
      link.href = url;

      // Set the download attribute with a filename
      link.setAttribute('download', 'downloaded-image.jpg');

      // Append the link to the body
      document.body.appendChild(link);

      // Trigger the download by simulating a click
      link.click();

      // Clean up by removing the anchor element
      document.body.removeChild(link);
    }

	},
	mounted() {}
};