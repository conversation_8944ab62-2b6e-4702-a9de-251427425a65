import { client } from '@/libraries/http-client';
import { buildQuery } from '@/libraries/helper';
import axios from 'axios';
const endpoint = '/v1/bites';


export default {
	// Get List
	getList(params, cb, errorCb) {
		const responseHandler = (response) => {
			if (cb) cb(response.data);
		};
		const errorHandler = (e) => {
			if (errorCb) errorCb(e);
		};
		const query = buildQuery(params);
		const url = `${endpoint}?${query}`;
		client.get(url)
			.then(responseHandler)
			.catch(errorHandler);
	},

	// Create
	create(params, cb, errorCb) {
		const responseHandler = (response) => {
			if (cb) cb(response.data);
		};
		const errorHandler = (e) => {
			if (errorCb) errorCb(e);
		};
		const url = `${endpoint}`;
		client.post(url, params)
			.then(responseHandler)
			.catch(errorHandler);
	},

	// Update
	update(id, params, cb, errorCb) {
		const url = `${endpoint}/${id}`;
		client.put(url, params)
			.then((response) => {
				cb(response.data);
			})
			.catch((e) => {
				if (errorCb) {
					errorCb(e);
				}
			});
	},
	// regenerate access token
	regenerate(id, cb, errorCb) {
		const url = `${endpoint}/regenerate/${id}`;
		client.put(url)
			.then((response) => {
				if (cb) cb(response.data);
			})
			.catch((e) => {
				if (errorCb) {
					errorCb(e);
				}
			});
	},
	// Get details
	get(id, cb, errorCb) {
		const cancelToken = axios.CancelToken.source();

		const url = `${endpoint}/${id}`;
		client.get(url, { cancelToken: cancelToken.token })
			.then((response) => {
				if (cb) cb(response.data);
			})
			.catch((e) => {
				errorCb(e);
			});
		return cancelToken;

	},
	// Get details
	getShareableDetail(params, cb, errorCb) {
		const query = buildQuery(params);
		const url = `${endpoint}/shareable/detail?${query}`;
		client.get(url)
			.then((response) => {
				if (cb) cb(response.data);
			})
			.catch((e) => {
				errorCb(e);
			});
	},
	// Get details
	getGeneratedUrl(id, cb, errorCb) {
		const url = `/v1/b/${id}`;
		client.get(url)
			.then((response) => {
				if (cb) cb(response.data);
			})
			.catch((e) => {
				errorCb(e);
			});
	},

	// Get details by slug
	getBySlug(slug, cb, errorCb) {
		const url = `${endpoint}/slug/${slug}`;
		client.get(url)
			.then((response) => {
				if (cb) cb(response.data);
			})
			.catch((e) => {
				errorCb(e);
			});
	},

	// Delete
	delete(id, cb, errorCb) {
		const url = `${endpoint}/${id}`;
		client.delete(url)
			.then((response) => {
				if (cb) cb(response.data);
			})
			.catch((e) => {
				if (errorCb) errorCb(e);
			});
	},
	// reorder
	reorder(params, cb, errorCb) {
		const responseHandler = (response) => {
			if (cb) cb(response.data);
		};
		const errorHandler = (e) => {
			if (errorCb) errorCb(e);
		};
		const url = `${endpoint}/reorder`;
		client.post(url, params)
			.then(responseHandler)
			.catch(errorHandler);
	},
	// reorder
	duplicate(id, cb, errorCb) {
		const responseHandler = (response) => {
			if (cb) cb(response.data);
		};
		const errorHandler = (e) => {
			if (errorCb) errorCb(e);
		};
		const url = `${endpoint}/duplicate/${id}`;
		client.post(url)
			.then(responseHandler)
			.catch(errorHandler);
	},

	render(id, type, frame, channel, currentActiveScene=0, isNewSchema=false, is_fade, cb, errorCb) {
		const responseHandler = (response) => {
			if (cb) cb(response.data);
		};
		const errorHandler = (e) => {
			if (errorCb) errorCb(e);
		};
		const url = `${endpoint}/render/${id}/${type}/${frame}?channel=${channel}&currentActiveScene=${currentActiveScene}&isNewSchema=${isNewSchema}&is_fade=${is_fade}`;
		client.get(url)
			.then(responseHandler)
			.catch(errorHandler);
	},
	customRender(params, header, cb, errorCb) {
		const responseHandler = (response) => {
			if (cb) cb(response.data);
		};
		const errorHandler = (e) => {
			if (errorCb) errorCb(e);
		};
		const url = `${import.meta.env.VITE_API_URL}/r/`;
		
		axios.post(url,  params, {
			headers: header,
		})
			.then(responseHandler)
			.catch(errorHandler);
	},
	cancelRender(uid, cb, errorCb) {
		const url = `v1/progress/${uid}`;
		client.delete(url)
			.then((response) => {
				if (cb) cb(response.data);
			})
			.catch((e) => {
				if (errorCb) errorCb(e);
			});
	},
	// Export
	export(id, cb, errorCb) {
		const url = `${endpoint}/export/${id}`;
		const responseHandler = (response) => {
			if (cb) cb(response.data);
		};
		const errorHandler = (e) => {
			if (errorCb) errorCb(e);
		};
		client.get(url)
			.then(responseHandler)
			.catch(errorHandler);
	},
	// Import
	import(params, cb, errorCb) {
		const url = `${endpoint}/import`;
		const responseHandler = (response) => {
			if (cb) cb(response.data);
		};
		const errorHandler = (e) => {
			if (errorCb) errorCb(e);
		};
		client.post(url, params)
			.then(responseHandler)
			.catch(errorHandler);
	},
	// get shareable url
	getShareAbleURL(id, cb, errorCb) {
		const url = `${endpoint}/shareableUrl/${id}`;
		client.get(url)
			.then((response) => {
				if (cb) cb(response.data);
			})
			.catch((e) => {
				errorCb(e);
			});
	},
	
	// get scene url
	getSceneData(id, type, options, cb, errorCb) {
		const url = `${endpoint}/sceneData/${id}/${type}?${options}`;
		client.get(url)
			.then((response) => {
				if (cb) cb(response.data);
			})
			.catch((e) => {
				errorCb(e);
			});
	},

	// get scene url
	getScenesData(id, type, header, cb, errorCb) {
		const responseHandler = (response) => {
			if (cb) cb(response.data);
		};
		const errorHandler = (e) => {
			if (errorCb) errorCb(e);
		};

		const url = `${import.meta.env.VITE_API_URL}/api/example/${id}?type=${type}`;
		
		axios.get(url, {
			headers: { 'Authorization': header},
		})
			.then(responseHandler)
			.catch(errorHandler);
	},

	// get scene url
	renderExample(params, header, cb, errorCb) {
		const responseHandler = (response) => {
			if (cb) cb(response.data);
		};
		const errorHandler = (e) => {
			if (errorCb) errorCb(e);
		};

		const url = `${import.meta.env.VITE_API_URL}/api/render`;
		
		axios.post(url, params, {
			headers: { 'Authorization': header},
		})
			.then(responseHandler)
			.catch(errorHandler);

	},
	
	// render bul
	renderBulk(params, cb, errorCb) {
		const responseHandler = (response) => {
			if (cb) cb(response.data);
		};
		const errorHandler = (e) => {
			if (errorCb) errorCb(e);
		};
		const url = `${endpoint}/render/bulk`;
		client.post(url, params)
			.then(responseHandler)
			.catch(errorHandler);
	},

	// saving group
	createGroup(params, cb, errorCb) {
		const responseHandler = (response) => {
			if (cb) cb(response.data);
		};
		const errorHandler = (e) => {
			if (errorCb) errorCb(e);
		};
		const url = `${endpoint}/groups`;
		client.post(url, params)
			.then(responseHandler)
			.catch(errorHandler);
	},

	// saving group
	createGroup(params, cb, errorCb) {
		const responseHandler = (response) => {
			if (cb) cb(response.data);
		};
		const errorHandler = (e) => {
			if (errorCb) errorCb(e);
		};
		const url = `${endpoint}/groups`;
		client.post(url, params)
			.then(responseHandler)
			.catch(errorHandler);
	},

	checkVariant(id, cb, errorCb) {
		const responseHandler = (response) => {
			if (cb) cb(response.data);
		};
		const errorHandler = (e) => {
			if (errorCb) errorCb(e);
		};
		const url = `${endpoint}/render/allSizes/check/${id}`;
		client.get(url)
			.then(responseHandler)
			.catch(errorHandler);
	},

	// article video
	generateChatGpt(params, cb, errorCb) {
		const cancelToken = axios.CancelToken.source();
		const responseHandler = (response) => {
			if (cb) cb(response.data);
		};
		const errorHandler = (e) => {
			if (errorCb) errorCb(e);
		};
		const url = `${endpoint}/article/summaries`;
		client.post(url, params, { cancelToken: cancelToken.token })
			.then(responseHandler)
			.catch(errorHandler);

		return cancelToken;
	},
};
