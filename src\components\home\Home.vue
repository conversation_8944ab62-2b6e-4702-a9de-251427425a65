<template>
	<div class="font-medium">
		{{ __dateFormatHome() }}
	</div>
	<div class="font-medium text-2xl">Good {{ __getGreet() }}, {{user?.fullName}}</div>
	<!-- based on role -->
	<General></General>
</template>

<script>
	import {
		mapGetters
	} from 'vuex';
	import General from '@/components/home/<USER>';
	import Client from '@/components/home/<USER>';
	import User from '@/components/home/<USER>';

	/* eslint-disable vue/html-closing-bracket-spacing */
	export default {
		components: {
			General,
			User,
			Client,
		},
		props: {},
		data() {
			return {};
		},
		computed: {
			...mapGetters({
				user: 'auth/user',
			})
		},
		watch: {},
		created() {
			
		},
		mounted() {},
		beforeUnmount() {},
		methods: {},
	};
</script>