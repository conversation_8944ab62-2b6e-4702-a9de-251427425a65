<template>
  <div class="h-full bg-white flex flex-col overflow-hidden">
    <!-- Header -->
    <NotebookHeader 
      :title="notebook?.title || 'Untitled Notebook'" 
      :notebookId="notebookId"
      @back-to-dashboard="$emit('back-to-dashboard')"
      @title-updated="handleTitleUpdate"
    />
    
    <!-- Main Content Area -->
    <div class="flex-1 flex overflow-hidden">
      <!-- Desktop Layout (3-column) -->
      <div v-if="isDesktop" class="flex w-full h-full">
        <!-- Sources Sidebar -->
        <div :class="`${sourcesWidth} flex-shrink-0`">
          <SourcesSidebar 
            :hasSource="hasSource" 
            :notebookId="notebookId"
            :selectedCitation="selectedCitation"
            @citation-close="handleCitationClose"
            @citation-click="handleCitationClick"
          />
        </div>
        
        <!-- Chat Area -->
        <div :class="`${chatWidth} flex-shrink-0`">
          <ChatArea 
            :hasSource="hasSource" 
            :notebookId="notebookId"
            :notebook="notebook"
            @citation-click="handleCitationClick"
          />
        </div>
        
        <!-- Studio Sidebar -->
        <div :class="`${studioWidth} flex-shrink-0`">
          <StudioSidebar 
            :notebookId="notebookId" 
            @citation-click="handleCitationClick"
          />
        </div>
      </div>
      
      <!-- Mobile/Tablet Layout (tabs) -->
      <div v-else class="w-full h-full">
        <MobileNotebookTabs
          :hasSource="hasSource"
          :notebookId="notebookId"
          :notebook="notebook"
          :selectedCitation="selectedCitation"
          @citation-close="handleCitationClose"
          @citation-click="handleCitationClick"
        />
      </div>
    </div>
  </div>
</template>

<script>
import NotebookHeader from './NotebookHeader.vue';
import SourcesSidebar from './SourcesSidebar.vue';
import ChatArea from './ChatArea.vue';
import StudioSidebar from './StudioSidebar.vue';
import MobileNotebookTabs from './MobileNotebookTabs.vue';

export default {
  name: 'NotebookView',
  components: {
    NotebookHeader,
    SourcesSidebar,
    ChatArea,
    StudioSidebar,
    MobileNotebookTabs,
  },
  props: {
    notebookId: {
      type: String,
      required: true
    },
    notebook: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      selectedCitation: null,
      sources: [],
      isLoadingSources: false
    };
  },
  computed: {
    isDesktop() {
      // Use a breakpoint similar to the original
      return window.innerWidth >= 1100;
    },
    
    hasSource() {
      return this.sources && this.sources.length > 0;
    },
    
    isSourceDocumentOpen() {
      return !!this.selectedCitation;
    },
    
    // Dynamic width calculations for desktop
    sourcesWidth() {
      return this.isSourceDocumentOpen ? 'w-[35%]' : 'w-[25%]';
    },
    
    studioWidth() {
      return 'w-[30%]';
    },
    
    chatWidth() {
      return this.isSourceDocumentOpen ? 'w-[35%]' : 'w-[45%]';
    }
  },
  created() {
    this.loadSources();
    // Listen for window resize to update desktop/mobile view
    window.addEventListener('resize', this.handleResize);
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    async loadSources() {
      if (!this.notebookId) return;

      this.isLoadingSources = true;
      try {
        // Simulate API call - replace with actual implementation
        await new Promise(resolve => setTimeout(resolve, 500));

        // Mock sources data - start with some sources for demo
        this.sources = [
          {
            id: '1',
            title: 'Research Document.pdf',
            type: 'pdf',
            notebook_id: this.notebookId,
            created_at: new Date().toISOString()
          },
          {
            id: '2',
            title: 'Project Overview.docx',
            type: 'docx',
            notebook_id: this.notebookId,
            created_at: new Date(Date.now() - 86400000).toISOString()
          }
        ];
      } catch (error) {
        console.error('Failed to load sources:', error);
        this.__showNotif('error', 'Error', 'Failed to load sources');
      } finally {
        this.isLoadingSources = false;
      }
    },
    
    handleCitationClick(citation) {
      this.selectedCitation = citation;
    },
    
    handleCitationClose() {
      this.selectedCitation = null;
    },
    
    handleTitleUpdate(newTitle) {
      // Update the notebook title
      if (this.notebook) {
        this.notebook.title = newTitle;
      }
    },
    
    handleResize() {
      // Force reactivity update for desktop/mobile detection
      this.$forceUpdate();
    }
  }
};
</script>
