<template>
  <div class="w-full h-full bg-white flex flex-col overflow-hidden">
    <!-- Header -->
    <NotebookHeader 
      :title="notebook?.title || 'Untitled Notebook'" 
      :notebookId="notebookId"
      @back-to-dashboard="$emit('back-to-dashboard')"
      @title-updated="handleTitleUpdate"
    />
    
    <!-- Main Content Area -->
    <div class="flex-1 flex overflow-hidden">
      <!-- Desktop Layout (3-column) with max-width constraint -->
      <div v-if="isDesktop" class="flex w-full h-full relative max-w-[1600px] mx-auto">
        <!-- Sources Sidebar - Always visible -->
        <div :class="`${sourcesWidth} flex-shrink-0`">
          <SourcesSidebar
            :hasSource="hasSource"
            :notebookId="notebookId"
            :selectedCitation="selectedCitation"
            @citation-close="handleCitationClose"
            @citation-click="handleCitationClick"
          />
        </div>

        <!-- Chat Area -->
        <div :class="`${chatWidth} flex-shrink-0`">
          <ChatArea
            :hasSource="hasSource"
            :notebookId="notebookId"
            :notebook="notebook"
            @citation-click="handleCitationClick"
          />
        </div>

        <!-- Studio Sidebar - Always visible -->
        <div :class="`${studioWidth} flex-shrink-0`">
          <StudioSidebar
            :notebookId="notebookId"
            @citation-click="handleCitationClick"
          />
        </div>
      </div>
      
      <!-- Mobile/Tablet Layout (tabs) -->
      <div v-else class="w-full h-full">
        <MobileNotebookTabs
          :hasSource="hasSource"
          :notebookId="notebookId"
          :notebook="notebook"
          :selectedCitation="selectedCitation"
          @citation-close="handleCitationClose"
          @citation-click="handleCitationClick"
        />
      </div>
    </div>
  </div>
</template>

<script>
import NotebookHeader from './NotebookHeader.vue';
import SourcesSidebar from './SourcesSidebar.vue';
import ChatArea from './ChatArea.vue';
import StudioSidebar from './StudioSidebar.vue';
import MobileNotebookTabs from './MobileNotebookTabs.vue';

export default {
  name: 'NotebookView',
  components: {
    NotebookHeader,
    SourcesSidebar,
    ChatArea,
    StudioSidebar,
    MobileNotebookTabs,
  },
  props: {
    notebookId: {
      type: String,
      required: true
    },
    notebook: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      selectedCitation: null,
      sources: [],
      isLoadingSources: false,
      windowWidth: typeof window !== 'undefined' ? window.innerWidth : 1200,
      isInitialized: false
    };
  },
  computed: {
    isDesktop() {
      // Use a more responsive breakpoint for better layout
      return this.windowWidth >= 1200;
    },
    
    hasSource() {
      return this.sources && this.sources.length > 0;
    },
    
    isSourceDocumentOpen() {
      return !!this.selectedCitation;
    },
    
    // Dynamic width calculations for desktop - like insights-lm-public
    sourcesWidth() {
      // Always visible, expand when source document is open
      return this.isSourceDocumentOpen ? 'w-[35%]' : 'w-[25%]';
    },

    studioWidth() {
      // Always visible with fixed width
      return 'w-[30%]';
    },

    chatWidth() {
      // Calculate chat width based on source document state
      return this.isSourceDocumentOpen ? 'w-[35%]' : 'w-[45%]';
    }
  },
  created() {
    this.initializeLayout();
    this.loadSources();
    // Listen for window resize to update desktop/mobile view
    window.addEventListener('resize', this.handleResize);
  },

  mounted() {
    // Ensure proper layout after component is mounted
    this.$nextTick(() => {
      this.handleResize();
      this.isInitialized = true;
    });
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    getInitialSidebarState(type) {
      // Determine initial sidebar state based on screen size
      if (typeof window === 'undefined') return true;

      const width = window.innerWidth;

      if (width >= 1600) {
        // Ultra-wide screens: show both sidebars
        return true;
      } else if (width >= 1400) {
        // Wide screens: show sources, hide studio initially
        return type === 'sources';
      } else if (width >= 1200) {
        // Medium screens: show sources only
        return type === 'sources';
      } else {
        // Small screens: hide both initially
        return false;
      }
    },

    initializeLayout() {
      // Set up initial layout state
      this.windowWidth = typeof window !== 'undefined' ? window.innerWidth : 1200;

      // Apply responsive sidebar states
      this.isSourcesSidebarVisible = this.getInitialSidebarState('sources');
      this.isStudioSidebarVisible = this.getInitialSidebarState('studio');
    },

    async loadSources() {
      if (!this.notebookId) return;

      this.isLoadingSources = true;
      try {
        // Simulate API call - replace with actual implementation
        await new Promise(resolve => setTimeout(resolve, 500));

        // Mock sources data - start with some sources for demo
        this.sources = [
          {
            id: '1',
            title: 'Research Document.pdf',
            type: 'pdf',
            notebook_id: this.notebookId,
            created_at: new Date().toISOString()
          },
          {
            id: '2',
            title: 'Project Overview.docx',
            type: 'docx',
            notebook_id: this.notebookId,
            created_at: new Date(Date.now() - 86400000).toISOString()
          }
        ];
      } catch (error) {
        console.error('Failed to load sources:', error);
        this.__showNotif('error', 'Error', 'Failed to load sources');
      } finally {
        this.isLoadingSources = false;
      }
    },
    
    handleCitationClick(citation) {
      console.log('NotebookView: Citation clicked, setting selectedCitation', {
        citationId: citation.citation_id,
        sourceId: citation.source_id,
        sourceTitle: citation.source_title,
        chunkLinesFrom: citation.chunk_lines_from,
        chunkLinesTo: citation.chunk_lines_to,
        isFromSourceList: citation.citation_id === -1
      });

      // Set the selected citation to automatically open the source content viewer
      this.selectedCitation = citation;
    },
    
    handleCitationClose() {
      console.log('NotebookView: Citation closed, clearing selectedCitation');
      this.selectedCitation = null;
    },
    
    handleTitleUpdate(newTitle) {
      // Update the notebook title
      if (this.notebook) {
        this.notebook.title = newTitle;
      }
    },
    
    handleResize() {
      const oldWidth = this.windowWidth;
      this.windowWidth = window.innerWidth;

      // Only auto-adjust on initial load or significant breakpoint changes
      if (!this.isInitialized) {
        return;
      }

      // Handle breakpoint transitions more gracefully
      const wasSmall = oldWidth < 1200;
      const isSmall = this.windowWidth < 1200;
      const wasMedium = oldWidth >= 1200 && oldWidth < 1400;
      const isMedium = this.windowWidth >= 1200 && this.windowWidth < 1400;
      const wasLarge = oldWidth >= 1400;
      const isLarge = this.windowWidth >= 1400;

      // Only auto-adjust when crossing major breakpoints
      if (wasSmall && (isMedium || isLarge)) {
        // Moving from small to larger: show sources
        this.isSourcesSidebarVisible = true;
        if (isLarge) {
          this.isStudioSidebarVisible = true;
        }
      } else if ((wasMedium || wasLarge) && isSmall) {
        // Moving to small screen: hide both for mobile experience
        this.isSourcesSidebarVisible = false;
        this.isStudioSidebarVisible = false;
      }
    },


  }
};
</script>

<style scoped>
/* Prevent layout shift during initialization */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Ensure smooth width transitions */
.flex-1 {
  transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1), flex 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Prevent content jumping during sidebar transitions */
.min-w-0 {
  min-width: 0;
}

/* Smooth button transitions */
button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

button:hover {
  transform: scale(1.05);
}

/* Ensure proper layout constraints */
.max-w-\[1920px\] {
  max-width: 1920px;
}

.max-w-\[1200px\] {
  max-width: 1200px;
}

.max-w-\[800px\] {
  max-width: 800px;
}
</style>
