<template>
  <div class="h-full bg-white flex flex-col overflow-hidden">
    <!-- Header -->
    <NotebookHeader 
      :title="notebook?.title || 'Untitled Notebook'" 
      :notebookId="notebookId"
      @back-to-dashboard="$emit('back-to-dashboard')"
      @title-updated="handleTitleUpdate"
    />
    
    <!-- Main Content Area -->
    <div class="flex-1 flex overflow-hidden">
      <!-- Desktop Layout (3-column) -->
      <div v-if="isDesktop" class="flex w-full h-full relative">
        <!-- Sources Sidebar -->
        <div v-if="isSourcesSidebarVisible" :class="`${sourcesWidth} flex-shrink-0 transition-all duration-300`">
          <SourcesSidebar
            :hasSource="hasSource"
            :notebookId="notebookId"
            :selectedCitation="selectedCitation"
            @citation-close="handleCitationClose"
            @citation-click="handleCitationClick"
          />
        </div>

        <!-- Sources Toggle Button -->
        <button
          @click="toggleSourcesSidebar"
          class="absolute top-1/2 transform -translate-y-1/2 z-10 bg-white border border-gray-300 rounded-md p-2 shadow-sm hover:bg-gray-50 transition-all duration-300"
          :class="{
            'left-2': !isSourcesSidebarVisible,
            'left-[calc(25%-1rem)]': isSourcesSidebarVisible && !isSourceDocumentOpen,
            'left-[calc(35%-1rem)]': isSourcesSidebarVisible && isSourceDocumentOpen
          }"
          :title="isSourcesSidebarVisible ? 'Hide Sources' : 'Show Sources'"
        >
          <svg class="h-4 w-4 text-gray-600 transition-transform duration-300" :class="{ 'rotate-180': !isSourcesSidebarVisible }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>

        <!-- Chat Area -->
        <div :class="`${chatWidth} flex-shrink-0 transition-all duration-300`">
          <ChatArea
            :hasSource="hasSource"
            :notebookId="notebookId"
            :notebook="notebook"
            @citation-click="handleCitationClick"
          />
        </div>

        <!-- Studio Toggle Button -->
        <button
          @click="toggleStudioSidebar"
          class="absolute top-1/2 transform -translate-y-1/2 z-10 bg-white border border-gray-300 rounded-md p-2 shadow-sm hover:bg-gray-50 transition-all duration-300"
          :class="{
            'right-2': !isStudioSidebarVisible,
            'right-[calc(30%-1rem)]': isStudioSidebarVisible
          }"
          :title="isStudioSidebarVisible ? 'Hide Studio' : 'Show Studio'"
        >
          <svg class="h-4 w-4 text-gray-600 transition-transform duration-300" :class="{ 'rotate-180': !isStudioSidebarVisible }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </button>

        <!-- Studio Sidebar -->
        <div v-if="isStudioSidebarVisible" :class="`${studioWidth} flex-shrink-0 transition-all duration-300`">
          <StudioSidebar
            :notebookId="notebookId"
            @citation-click="handleCitationClick"
          />
        </div>
      </div>
      
      <!-- Mobile/Tablet Layout (tabs) -->
      <div v-else class="w-full h-full">
        <MobileNotebookTabs
          :hasSource="hasSource"
          :notebookId="notebookId"
          :notebook="notebook"
          :selectedCitation="selectedCitation"
          @citation-close="handleCitationClose"
          @citation-click="handleCitationClick"
        />
      </div>
    </div>
  </div>
</template>

<script>
import NotebookHeader from './NotebookHeader.vue';
import SourcesSidebar from './SourcesSidebar.vue';
import ChatArea from './ChatArea.vue';
import StudioSidebar from './StudioSidebar.vue';
import MobileNotebookTabs from './MobileNotebookTabs.vue';

export default {
  name: 'NotebookView',
  components: {
    NotebookHeader,
    SourcesSidebar,
    ChatArea,
    StudioSidebar,
    MobileNotebookTabs,
  },
  props: {
    notebookId: {
      type: String,
      required: true
    },
    notebook: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      selectedCitation: null,
      sources: [],
      isLoadingSources: false,
      isSourcesSidebarVisible: true,
      isStudioSidebarVisible: true
    };
  },
  computed: {
    isDesktop() {
      // Use a breakpoint similar to the original
      return window.innerWidth >= 1100;
    },
    
    hasSource() {
      return this.sources && this.sources.length > 0;
    },
    
    isSourceDocumentOpen() {
      return !!this.selectedCitation;
    },
    
    // Dynamic width calculations for desktop
    sourcesWidth() {
      if (!this.isSourcesSidebarVisible) return 'w-0';
      return this.isSourceDocumentOpen ? 'w-[35%]' : 'w-[25%]';
    },

    studioWidth() {
      if (!this.isStudioSidebarVisible) return 'w-0';
      return 'w-[30%]';
    },

    chatWidth() {
      // Calculate chat width based on visible sidebars
      const sourcesVisible = this.isSourcesSidebarVisible;
      const studioVisible = this.isStudioSidebarVisible;

      if (!sourcesVisible && !studioVisible) {
        return 'w-full';
      } else if (!sourcesVisible && studioVisible) {
        return 'w-[70%]';
      } else if (sourcesVisible && !studioVisible) {
        return this.isSourceDocumentOpen ? 'w-[65%]' : 'w-[75%]';
      } else {
        // Both sidebars visible
        return this.isSourceDocumentOpen ? 'w-[35%]' : 'w-[45%]';
      }
    }
  },
  created() {
    this.loadSources();
    // Listen for window resize to update desktop/mobile view
    window.addEventListener('resize', this.handleResize);
    // Add keyboard shortcuts for sidebar toggles
    window.addEventListener('keydown', this.handleKeyboardShortcuts);
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.handleResize);
    window.removeEventListener('keydown', this.handleKeyboardShortcuts);
  },
  methods: {
    async loadSources() {
      if (!this.notebookId) return;

      this.isLoadingSources = true;
      try {
        // Simulate API call - replace with actual implementation
        await new Promise(resolve => setTimeout(resolve, 500));

        // Mock sources data - start with some sources for demo
        this.sources = [
          {
            id: '1',
            title: 'Research Document.pdf',
            type: 'pdf',
            notebook_id: this.notebookId,
            created_at: new Date().toISOString()
          },
          {
            id: '2',
            title: 'Project Overview.docx',
            type: 'docx',
            notebook_id: this.notebookId,
            created_at: new Date(Date.now() - 86400000).toISOString()
          }
        ];
      } catch (error) {
        console.error('Failed to load sources:', error);
        this.__showNotif('error', 'Error', 'Failed to load sources');
      } finally {
        this.isLoadingSources = false;
      }
    },
    
    handleCitationClick(citation) {
      this.selectedCitation = citation;
    },
    
    handleCitationClose() {
      this.selectedCitation = null;
    },
    
    handleTitleUpdate(newTitle) {
      // Update the notebook title
      if (this.notebook) {
        this.notebook.title = newTitle;
      }
    },
    
    handleResize() {
      // Force reactivity update for desktop/mobile detection
      this.$forceUpdate();
    },

    toggleSourcesSidebar() {
      this.isSourcesSidebarVisible = !this.isSourcesSidebarVisible;
      const action = this.isSourcesSidebarVisible ? 'shown' : 'hidden';
      this.__showNotif('info', 'Sources Sidebar', `Sources sidebar ${action}`);
    },

    toggleStudioSidebar() {
      this.isStudioSidebarVisible = !this.isStudioSidebarVisible;
      const action = this.isStudioSidebarVisible ? 'shown' : 'hidden';
      this.__showNotif('info', 'Studio Sidebar', `Studio sidebar ${action}`);
    },

    handleKeyboardShortcuts(event) {
      // Ctrl/Cmd + [ to toggle sources sidebar
      if ((event.ctrlKey || event.metaKey) && event.key === '[') {
        event.preventDefault();
        this.toggleSourcesSidebar();
      }
      // Ctrl/Cmd + ] to toggle studio sidebar
      if ((event.ctrlKey || event.metaKey) && event.key === ']') {
        event.preventDefault();
        this.toggleStudioSidebar();
      }
    }
  }
};
</script>
