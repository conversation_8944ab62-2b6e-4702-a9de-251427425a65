<template>
  <div class="h-full bg-white flex flex-col overflow-hidden">
    <!-- Header -->
    <NotebookHeader 
      :title="notebook?.title || 'Untitled Notebook'" 
      :notebookId="notebookId"
      @back-to-dashboard="$emit('back-to-dashboard')"
      @title-updated="handleTitleUpdate"
    />
    
    <!-- Main Content Area -->
    <div class="flex-1 flex overflow-hidden">
      <!-- Desktop Layout (3-column) with max-width constraint -->
      <div v-if="isDesktop" class="flex w-full h-full relative max-w-[1920px] mx-auto">
        <!-- Sources Sidebar -->
        <div v-if="isSourcesSidebarVisible" :class="`${sourcesWidth} flex-shrink-0 transition-all duration-500 ease-in-out`">
          <SourcesSidebar
            :hasSource="hasSource"
            :notebookId="notebookId"
            :selectedCitation="selectedCitation"
            @citation-close="handleCitationClose"
            @citation-click="handleCitationClick"
          />
        </div>

        <!-- Sources Toggle Button -->
        <button
          @click="toggleSourcesSidebar"
          class="absolute top-1/2 transform -translate-y-1/2 z-10 bg-white border border-gray-300 rounded-md p-2 shadow-sm hover:bg-gray-50 transition-all duration-500 ease-in-out"
          :class="{
            'left-2': !isSourcesSidebarVisible,
            'left-[calc(25%-1rem)]': isSourcesSidebarVisible && !isSourceDocumentOpen,
            'left-[calc(35%-1rem)]': isSourcesSidebarVisible && isSourceDocumentOpen
          }"
          :title="isSourcesSidebarVisible ? 'Hide Sources' : 'Show Sources'"
        >
          <svg class="h-4 w-4 text-gray-600 transition-transform duration-500 ease-in-out" :class="{ 'rotate-180': !isSourcesSidebarVisible }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>

        <!-- Chat Area -->
        <div :class="`${chatWidth} flex-shrink-0 transition-all duration-500 ease-in-out`">
          <ChatArea
            :hasSource="hasSource"
            :notebookId="notebookId"
            :notebook="notebook"
            @citation-click="handleCitationClick"
          />
        </div>

        <!-- Studio Toggle Button -->
        <button
          @click="toggleStudioSidebar"
          class="absolute top-1/2 transform -translate-y-1/2 z-10 bg-white border border-gray-300 rounded-md p-2 shadow-sm hover:bg-gray-50 transition-all duration-500 ease-in-out"
          :class="{
            'right-2': !isStudioSidebarVisible,
            'right-[calc(30%-1rem)]': isStudioSidebarVisible
          }"
          :title="isStudioSidebarVisible ? 'Hide Studio' : 'Show Studio'"
        >
          <svg class="h-4 w-4 text-gray-600 transition-transform duration-500 ease-in-out" :class="{ 'rotate-180': !isStudioSidebarVisible }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </button>

        <!-- Studio Sidebar -->
        <div v-if="isStudioSidebarVisible" :class="`${studioWidth} flex-shrink-0 transition-all duration-500 ease-in-out`">
          <StudioSidebar
            :notebookId="notebookId"
            @citation-click="handleCitationClick"
          />
        </div>
      </div>
      
      <!-- Mobile/Tablet Layout (tabs) -->
      <div v-else class="w-full h-full">
        <MobileNotebookTabs
          :hasSource="hasSource"
          :notebookId="notebookId"
          :notebook="notebook"
          :selectedCitation="selectedCitation"
          @citation-close="handleCitationClose"
          @citation-click="handleCitationClick"
        />
      </div>
    </div>
  </div>
</template>

<script>
import NotebookHeader from './NotebookHeader.vue';
import SourcesSidebar from './SourcesSidebar.vue';
import ChatArea from './ChatArea.vue';
import StudioSidebar from './StudioSidebar.vue';
import MobileNotebookTabs from './MobileNotebookTabs.vue';

export default {
  name: 'NotebookView',
  components: {
    NotebookHeader,
    SourcesSidebar,
    ChatArea,
    StudioSidebar,
    MobileNotebookTabs,
  },
  props: {
    notebookId: {
      type: String,
      required: true
    },
    notebook: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      selectedCitation: null,
      sources: [],
      isLoadingSources: false,
      isSourcesSidebarVisible: this.getInitialSidebarState('sources'),
      isStudioSidebarVisible: this.getInitialSidebarState('studio'),
      windowWidth: typeof window !== 'undefined' ? window.innerWidth : 1200,
      isInitialized: false
    };
  },
  computed: {
    isDesktop() {
      // Use a more responsive breakpoint for better layout
      return this.windowWidth >= 1200;
    },
    
    hasSource() {
      return this.sources && this.sources.length > 0;
    },
    
    isSourceDocumentOpen() {
      return !!this.selectedCitation;
    },
    
    // Dynamic width calculations for desktop with better constraints
    sourcesWidth() {
      if (!this.isSourcesSidebarVisible) return 'w-0';
      // Use fixed pixel widths for better control on ultra-wide screens
      return this.isSourceDocumentOpen ? 'w-[420px] max-w-[35%]' : 'w-[320px] max-w-[25%]';
    },

    studioWidth() {
      if (!this.isStudioSidebarVisible) return 'w-0';
      return 'w-[380px] max-w-[30%]';
    },

    chatWidth() {
      // Calculate chat width based on visible sidebars with better responsive behavior
      const sourcesVisible = this.isSourcesSidebarVisible;
      const studioVisible = this.isStudioSidebarVisible;

      if (!sourcesVisible && !studioVisible) {
        return 'w-full max-w-[1200px] mx-auto';
      } else if (!sourcesVisible && studioVisible) {
        return 'flex-1 min-w-0 max-w-[1200px]';
      } else if (sourcesVisible && !studioVisible) {
        return 'flex-1 min-w-0 max-w-[1200px]';
      } else {
        // Both sidebars visible
        return 'flex-1 min-w-0 max-w-[800px]';
      }
    }
  },
  created() {
    this.initializeLayout();
    this.loadSources();
    // Listen for window resize to update desktop/mobile view
    window.addEventListener('resize', this.handleResize);
    // Add keyboard shortcuts for sidebar toggles
    window.addEventListener('keydown', this.handleKeyboardShortcuts);
  },

  mounted() {
    // Ensure proper layout after component is mounted
    this.$nextTick(() => {
      this.handleResize();
      this.isInitialized = true;
    });
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.handleResize);
    window.removeEventListener('keydown', this.handleKeyboardShortcuts);
  },
  methods: {
    getInitialSidebarState(type) {
      // Determine initial sidebar state based on screen size
      if (typeof window === 'undefined') return true;

      const width = window.innerWidth;

      if (width >= 1600) {
        // Ultra-wide screens: show both sidebars
        return true;
      } else if (width >= 1400) {
        // Wide screens: show sources, hide studio initially
        return type === 'sources';
      } else if (width >= 1200) {
        // Medium screens: show sources only
        return type === 'sources';
      } else {
        // Small screens: hide both initially
        return false;
      }
    },

    initializeLayout() {
      // Set up initial layout state
      this.windowWidth = typeof window !== 'undefined' ? window.innerWidth : 1200;

      // Apply responsive sidebar states
      this.isSourcesSidebarVisible = this.getInitialSidebarState('sources');
      this.isStudioSidebarVisible = this.getInitialSidebarState('studio');
    },

    async loadSources() {
      if (!this.notebookId) return;

      this.isLoadingSources = true;
      try {
        // Simulate API call - replace with actual implementation
        await new Promise(resolve => setTimeout(resolve, 500));

        // Mock sources data - start with some sources for demo
        this.sources = [
          {
            id: '1',
            title: 'Research Document.pdf',
            type: 'pdf',
            notebook_id: this.notebookId,
            created_at: new Date().toISOString()
          },
          {
            id: '2',
            title: 'Project Overview.docx',
            type: 'docx',
            notebook_id: this.notebookId,
            created_at: new Date(Date.now() - 86400000).toISOString()
          }
        ];
      } catch (error) {
        console.error('Failed to load sources:', error);
        this.__showNotif('error', 'Error', 'Failed to load sources');
      } finally {
        this.isLoadingSources = false;
      }
    },
    
    handleCitationClick(citation) {
      this.selectedCitation = citation;
    },
    
    handleCitationClose() {
      this.selectedCitation = null;
    },
    
    handleTitleUpdate(newTitle) {
      // Update the notebook title
      if (this.notebook) {
        this.notebook.title = newTitle;
      }
    },
    
    handleResize() {
      const oldWidth = this.windowWidth;
      this.windowWidth = window.innerWidth;

      // Only auto-adjust on initial load or significant breakpoint changes
      if (!this.isInitialized) {
        return;
      }

      // Handle breakpoint transitions more gracefully
      const wasSmall = oldWidth < 1200;
      const isSmall = this.windowWidth < 1200;
      const wasMedium = oldWidth >= 1200 && oldWidth < 1400;
      const isMedium = this.windowWidth >= 1200 && this.windowWidth < 1400;
      const wasLarge = oldWidth >= 1400;
      const isLarge = this.windowWidth >= 1400;

      // Only auto-adjust when crossing major breakpoints
      if (wasSmall && (isMedium || isLarge)) {
        // Moving from small to larger: show sources
        this.isSourcesSidebarVisible = true;
        if (isLarge) {
          this.isStudioSidebarVisible = true;
        }
      } else if ((wasMedium || wasLarge) && isSmall) {
        // Moving to small screen: hide both for mobile experience
        this.isSourcesSidebarVisible = false;
        this.isStudioSidebarVisible = false;
      }
    },

    toggleSourcesSidebar() {
      this.isSourcesSidebarVisible = !this.isSourcesSidebarVisible;
      const action = this.isSourcesSidebarVisible ? 'shown' : 'hidden';
      this.__showNotif('info', 'Sources Sidebar', `Sources sidebar ${action}`);
    },

    toggleStudioSidebar() {
      this.isStudioSidebarVisible = !this.isStudioSidebarVisible;
      const action = this.isStudioSidebarVisible ? 'shown' : 'hidden';
      this.__showNotif('info', 'Studio Sidebar', `Studio sidebar ${action}`);
    },

    handleKeyboardShortcuts(event) {
      // Ctrl/Cmd + [ to toggle sources sidebar
      if ((event.ctrlKey || event.metaKey) && event.key === '[') {
        event.preventDefault();
        this.toggleSourcesSidebar();
      }
      // Ctrl/Cmd + ] to toggle studio sidebar
      if ((event.ctrlKey || event.metaKey) && event.key === ']') {
        event.preventDefault();
        this.toggleStudioSidebar();
      }
    }
  }
};
</script>

<style scoped>
/* Prevent layout shift during initialization */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Ensure smooth width transitions */
.flex-1 {
  transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1), flex 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Prevent content jumping during sidebar transitions */
.min-w-0 {
  min-width: 0;
}

/* Smooth button transitions */
button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

button:hover {
  transform: scale(1.05);
}

/* Ensure proper layout constraints */
.max-w-\[1920px\] {
  max-width: 1920px;
}

.max-w-\[1200px\] {
  max-width: 1200px;
}

.max-w-\[800px\] {
  max-width: 800px;
}
</style>
