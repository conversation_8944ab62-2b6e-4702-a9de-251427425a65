<template>
    <div class="py-6 px-4 bg-gray-50 min-h-screen">
        <!-- Date and Welcome Text -->
        <div class="mb-6">
            <p class="text-sm text-gray-500">{{ __dateFormatHome() }}</p>
            <h1 class="text-2xl font-semibold text-gray-800">
                Good {{ __getGreet() }}, {{user?.fullName}}
            </h1>
        </div>

        <!-- Action Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <!-- Change Password Card -->
            <div @click="goto('changePassword')" class="flex bg-white border rounded-lg shadow-sm p-8 text-center">
                <div class="flex justify-end items-center text-3xl mb-2 m-auto">
                    <div class="mr-6">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                            stroke="currentColor" class="size-6">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M16.5 10.5V6.75a4.5 4.5 0 1 0-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H6.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z" />
                        </svg>
                    </div>
                    <div class="text-start">
                        <h2 class="text-lg font-semibold text-gray-800">Change Password</h2>
                        <p class="text-sm text-gray-500">Set your own chosen password</p>
                    </div>
                </div>
            </div>

            <!-- Account Profile Card -->
            <div @click="goto('accountProfile')" class="flex bg-white border rounded-lg shadow-sm p-8 text-center">
                <div class="flex justify-end items-center text-3xl mb-2 m-auto">
                    <div class="mr-6">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                            stroke="currentColor" class="size-6">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z" />
                        </svg>
                    </div>
                    <div class="text-start">
                        <h2 class="text-lg font-semibold text-gray-800">Account Profile</h2>
                        <p class="text-sm text-gray-500">Modify your profile and other preferences</p>
                    </div>
                </div>
            </div>

            <!-- Select Your Expertise Card -->
            <div @click="goto('selectExpertise')" class="flex bg-white border rounded-lg shadow-sm p-8 text-center">
                <div class="flex justify-end items-center text-3xl mb-2 m-auto">
                    <div class="mr-6">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                            stroke="currentColor" class="size-6">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z" />
                        </svg>
                    </div>
                    <div class="text-start">
                        <h2 class="text-lg font-semibold text-gray-800">Select Your Expertise</h2>
                        <p class="text-sm text-gray-500">To match the task that will be assigned</p>
                    </div>
                </div>
            </div>

            <!-- Service Package Card -->
            <div @click="goto('servicePackage')" class="flex bg-white border rounded-lg shadow-sm p-8 text-center">
                <div class="flex justify-end items-center text-3xl mb-2 m-auto">
                    <div class="mr-6">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                            stroke="currentColor" class="size-6">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5M10 11.25h4M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z" />
                        </svg>
                    </div>
                    <div class="text-start">
                        <h2 class="text-lg font-semibold text-gray-800">Service Package</h2>
                        <p class="text-sm text-gray-500">Service that you are available to work on</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import { mapActions, mapGetters } from 'vuex';
    import authApi from '@/api/auth'
    export default {
        
        methods: {
            ...mapActions({
                fetchUser: 'auth/fetchUser',
                setUser: 'auth/setUser'
            }),
            goto(target) {
                if (target === 'changePassword') this.$router.push('/settings/change-password')
                if (target === 'accountProfile') this.$router.push('/settings/profile')
                if (target === 'selectExpertise') this.$router.push('/settings/expertise')
                if (target === 'servicePackage') this.$router.push('/settings/service')
            },
            updateIsActive() {
                if (!this.user?.isActive) {
                    this.updateUser()
                } else {
                    console.log("apakah apakah", this.user.isActive);
                }
            },
            updateUser() {
                const callback = (response) => {
                    this.setUser(response.data)
                }

                const errCallback = (err) => {
                    const message = err?.response?.data?.message;
                    this.__showNotif('error', 'Error', message);
                }
                const params = {
                    isActive: 1,
                    fullName: this.user.fullName,
                    phone: this.user.phone,
                    email: this.user.email,
                    username: this.user.username,
                }
                const id = this.user?.id
                authApi.update(params, callback, errCallback)
            },
        },
        data() {
            return {
                userName: "fikuri", // Assuming you fetch this from a store or API
                date: new Date(),
            };
        },
        mounted() {
            this.userName = this.user?.fullName;
            this.updateIsActive()
        },
        computed: {
            ...mapGetters({
                user: 'auth/user'
            }),
            formattedDate() {
                const options = {
                    weekday: "long",
                    day: "numeric",
                };
                return this.date.toLocaleDateString(undefined, options);
            },
        },
    };
</script>

<style scoped>
    /* You can customize some additional styling here if necessary */
</style>