<template>
  <div class="h-full bg-white flex flex-col overflow-hidden">
    <!-- Main Content -->
    <div class="flex-1 flex overflow-hidden">
      <!-- Dashboard View -->
      <div v-if="currentView === 'dashboard'" class="w-full">
        <DashboardView 
          :notebooks="notebooks"
          :isLoading="isLoading"
          :isCreating="isCreating"
          @create-notebook="handleCreateNotebook"
          @open-notebook="handleOpenNotebook"
        />
      </div>
      
      <!-- Notebook View -->
      <div v-else-if="currentView === 'notebook'" class="w-full flex">
        <NotebookView 
          :notebookId="currentNotebookId"
          :notebook="currentNotebook"
          @back-to-dashboard="handleBackToDashboard"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import DashboardView from './components/DashboardView.vue';
import NotebookView from './components/NotebookView.vue';

export default {
  name: 'AnswerFlow',
  components: {
    DashboardView,
    NotebookView,
  },
  data() {
    return {
      currentView: 'dashboard', // 'dashboard' or 'notebook'
      currentNotebookId: null,
      notebooks: [],
      isLoading: false,
      isCreating: false,
      error: null,
    };
  },
  computed: {
    ...mapGetters({
      user: 'auth/user'
    }),
    currentNotebook() {
      return this.notebooks.find(n => n.id === this.currentNotebookId);
    }
  },
  created() {
    // Check authentication
    if (!this.user) {
      this.$router.push('/login');
      return;
    }

    this.loadNotebooks();

    // Add keyboard shortcuts
    document.addEventListener('keydown', this.handleKeyboardShortcuts);
  },

  beforeUnmount() {
    document.removeEventListener('keydown', this.handleKeyboardShortcuts);
  },
  methods: {
    async loadNotebooks() {
      this.isLoading = true;
      try {
        // Simulate API call - replace with actual implementation
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock data for now
        this.notebooks = [
          {
            id: '1',
            title: 'Research Project',
            description: 'AI research documentation',
            updated_at: new Date().toISOString(),
            sources: [{ count: 3 }],
            icon: '📝',
            color: 'bg-blue-100'
          },
          {
            id: '2', 
            title: 'Meeting Notes',
            description: 'Weekly team meeting notes',
            updated_at: new Date(Date.now() - 86400000).toISOString(),
            sources: [{ count: 1 }],
            icon: '📋',
            color: 'bg-green-100'
          }
        ];
      } catch (error) {
        console.error('Failed to load notebooks:', error);
        this.error = error.message;
        this.__showNotif('error', 'Error', 'Failed to load notebooks');
      } finally {
        this.isLoading = false;
      }
    },
    
    async handleCreateNotebook() {
      this.isCreating = true;
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const newNotebook = {
          id: Date.now().toString(),
          title: 'Untitled notebook',
          description: '',
          updated_at: new Date().toISOString(),
          sources: [{ count: 0 }],
          icon: '📝',
          color: 'bg-gray-100'
        };
        
        this.notebooks.unshift(newNotebook);
        this.handleOpenNotebook(newNotebook.id);
        
        this.__showNotif('success', 'Success', 'Notebook created successfully');
      } catch (error) {
        console.error('Failed to create notebook:', error);
        this.__showNotif('error', 'Error', 'Failed to create notebook');
      } finally {
        this.isCreating = false;
      }
    },
    
    handleOpenNotebook(notebookId) {
      this.currentNotebookId = notebookId;
      this.currentView = 'notebook';
    },
    
    handleBackToDashboard() {
      this.currentView = 'dashboard';
      this.currentNotebookId = null;
      // Reload notebooks to get latest data
      this.loadNotebooks();
    },

    handleKeyboardShortcuts(event) {
      // Ctrl/Cmd + N: Create new notebook
      if ((event.ctrlKey || event.metaKey) && event.key === 'n') {
        event.preventDefault();
        if (this.currentView === 'dashboard') {
          this.handleCreateNotebook();
        }
      }

      // Escape: Go back to dashboard
      if (event.key === 'Escape' && this.currentView === 'notebook') {
        this.handleBackToDashboard();
      }
    }
  }
};
</script>
