<template>
  <header class="bg-white px-6 py-4">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-2">
        <Logo />
        <h1 class="text-xl font-medium text-gray-900">AnswerFlow</h1>
      </div>
      
      <div class="flex items-center space-x-4">
        <!-- Help Button -->
        <button
          @click="showHelp = true"
          class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors"
          title="Help & Shortcuts"
        >
          <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </button>

        <!-- User Menu -->
        <div class="relative" ref="userMenu">
          <button 
            @click="toggleUserMenu"
            class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center cursor-pointer hover:bg-purple-600 transition-colors"
          >
            <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </button>
          
          <!-- Dropdown Menu -->
          <div 
            v-if="showUserMenu"
            class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-50"
          >
            <div class="py-1">
              <button 
                @click="handleSignOut"
                class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
              >
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
                Sign Out
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Help Modal -->
    <HelpModal :show="showHelp" @close="showHelp = false" />
  </header>
</template>

<script>
import Logo from './Logo.vue';
import HelpModal from './HelpModal.vue';

export default {
  name: 'DashboardHeader',
  components: {
    Logo,
    HelpModal,
  },
  props: {
    userEmail: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      showUserMenu: false,
      showHelp: false
    };
  },
  mounted() {
    // Close menu when clicking outside
    document.addEventListener('click', this.handleClickOutside);
  },
  beforeUnmount() {
    document.removeEventListener('click', this.handleClickOutside);
  },
  methods: {
    toggleUserMenu() {
      this.showUserMenu = !this.showUserMenu;
    },
    
    handleClickOutside(event) {
      if (this.$refs.userMenu && !this.$refs.userMenu.contains(event.target)) {
        this.showUserMenu = false;
      }
    },
    
    handleSignOut() {
      this.showUserMenu = false;
      // Navigate to logout
      this.$router.push('/logout');
    }
  }
};
</script>
