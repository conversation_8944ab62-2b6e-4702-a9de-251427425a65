<template>
	<!-- Loader -->
	<loader-circle v-if="isFetchingProject" />
	Analytic
</template>

<script>
import {
	BriefcaseIcon,
	DownloadIcon,
	UserGroupIcon,
	XIcon,
} from '@heroicons/vue/solid';

import { mapGetters, mapActions } from 'vuex';
import TModal from '@/components/global/Modal.vue';
import projectApi from "@/api/project";

export default {
	components: {
		BriefcaseIcon,
		DownloadIcon,
		UserGroupIcon,
		XIcon,
		TModal,
	},
	data() {
		return {
		};
	},
	computed: {
		...mapGetters({
			getToken: 'auth/getToken',
			user: 'auth/user',
			isFetchingUser: 'auth/isFetchingUser',
			isAdmin: 'auth/isAdmin',
		}),
		isPackageMax() {
			return this.user &&  this.user.userCredit &&  this.user.userCredit.credit ? this.user.userCredit.credit : 'Unlimited';
		},
    currentId() {
      return this.$route.params.id;
    }
	},
	created() {
    this.setDataUrl()
	},
  watch: {
    currentId() {
      this.setDataUrl()
    },
  },
	mounted() {
	},
	methods: {
		...mapActions({
			fetchUser: 'auth/fetchUser',
      setDataProject: 'application/setDataProject',
		}),
    setDataUrl() {
      const encryptedId = this.$route.params.id; // Get the encrypted string from route params
      const decryptedData = this.__decryptProjectData(encryptedId); // Decrypt the data
      
      if (decryptedData) {
        this.projectId = decryptedData.id;   // Access the original project ID
        this.projectSlug = decryptedData.slug; // Access the original project slug
      }
      this.getProject()
    },
    getProject() {
      const callback = (response) => {
        const data = response.data;
        this.setDataProject(data);
      };
      const errCallback = (err) => {
        console.log(err);
      };
      projectApi.get(this.projectId, callback, errCallback);
    },
	},
};
</script>