<template>
  <div>
    <label :for="id" class="body-m text-kb_medium_grey dark:text-white">
      <slot></slot>
    </label>
    <div class="relative">
      <input
        v-if="controlType === 'titleTask'"
        :id="id"
        class="_input-title h-12"
        :class="_class"
        v-model="internalValue"
        :placeholder="ph"
        @blur="handleBlur"
      />
      <input
        v-if="controlType === 'sectionColumn'"
        :id="id"
        class="_input_section h-6 bg-transparent ml-[-20px] mt-[-10px] w-full"
        :class="[_class, { 'focused-border': isFocused }]"
        v-model="internalValue"
        :placeholder="ph"
        @blur="handleBlur"
        @focus="handleFocus"
        @setFocusSection="setFocusSection"
        ref="inputSection"
      />
      <input
        v-if="controlType === 'subtaskInput'"
        :id="id"
        class="_input-subtask h-8"
        :class="[_class, { 'focused-border': isFocused }]"
        v-model="internalValue"
        :placeholder="ph"
        :style="{ width: inputWidth + 'px' }"
        @keyup.enter="handleEnter"
        @blur="handleBlur"
        @focus="handleFocus"
        @setFocusSection="setFocusSectionSubtask"
        @input="updateInputWidth"
        ref="inputSectionSubtask"
      />
      <!-- Hidden span to calculate dynamic width -->
      <span ref="hiddenSpanSubtask" class="hidden-span">{{ internalValue }}</span>
      
      <input
        v-if="controlType === 'sectionColumnAddDirection'"
        :id="id"
        class="_input_section h-6 bg-transparent ml-[-20px] mt-[-10px] w-full"
        :class="_class"
        v-model="internalValue"
        :placeholder="ph"
        @blur="handleBlur"
        @focus="handleFocus"
        ref="inputElement"

      />
      <input
        v-if="controlType !== 'textarea' && controlType !== 'textarea2' && controlType !== 'titleTask' &&
        controlType !== 'sectionColumn' && controlType !== 'sectionColumnAddDirection' && controlType !== 'subtaskInput'" 
        :id="id"
        class="_input h-8"
        :class="_class"
        v-model="internalValue"
        :placeholder="ph"
        @blur="handleBlur"
        @focus="handleFocus"
        ref="inputElement"
      />
      <textarea
        v-if="controlType === 'textarea'"
        :id="id"
        class="_input max-h-[380px]"
        rows="10"
        :class="_class"
        v-model="internalValue"
        :placeholder="ph"
        @blur="handleBlur"
      ></textarea>
      <textarea
        v-if="controlType === 'textarea2'"
        :id="id"
        class="_input max-h-[160px]"
        rows="1"
        :class="_class"
        v-model="internalValue"
        :placeholder="ph"
        @blur="handleBlur"
      ></textarea>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue';

const props = defineProps({
  modelValue: String,
  ph: String,
  controlType: String,
});

const emit = defineEmits(['update:modelValue', 'blur', 'focus']);

// Internal value state, which is synced with v-model
const internalValue = ref(props.modelValue);

// Expose the error state for validation
const isError = ref(false);
const isFocused = ref(false);
const inputElement = ref(null);
const inputSection = ref(null);
const inputSectionSubtask = ref(null);
const hiddenSpanSubtask = ref(null);
const inputWidth = ref(80); // Default width


const id = ref(parseInt(1e6 * Math.random()));

// Sync the internalValue with the modelValue prop
watch(
  () => props.modelValue,
  (newVal) => {
    internalValue.value = newVal;
  }
);

// Handle input event
watch(internalValue, (newVal) => {
  emit('update:modelValue', newVal);
});

// Function to update input width based on the hidden span's width
const updateInputWidth = () => {
  nextTick(() => {
    const spanWidth = hiddenSpanSubtask.value?.offsetWidth || 80; // Fallback to 80 if no text
    inputWidth.value = Math.min(spanWidth + 10, window.innerWidth * 0.8); // Add padding and set a max width of 80% of parent
  });
};

const handleBlur = (e) => {
  if (internalValue.value === '') {
    isError.value = true;
  }
  isFocused.value = false;
  emit('blur', e);
};

const handleEnter = (e) => {
  if (internalValue.value === '') {
    isError.value = true;
  }
  isFocused.value = false;
  emit('keyupEnter', e);
}

const handleFocus = (e) => {
  isFocused.value = true;
  emit('focus', e);
};

const setFocusSection = (e) => {
  isFocused.value = true;
  inputSection.value?.focus();
};

const setFocusSectionSubtask = (e) => {
  isFocused.value = true;
  inputSectionSubtask.value?.focus();
};

// Optional: focus the input field on mount
onMounted(() => {
  nextTick(() => {
    inputElement.value?.focus();
    updateInputWidth();
  });
});

const _class = computed(() => {
  return isError.value ? '!border-kb_red' : '';
});

defineExpose({ isError, setFocusSection, setFocusSectionSubtask });
</script>

<style scoped>
._input {
  border-radius: 4px;
  @apply block w-full pl-2 font-medium text-kb_black text-f13 leading-6 dark:text-white dark:bg-kb_dark_grey dark:placeholder:text-white placeholder:opacity-25 border-gray-300;
}
._input_section {
  border-radius: 4px;
  @apply block w-full pl-2 font-medium text-kb_black text-f13 leading-6 dark:text-white dark:bg-kb_dark_grey dark:placeholder:text-white;
}
._input-title {
  border-radius: 4px;
  @apply block w-full font-bold text-kb_black text-2xl leading-6 dark:text-white dark:bg-kb_dark_grey dark:placeholder:text-white placeholder:opacity-25 border-gray-300;
}
._input-subtask {
  border-radius: 4px;
  max-width: 530px;
  @apply block w-full pl-2 font-medium text-kb_black text-f13 leading-6 dark:text-white dark:bg-kb_dark_grey dark:placeholder:text-white placeholder:opacity-25 border-gray-300;
}
input:focus,
textarea:focus {
  outline-style: solid;
  outline-color: transparent;
}

.hidden-span {
  visibility: hidden;
  white-space: pre;
  position: absolute;
  font-size: 13px; /* Ensure font-size matches the input field */
  font-weight: 500;
  line-height: 23px;
}

::placeholder {
  font-size: 13px;
  color: #000112;
  font-weight: 500;
  line-height: 23px;
}

.focused-border {
    border: 1px solid gray; /* Add desired focus border */
  }
</style>
