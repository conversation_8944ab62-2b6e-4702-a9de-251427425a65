<template>
	<!-- <div v-show="!isChrome"
		class="top-0 px-6 w-full py-2 [h-30px] lg:flex lg:items-center bg-red-600 text-white text-sm justify-center">
		{{ $t('Bannerbite performs better with Chrome based browser') }},
		<a href="https://www.google.co.id/chrome/?brand=CHBD&gclid=CjwKCAjwiOv7BRBREiwAXHbv3AWBmeej6mPeNXp8FwCu2rVTeVyKHmOTY8PZwdTt57cJPs2tReOVzBoCC8YQAvD_BwE&gclsrc=aw.ds"
			target="_blank" class="underline pointer text-white pl-1">{{ $t('Download here') }}</a>
	</div> -->
	<div>
		<!-- dont make sense teuing aku ge gatau -->
		<!-- <div class="hidden">
			<nav class="-mb-0.5 flex gap-x-6">
				<a ref="triggerHS"
					class="py-4 px-1 inline-flex items-center gap-2 hover:border-b-2 hover:border-primary-600 text-sm whitespace-nowrap hover:text-primary-600"
					href="#">
					Overview
				</a>
			</nav>
		</div> -->
	</div>
	<div id="main-view" class="h-screen overflow-hidden bg-gray-100">
		<Sidebar />
		<Header />
		<main id="content" class="lg:ps-[260px] lg:pt-[50px]">
			<div class="main-padding sm:py-0 space-y-5">
				<!-- Card -->
				<div
					class="min-w-full flex flex-col justify-center h-72 md:h-96 min-h-[calc(100vh-40px)] sm:min-h-[calc(100vh-40px)] md:min-h-[calc(100vh-143px)] bg-white shadow-sm rounded-xl">
					<div class="relative p-4  h-full rounded-xl overflow-hidden  overflow-y-auto">
						<router-view v-slot="{ Component }">
							<!-- <transition name="slide"> -->
							<component :is="Component" />
							<!-- </transition> -->
						</router-view>
					</div>
				</div>
				<!-- End Card -->
			</div>
		</main>
		<Footer />
	</div>
</template>

<script>
	import Sidebar from '@/layouts/partials/SidebarClient.vue';
	import Header from '@/layouts/partials/HeaderClient.vue';
	import Footer from '@/layouts/partials/Footer.vue';

	export default {
		components: {
			Sidebar,
			Header,
			Footer,
		},
		setup() {},
		data() {
			return {
				isChrome: false,
			};
		},
		mounted() {
			// Add the click event listener to the document
			// document.addEventListener('click', this.clickThis);
		},
		beforeDestroy() {
			// Remove the click event listener to prevent memory leaks
			// document.removeEventListener('click', this.clickThis);
		},
		computed: {
			activeRouteId() {
				return this.$route.path !== "/embed";
			},
			activeRouteName() {
				return this.$route.name;
			},
		},
		watch: {
			$route(to, from) {
				this.scrollDown();
			},
		},
		created() {
			this.browserName();
		},
		methods: {
			// clickThis() {
			// 	this.$refs.triggerHS ? .click();
			// 	// Your logic here
			// },
			scrollDown() {
				const objDiv = this.$refs.mainView;
				// Scroll top div
				// objDiv.scrollTop = 0;

				// Scroll bottom of div
				// objDiv.scrollTop = objDiv.scrollHeight;
			},
			browserName(agent) {
				let userAgent = navigator.userAgent;
				let browserName;
				if (userAgent.match(/chrome|chromium|crios/i)) {
					browserName = "chrome";
				} else if (userAgent.match(/firefox|fxios/i)) {
					browserName = "firefox";
				} else if (userAgent.match(/safari/i)) {
					browserName = "safari";
				} else if (userAgent.match(/opr\//i)) {
					browserName = "opera";
				} else if (userAgent.match(/edg/i)) {
					browserName = "edge";
				} else {
					browserName = "No browser detection";
				}
				if (browserName != 'chrome') {
					this.isChrome = false;
				} else {
					this.isChrome = true;
				}
			},
		},
	};
</script>
<style>
	@media (min-width: 1024px) {
		.main-padding {
			padding: 28px 18px 28px 18px;
		}

		.main-width {
			width: calc(100vw - 332px);
		}
	}

	@media (max-width: 1024px) {
		.main-width {
			width: calc(100vw - 34px);
		}
	}
</style>