<template>
  <div v-if="open" class="fixed inset-0 z-50 flex items-center justify-center">
    <!-- Backdrop -->
    <div 
      class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
      @click="$emit('close')"
    ></div>
    
    <!-- Dialog -->
    <div class="relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
      <!-- Header -->
      <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-medium text-gray-900">Add Source</h2>
          <button 
            @click="$emit('close')"
            class="p-1 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      <!-- Content -->
      <div class="p-6">
        <div class="space-y-4">
          <!-- File Upload -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Upload Files</label>
            <div
              @drop="handleDrop"
              @dragover.prevent
              @dragenter.prevent
              class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors cursor-pointer"
              @click="triggerFileInput"
            >
              <input
                ref="fileInput"
                type="file"
                multiple
                accept=".pdf,.doc,.docx,.txt"
                @change="handleFileSelect"
                class="hidden"
              />
              <svg class="h-8 w-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
              <p class="text-sm text-gray-600 mb-1">
                Drop files here or click to upload
              </p>
              <p class="text-xs text-gray-500">
                PDF, DOC, TXT files supported
              </p>
            </div>
          </div>

          <!-- Integration Options -->
          <div class="grid grid-cols-2 gap-4">
            <t-button
              variant="outline"
              class="h-auto p-4 flex flex-col items-center space-y-2"
              @click="showWebsiteDialog = true"
            >
              <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
              </svg>
              <span class="font-medium text-sm">Link - Website</span>
              <span class="text-xs text-gray-500">Multiple URLs at once</span>
            </t-button>

            <t-button
              variant="outline"
              class="h-auto p-4 flex flex-col items-center space-y-2"
              @click="showTextDialog = true"
            >
              <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <span class="font-medium text-sm">Paste Text - Copied Text</span>
              <span class="text-xs text-gray-500">Add copied content</span>
            </t-button>
          </div>
        </div>
      </div>
    </div>

    <!-- Website Dialog -->
    <div v-if="showWebsiteDialog" class="fixed inset-0 z-60 flex items-center justify-center">
      <div 
        class="fixed inset-0 bg-black bg-opacity-50"
        @click="showWebsiteDialog = false"
      ></div>
      <div class="relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Add Website URLs</h3>
          <textarea
            v-model="websiteUrls"
            placeholder="Enter URLs, one per line..."
            class="w-full h-32 p-3 border border-gray-300 rounded-md resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          ></textarea>
          <div class="flex justify-end space-x-3 mt-4">
            <t-button variant="outline" @click="showWebsiteDialog = false">Cancel</t-button>
            <t-button @click="handleWebsiteSubmit">Add URLs</t-button>
          </div>
        </div>
      </div>
    </div>

    <!-- Text Dialog -->
    <div v-if="showTextDialog" class="fixed inset-0 z-60 flex items-center justify-center">
      <div 
        class="fixed inset-0 bg-black bg-opacity-50"
        @click="showTextDialog = false"
      ></div>
      <div class="relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Add Copied Text</h3>
          <input
            v-model="textTitle"
            placeholder="Title for this text..."
            class="w-full p-3 border border-gray-300 rounded-md mb-3 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <textarea
            v-model="textContent"
            placeholder="Paste your text content here..."
            class="w-full h-32 p-3 border border-gray-300 rounded-md resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          ></textarea>
          <div class="flex justify-end space-x-3 mt-4">
            <t-button variant="outline" @click="showTextDialog = false">Cancel</t-button>
            <t-button @click="handleTextSubmit">Add Text</t-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AddSourceDialog',
  props: {
    open: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showWebsiteDialog: false,
      showTextDialog: false,
      websiteUrls: '',
      textTitle: '',
      textContent: ''
    };
  },
  methods: {
    triggerFileInput() {
      this.$refs.fileInput.click();
    },

    handleFileSelect(event) {
      const files = Array.from(event.target.files);
      this.handleFiles(files);
    },

    handleDrop(event) {
      event.preventDefault();
      const files = Array.from(event.dataTransfer.files);
      this.handleFiles(files);
    },

    handleFiles(files) {
      console.log('Files selected:', files);
      files.forEach(file => {
        const sourceData = {
          title: file.name,
          type: this.getFileType(file.name),
          file: file
        };
        this.$emit('submit', sourceData);
      });
    },

    getFileType(filename) {
      const extension = filename.split('.').pop().toLowerCase();
      if (extension === 'pdf') return 'pdf';
      if (['doc', 'docx'].includes(extension)) return 'doc';
      return 'text';
    },

    handleWebsiteSubmit() {
      const urls = this.websiteUrls.split('\n').filter(url => url.trim());
      urls.forEach(url => {
        const sourceData = {
          title: `Website: ${url}`,
          type: 'website',
          url: url.trim()
        };
        this.$emit('submit', sourceData);
      });
      this.websiteUrls = '';
      this.showWebsiteDialog = false;
    },

    handleTextSubmit() {
      if (this.textTitle && this.textContent) {
        const sourceData = {
          title: this.textTitle,
          type: 'text',
          content: this.textContent
        };
        this.$emit('submit', sourceData);
        this.textTitle = '';
        this.textContent = '';
        this.showTextDialog = false;
      }
    }
  }
};
</script>
