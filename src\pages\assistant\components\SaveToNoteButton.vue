<template>
  <t-button
    variant="ghost"
    size="sm"
    @click="handleSaveToNote"
    :disabled="isCreating"
    class="flex items-center space-x-1 text-gray-600 hover:text-gray-800"
  >
    <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
    </svg>
    <span class="text-xs">{{ isCreating ? 'Saving...' : 'Save to note' }}</span>
  </t-button>
</template>

<script>
import TButton from '@/components/global/Button.vue';

export default {
  name: 'SaveToNoteButton',
  components: {
    TButton,
  },
  props: {
    content: {
      type: [String, Object],
      required: true
    },
    notebookId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      isCreating: false
    };
  },
  methods: {
    async handleSaveToNote() {
      if (!this.notebookId) return;
      
      this.isCreating = true;
      
      try {
        // Handle both string content and enhanced content with citations
        let contentText;
        let title;
        let sourceType;
        let extractedText;
        
        // Check if this is an AI response with structured content (object with segments)
        const isAIResponse = typeof this.content === 'object' && this.content && 'segments' in this.content && Array.isArray(this.content.segments);
        
        if (isAIResponse) {
          // For AI responses with citations, save the structured content as JSON
          contentText = JSON.stringify(this.content);
          // Generate title from the first segment's text
          const firstSegmentText = this.content.segments[0]?.text || 'AI Response';
          title = firstSegmentText.length > 50 ? firstSegmentText.substring(0, 47) + '...' : firstSegmentText;
          sourceType = 'ai_response';
          
          // Extract text for preview from first few segments
          extractedText = this.content.segments
            .slice(0, 3)
            .map(segment => segment.text)
            .join(' ')
            .substring(0, 200);
        } else {
          // For simple string content (typically user messages)
          const contentString = typeof this.content === 'string' ? this.content : String(this.content);
          contentText = contentString;
          const firstLine = contentString.split('\n')[0];
          title = firstLine.length > 50 ? firstLine.substring(0, 47) + '...' : firstLine;
          sourceType = 'user';
          extractedText = undefined; // User notes don't need extracted text
        }
        
        // Create note using the notes store/API
        await this.createNote({
          title,
          content: contentText,
          source_type: sourceType,
          extracted_text: extractedText,
          notebook_id: this.notebookId
        });
        
        this.__showNotif('success', 'Success', 'Note saved successfully');
        this.$emit('saved');
        
      } catch (error) {
        console.error('Failed to save note:', error);
        this.__showNotif('error', 'Error', 'Failed to save note');
      } finally {
        this.isCreating = false;
      }
    },
    
    async createNote(noteData) {
      try {
        // Import Supabase client
        const { db } = await import('@/integrations/supabase/client.js');

        // Use Supabase to create the note
        const { data, error } = await db.createNote(noteData);

        if (error) {
          throw error;
        }

        return data;
      } catch (error) {
        console.error('Supabase Error creating note:', error);
        throw error;
      }
    }
  }
};
</script>

<style scoped>
/* Additional styles if needed */
</style>
