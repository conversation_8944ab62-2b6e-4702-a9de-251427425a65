<template>
	<aside id="hs-pro-sidebar" class="hs-overlay [--auto-close:lg]
	hs-overlay-open:translate-x-0
	-translate-x-full transition-all duration-300 transform
	w-[260px] h-full
	hidden
	fixed inset-y-0 start-0 z-[10]
	bg-white border-e border-gray-200
	lg:block lg:translate-x-0 lg:end-auto lg:bottom-0" tabindex="-1" aria-label="Sidebar">
		<div class="relative flex flex-col h-full max-h-full pt-3">
			<header class="h-[46px] px-8">
				<!-- Logo -->
				<a class="flex-none rounded-md text-xl inline-block font-semibold focus:outline-none focus:opacity-80"
					href="/" aria-label="Preline">
					<img class="mx-auto h-10 mb-5 w-auto mb-6" src="@/assets/images/android-chrome-512x512.png"
						alt="Planlagt">
				</a>
				<!-- End Logo -->
			</header>

			<!-- Content -->
			<div
				class="mt-1.5 h-full overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300">
				<!-- Nav -->
				<nav class="hs-accordion-group pb-3  w-full flex flex-col flex-wrap" data-hs-accordion-always-open>
					<ul class="flex flex-col gap-y-1">
						<SideBarItem v-if="isAdmin" @click="resetDataProject()"
							v-for="(item, index) in filteredNavItems" :key="index" :item="item">
							<template #default>
								<svg v-if="item.isSvg" :class="{'text-primary-600': isActive(item)}"
									xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
									fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
									stroke-linejoin="round" class="shrink-0 mt-0.5 size-5 text-gray-400">
									<rect width="13" height="7" x="8" y="3" rx="1" />
									<path d="m2 9 3 3-3 3" />
									<rect width="13" height="7" x="8" y="14" rx="1" /></svg>
								<svg v-if="item.isBooking" :class="{'text-primary-600': isActive(item)}"
									xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
									fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
									stroke-linejoin="round" class="shrink-0 mt-0.5 size-5 text-gray-400">
									<path d="M8 2v4" />
									<path d="M16 2v4" />
									<path d="M21 13V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h8" />
									<path d="M3 10h18" />
									<path d="M16 19h6" />
									<path d="M19 16v6" /></svg>
								<svg v-if="!item.isSvg && !item.isBooking" class="shrink-0 mt-0.5 size-5 text-gray-400"
									:class="{'text-primary-600': isActive(item)}" xmlns="http://www.w3.org/2000/svg"
									:width="24" :height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
									stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
									<path :d="item.iconPath1" />
									<polyline v-if="item.iconPath2" :points="item.iconPath2" />
									<circle v-if="item.iconDot" :cx="item.iconDot.cx" :cy="item.iconDot.cy"
										:r="item.iconDot.r" />

								</svg>
							</template>
						</SideBarItem>
						<SideBarItem v-if="isClient" @click="resetDataProject()"
							v-for="(item, index) in navItemsClient" :key="index" :item="item">
							<template #default>
								<svg v-if="item.isSvg" :class="{'text-primary-600': isActive(item)}"
									xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
									fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
									stroke-linejoin="round" class="shrink-0 mt-0.5 size-5 text-gray-400">
									<rect width="13" height="7" x="8" y="3" rx="1" />
									<path d="m2 9 3 3-3 3" />
									<rect width="13" height="7" x="8" y="14" rx="1" /></svg>
								<svg v-if="item.isBooking" :class="{'text-primary-600': isActive(item)}"
									xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
									fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
									stroke-linejoin="round" class="shrink-0 mt-0.5 size-5 text-gray-400">
									<path d="M8 2v4" />
									<path d="M16 2v4" />
									<path d="M21 13V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h8" />
									<path d="M3 10h18" />
									<path d="M16 19h6" />
									<path d="M19 16v6" /></svg>
								<svg v-if="!item.isSvg && !item.isBooking" class="shrink-0 mt-0.5 size-5 text-gray-400"
									:class="{'text-primary-600': isActive(item)}" xmlns="http://www.w3.org/2000/svg"
									:width="24" :height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
									stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
									<path :d="item.iconPath1" />
									<polyline v-if="item.iconPath2" :points="item.iconPath2" />
									<circle v-if="item.iconDot" :cx="item.iconDot.cx" :cy="item.iconDot.cy"
										:r="item.iconDot.r" />

								</svg>
							</template>
						</SideBarItem>
						<SideBarItem v-if="isUser" @click="resetDataProject()"
							v-for="(item, index) in navItemsInternal" :key="index" :item="item">
							<template #default>
								<svg v-if="item.isSvg" :class="{'text-primary-600': isActive(item)}"
									xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
									fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
									stroke-linejoin="round" class="shrink-0 mt-0.5 size-5 text-gray-400">
									<rect width="13" height="7" x="8" y="3" rx="1" />
									<path d="m2 9 3 3-3 3" />
									<rect width="13" height="7" x="8" y="14" rx="1" /></svg>
								<svg v-if="item.isBooking" :class="{'text-primary-600': isActive(item)}"
									xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
									fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
									stroke-linejoin="round" class="shrink-0 mt-0.5 size-5 text-gray-400">
									<path d="M8 2v4" />
									<path d="M16 2v4" />
									<path d="M21 13V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h8" />
									<path d="M3 10h18" />
									<path d="M16 19h6" />
									<path d="M19 16v6" /></svg>
								<svg v-if="!item.isSvg && !item.isBooking" class="shrink-0 mt-0.5 size-5 text-gray-400"
									:class="{'text-primary-600': isActive(item)}" xmlns="http://www.w3.org/2000/svg"
									:width="24" :height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
									stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
									<path :d="item.iconPath1" />
									<polyline v-if="item.iconPath2" :points="item.iconPath2" />
									<circle v-if="item.iconDot" :cx="item.iconDot.cx" :cy="item.iconDot.cy"
										:r="item.iconDot.r" />

								</svg>
							</template>
						</SideBarItem>

						<!-- Divider -->
						<div v-if="filteredProject?.length > 0 && isAdmin || isClient && filteredProject?.length > 0"
							class="pl-8 text-sm font-medium text-gray-500 mt-6 mb-4">
							{{ $t('Projects') }}
						</div>
						<div v-if="isAdmin || isClient"
							v-for="starred in filteredProject" :key="starred.id"
							class="pl-8 font-medium flex items-center mb-3 pointer"
							@click="setDataProject(starred); navigateToOverview(starred)">
							<div :class="{'bg-primary-600 text-white': getActiveProject?.slug === starred.slug}"
								class="border-[1px] rounded-lg border-gray-300 w-[25px] h-[25px] flex items-center justify-center text-center text-[10px] font-bold text-gray-400">
								{{ __generateInitial(starred.name) }}
							</div>
							<div class="w-[180px] text-sm ml-2 truncate"
								:class="{'text-primary-600': getActiveProject?.slug === starred.slug}">
								{{ starred.name }}
							</div>
						</div>
						<div v-if="(isAdmin && filteredProject?.length >= 1) || (isClient && filteredProject?.length >= 1)"
							@click="goTo('/projects'); resetDataProject()" class="pl-8 text-gray-500 font-medium text-sm pointer">
							{{ $t('See all Projects') }}
						</div>
						<!-- End Divider -->
					</ul>
				</nav>
				<!-- End Nav -->
			</div>
			<!-- End Content -->

			<footer class="hidden lg:block mb-2">
				<!-- Project Dropdown -->
				<div class="pl-8 flex items-center" @click="resetDataProject()">
					<router-link class="flex items-center gap-x-3 py-2 text-sm text-gray-800 rounded-lg"
						to="/settings/change-password">
						<svg class="shrink-0 size-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" width="24"
							height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
							stroke-linecap="round" stroke-linejoin="round">
							<path
								d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z" />
							<circle cx="12" cy="12" r="3" />
						</svg>
						<div class="text-md font-medium">{{ $t('Settings') }}</div>
					</router-link>
				</div>
				<!-- End Project Dropdown -->
			</footer>

			<div class="lg:hidden absolute top-3 -end-3 z-10">
				<!-- Sidebar Close -->
				<button type="button"
					class="w-6 h-7 inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-md border border-gray-200 bg-white text-gray-500 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none"
					data-hs-overlay="#hs-pro-sidebar">
					<svg class="shrink-0 size-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
						viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
						stroke-linejoin="round">
						<polyline points="7 8 3 12 7 16" />
						<line x1="21" x2="11" y1="12" y2="12" />
						<line x1="21" x2="11" y1="6" y2="6" />
						<line x1="21" x2="11" y1="18" y2="18" /></svg>
				</button>
				<!-- End Sidebar Close -->
			</div>
		</div>
	</aside>
</template>

<script>
	import projectApi from "@/api/project";
	import tasksApi from "@/api/tasks";
	import SideBarItem from '@/layouts/partials/SideBarItem.vue';
	import {
		mapActions,
		mapGetters
	} from "vuex";
	export default {
		components: {
			SideBarItem,
		},
		created() {
			console.log(this.isUser)
			this.getProjects();
			this.getTasks()
			this.$soketio.on('project_update', () => {
				this.getProjects();
			});
		},
		computed: {
			...mapGetters({
				user: 'auth/user',
				isClient: 'auth/isClient',
				isAdmin: 'auth/isAdmin',
				isFreelancer: 'auth/isFreelancer',
				isUser: 'auth/isUser',
				getActiveProject: 'application/getActiveProject',
				getStarredProject: 'application/getStarredProject'
			}),
			filteredProject() {
				return this.getStarredProject?.sort((a, b) => b.meta.isStarred - a.meta.isStarred).slice(0, 5);
			},
			filteredNavItems() {
				return this.navItemsAdmin
			}
		},
		data() {
			return {
				navItemsAdmin: [{
						path: '/',
						isClient: 'none',
						label: 'Home',
						iconPath1: 'M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z',
						iconPath2: '9 22 9 12 15 12 15 22'
					},
					{
						path: '/tasks',
						label: 'My Task',
						isClient: 0,
						iconPath1: 'M6 6.878V6a2.25 2.25 0 0 1 2.25-2.25h7.5A2.25 2.25 0 0 1 18 6v.878m-12 0c.235-.083.487-.128.75-.128h10.5c.263 0 .515.045.75.128m-12 0A2.25 2.25 0 0 0 4.5 9v.878m13.5-3A2.25 2.25 0 0 1 19.5 9v.878m0 0a2.246 2.246 0 0 0-.75-.128H5.25c-.263 0-.515.045-.75.128m15 0A2.25 2.25 0 0 1 21 12v6a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 18v-6c0-.98.626-1.813 1.5-2.122'
					},
					{
						path: '/projects',
						label: 'Projects',
						isClient: 0,
						iconPath1: 'm6 14 1.45-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.55 6a2 2 0 0 1-1.94 1.5H4a2 2 0 0 1-2-2V5c0-1.1.9-2 2-2h3.93a2 2 0 0 1 1.66.9l.82 1.2a2 2 0 0 0 1.66.9H18a2 2 0 0 1 2 2v2',
						iconDot: {
							cx: 12,
							cy: 15,
							r: 1.5
						}
					},
					{

						path: '/admin',
						label: 'Planlagt Data',
						isClient: 0,
						isSvg: true,
						iconPath1: 'm2 9 3 3-3 3',
						iconPath2: '',
						children: [{
								path: '/admin/users',
								label: 'Users',
							},
							{
								path: '/admin/client',
								label: 'Client',
							},
							{
								path: '/admin/offer',
								label: 'Offer',
							},
							{
								path: '/admin/service',
								alternativePath: '/admin/product',
								label: 'Service & Product Item',
							},
							{
								path: '/admin/expertise',
								label: 'Expertise',
							},
							{
								path: '/admin/role',
								alternativePath: '/admin/job',
								label: 'Roles & Job',
							},
							{
								path: '/admin/category',
								label: 'Product Category',
							}
						]
					},
				],
				navItemsClient: [{
						path: '/',
						label: 'Home',
						iconPath1: 'M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z',
						iconPath2: '9 22 9 12 15 12 15 22'
					},
					{
						path: '/projects',
						label: 'Projects',
						isClient: 0,
						iconPath1: 'm6 14 1.45-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.55 6a2 2 0 0 1-1.94 1.5H4a2 2 0 0 1-2-2V5c0-1.1.9-2 2-2h3.93a2 2 0 0 1 1.66.9l.82 1.2a2 2 0 0 0 1.66.9H18a2 2 0 0 1 2 2v2',
						iconDot: {
							cx: 12,
							cy: 15,
							r: 1.5
						}
					},
					{
						path: '/event',
						label: 'Booking',
						isBooking: true,
					},
				],
				navItemsInternal: [{
						path: '/',
						label: 'Home',
						iconPath1: 'M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z',
						iconPath2: '9 22 9 12 15 12 15 22'
					},
					{
						path: '/tasks',
						label: 'My Task',
						isClient: 0,
						iconPath1: 'M6 6.878V6a2.25 2.25 0 0 1 2.25-2.25h7.5A2.25 2.25 0 0 1 18 6v.878m-12 0c.235-.083.487-.128.75-.128h10.5c.263 0 .515.045.75.128m-12 0A2.25 2.25 0 0 0 4.5 9v.878m13.5-3A2.25 2.25 0 0 1 19.5 9v.878m0 0a2.246 2.246 0 0 0-.75-.128H5.25c-.263 0-.515.045-.75.128m15 0A2.25 2.25 0 0 1 21 12v6a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 18v-6c0-.98.626-1.813 1.5-2.122'
					},
					{
						path: '/projects',
						label: 'Projects',
						isClient: 0,
						iconPath1: 'm6 14 1.45-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.55 6a2 2 0 0 1-1.94 1.5H4a2 2 0 0 1-2-2V5c0-1.1.9-2 2-2h3.93a2 2 0 0 1 1.66.9l.82 1.2a2 2 0 0 0 1.66.9H18a2 2 0 0 1 2 2v2',
						iconDot: {
							cx: 12,
							cy: 15,
							r: 1.5
						}
					},
				],
				projects: [],
				tasks: [],
			};
		},
		methods: {
			...mapActions({
				setDataProject: 'application/setDataProject',
				resetDataProject: 'application/resetDataProject',
				setDatastarredProject: 'application/setDatastarredProject',
			}),
			navigateToOverview(starred) {
				// Use btoa inside a method
				window.location.href = (`${import.meta.env.VITE_APP_URL}/e/kanban/${this.__encryptProjectData(starred.id, starred.slug)}`);
			},
			isActive(item) {
				// Check if the current route matches the parent item
				if (this.$route.path === (item.path)) {
					return true;
				}

				// Check if the current route matches any child item
				if (item.children && item.children.length) {
					return item.children.some(child => this.isActive(child));
				}

				return false;
			},
			goTo(route) {
				this.$router.push(route);
			},
			getProjects() {
				const callback = (response) => {
					const data = response.data;
					this.setDatastarredProject(data)
					this.projects = data;
				}
				const errCallback = (err) => {
					console.log(err)
				}

				const params = {
					orderBy: 'createdAt',
					sortBy: 'desc',
					page: 1,
					limit: 100,
				}
				projectApi.getListStarred(params, callback, errCallback)
			},
			async getTasks() {
				this.isFetching = true;
				const callback = (response) => {
					const data = response.data;
					this.tasks = data;
					if (this.tasks.length === 0) {
						this.removeMyTaskMenu()
					}
					this.isFetching = false;
				};
				const errCallback = (err) => {
					console.log(err);
					this.isFetching = false;
				};
				let params = {
					orderBy: 'createdAt',
					sortBy: 'asc',
					limit: 1,
				}
				tasksApi.getList(params, callback, errCallback);
			},
			removeMyTaskMenu() {
				// Find the index of the "My Task" menu item
				const index = this.navItemsInternal.findIndex(item => item.path === '/tasks');
				// If the item exists in the array, remove it using splice
				if (index !== -1) {
					this.navItemsInternal.splice(index, 1);
				}
				const indexAdmin = this.navItemsAdmin.findIndex(item => item.path === '/tasks');
				// If the item exists in the array, remove it using splice
				if (index !== -1) {
					this.navItemsAdmin.splice(indexAdmin, 1);
				}
			}
		}
	};
</script>