<template>
	<aside id="hs-pro-sidebar" class="hs-overlay [--auto-close:lg]
	hs-overlay-open:translate-x-0
	-translate-x-full transition-all duration-300 transform
	w-[260px] h-full
	hidden
	fixed inset-y-0 start-0 z-[10]
	bg-[#F9FAFB] border-e border-gray-200
	lg:block lg:translate-x-0 lg:end-auto lg:bottom-0" tabindex="-1" aria-label="Sidebar">
		<div class="relative flex flex-col h-full max-h-full pt-3">
			<header class="h-[46px] px-8">
				<!-- Logo -->
				<a class="flex-none rounded-md text-xl inline-block font-semibold focus:outline-none focus:opacity-80"
					href="/" aria-label="Preline">
					<img class="mx-auto h-6 w-auto mt-3" src="@/assets/images/logo_desidia.png"
						alt="Planlagt">
				</a>
				<!-- End Logo -->
			</header>

			<!-- Content -->
			<div
				class="mt-[100px] h-full overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300">
        <!-- Divider -->
        <div
          class="pl-8 text-sm font-medium text-gray-500 mt-6 mb-2">
          {{ $t('Inquiry') }}
        </div>
				<!-- Nav -->
				<nav class="hs-accordion-group pb-3  w-full flex flex-col flex-wrap" data-hs-accordion-always-open>
					<ul class="flex flex-col gap-y-1">
						<SideBarItem v-if="isAdmin"
							v-for="(item, index) in filteredNavItems" :key="index" :item="item">
							<template #default>
                <svg v-if="item.isDashboard" :class="{'text-primary-600': isActive(item)}" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="shrink-0 mt-0.5 size-5 text-gray-700"><rect width="7" height="9" x="3" y="3" rx="1"/><rect width="7" height="5" x="14" y="3" rx="1"/><rect width="7" height="9" x="14" y="12" rx="1"/><rect width="7" height="5" x="3" y="16" rx="1"/></svg>
                <svg v-if="item.isBoard" :class="{'text-primary-600': isActive(item)}" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="shrink-0 mt-0.5 size-5 text-gray-700"><path d="M6 5v11"/><path d="M12 5v6"/><path d="M18 5v14"/></svg>
							</template>
						</SideBarItem>

            <div
              class="pl-8 text-sm font-medium text-gray-500 mt-10 mb-2">
              {{ $t('Management') }}
            </div>
						<!-- Divider -->
						<SideBarItem v-if="isAdmin"
							v-for="(item, index) in navItemsAdmin" :key="index" :item="item">
							<template #default>
                <svg v-if="item.isUser" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="shrink-0 mt-0.5 size-5 text-gray-700"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><path d="M16 3.128a4 4 0 0 1 0 7.744"/><path d="M22 21v-2a4 4 0 0 0-3-3.87"/><circle cx="9" cy="7" r="4"/></svg>
                <svg v-if="item.isRole" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="shrink-0 mt-0.5 size-5 text-gray-700"><path d="m16 11 2 2 4-4"/><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/></svg>
                <svg v-if="item.isAssist" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="shrink-0 mt-0.5 size-5 text-gray-700"><path d="M12 6V2H8"/><path d="m8 18-4 4V8a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2Z"/><path d="M2 12h2"/><path d="M9 11v2"/><path d="M15 11v2"/><path d="M20 12h2"/></svg>
              </template>
						</SideBarItem>
						<!-- End Divider -->
					</ul>
				</nav>
				<!-- End Nav -->
			</div>
			<!-- End Content -->
			<div class="lg:hidden absolute top-3 -end-3 z-10">
				<!-- Sidebar Close -->
				<button type="button"
					class="w-6 h-7 inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-md border border-gray-200 bg-white text-gray-500 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none"
					data-hs-overlay="#hs-pro-sidebar">
					<svg class="shrink-0 size-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
						viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
						stroke-linejoin="round">
						<polyline points="7 8 3 12 7 16" />
						<line x1="21" x2="11" y1="12" y2="12" />
						<line x1="21" x2="11" y1="6" y2="6" />
						<line x1="21" x2="11" y1="18" y2="18" /></svg>
				</button>
				<!-- End Sidebar Close -->
			</div>
		</div>
	</aside>
</template>

<script>
	import SideBarItem from '@/layouts/partials/SideBarItem.vue';
	import {
		mapActions,
		mapGetters
	} from "vuex";
	export default {
		components: {
			SideBarItem,
		},
		created() {
			this.$soketio.on('project_update', () => {
				this.getProjects();
			});
		},
		computed: {
			...mapGetters({
				user: 'auth/user',
				isClient: 'auth/isClient',
				isAdmin: 'auth/isAdmin',
				isFreelancer: 'auth/isFreelancer',
				isUser: 'auth/isUser',
				getActiveProject: 'application/getActiveProject',
				getStarredProject: 'application/getStarredProject'
			}),
			filteredProject() {
				return this.getStarredProject?.sort((a, b) => b.meta.isStarred - a.meta.isStarred).slice(0, 5);
			},
			filteredNavItems() {
				return this.navItemsMain
			}
		},
		data() {
			return {
				navItemsMain: [{
						path: '/',
						label: 'Dashboard',
            isDashboard: true,
					},
					{
						path: '/tasks',
						label: 'Board',
            isBoard: true,
					},
				],
        navItemsAdmin: [{
						path: '/admin/users',
						label: 'Users',
						isClient: 0,
            isUser: true,
          },
          {
						path: '/admin/role',
            alternativePath: '/admin/group',
						label: 'Roles & Group',
						isClient: 0,
            isRole: true,
					},
          {
						path: '/assistant',
						label: 'AnswerFlow',
            isAssist: true,
						isClient: 0,
					},
				],
				projects: [],
				tasks: [],
			};
		},
		methods: {
			...mapActions({
				setDataProject: 'application/setDataProject',
				resetDataProject: 'application/resetDataProject',
				setDatastarredProject: 'application/setDatastarredProject',
			}),
			navigateToOverview(starred) {
				// Use btoa inside a method
				window.location.href = (`${import.meta.env.VITE_APP_URL}/e/kanban/${this.__encryptProjectData(starred.id, starred.slug)}`);
			},
			isActive(item) {
				// Check if the current route matches the parent item
				if (this.$route.path === (item.path)) {
					return true;
				}

				// Check if the current route matches any child item
				if (item.children && item.children.length) {
					return item.children.some(child => this.isActive(child));
				}

				return false;
			},
			goTo(route) {
				this.$router.push(route);
			},
			getProjects() {
				const callback = (response) => {
					const data = response.data;
					this.setDatastarredProject(data)
					this.projects = data;
				}
				const errCallback = (err) => {
					console.log(err)
				}

				const params = {
					orderBy: 'createdAt',
					sortBy: 'desc',
					page: 1,
					limit: 100,
				}
				projectApi.getListStarred(params, callback, errCallback)
			},
			async getTasks() {
				this.isFetching = true;
				const callback = (response) => {
					const data = response.data;
					this.tasks = data;
					if (this.tasks.length === 0) {
						this.removeMyTaskMenu()
					}
					this.isFetching = false;
				};
				const errCallback = (err) => {
					console.log(err);
					this.isFetching = false;
				};
				let params = {
					orderBy: 'createdAt',
					sortBy: 'asc',
					limit: 1,
				}
				tasksApi.getList(params, callback, errCallback);
			},
			removeMyTaskMenu() {
				// Find the index of the "My Task" menu item
				const index = this.navItemsInternal.findIndex(item => item.path === '/tasks');
				// If the item exists in the array, remove it using splice
				if (index !== -1) {
					this.navItemsInternal.splice(index, 1);
				}
				const indexAdmin = this.navItemsAdmin.findIndex(item => item.path === '/tasks');
				// If the item exists in the array, remove it using splice
				if (index !== -1) {
					this.navItemsAdmin.splice(indexAdmin, 1);
				}
			}
		}
	};
</script>