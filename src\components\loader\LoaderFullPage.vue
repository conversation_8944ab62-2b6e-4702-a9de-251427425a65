
<template>
	<div
		class="fixed top-0 left-0 right-0 bottom-0 w-full z-[100] overflow-hidden bg-[#333333] opacity-90 flex flex-col items-center justify-center"
		:style="customStyle"
	>
		<loader-circle />
		<h2 class="text-center text-white text-xl font-semibold mt-[6em]">
			{{ text }}
		</h2>
		<p
			v-if="isShowDefaultText"
			class="w-full text-center text-white"
		>
			Processing, Please wait...
		</p>
		<slot />
	</div>
</template>

<script>
/* eslint-disable vue/html-closing-bracket-spacing */
export default {
	components: {
	},
	props: {
		text: {
			type: String,
		},
		isShowDefaultText: {
			type: Boolean,
			default: () => true,
		},
		customStyle: {
			type: String,
		}
	},
	data() {
		return {
		};
	},
	computed: {},
	watch: {},
	created() {},
	mounted() {
		document.addEventListener('keydown', this.closeOnEscape);
	},
	beforeUnmount() {
		document.removeEventListener('keydown', this.closeOnEscape);
	},
	methods: {
		closeOnEscape(event) {
			if (event.key === 'Escape') {
				this.$emit('close');
			}
		}
	}
};
</script>