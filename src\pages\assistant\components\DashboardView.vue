<template>
  <div class="min-h-screen bg-white">
    <!-- Header -->
    <DashboardHeader :userEmail="user?.email" />
    
    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-6 py-[60px]">
      <div class="mb-8">
        <h1 class="font-medium text-gray-900 mb-2 text-5xl">Welcome to AnswerFlow</h1>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading" class="text-center py-16">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p class="text-gray-600 text-lg">Loading your notebooks...</p>
        <p class="text-gray-500 text-sm mt-2">This may take a moment</p>
      </div>

      <!-- Content -->
      <div v-else>
        <!-- Empty State -->
        <EmptyDashboard v-if="!hasNotebooks" @create-notebook="$emit('create-notebook')" :isCreating="isCreating" />
        
        <!-- Notebooks Grid -->
        <NotebookGrid v-else :notebooks="notebooks" @create-notebook="$emit('create-notebook')" @open-notebook="$emit('open-notebook', $event)" :isCreating="isCreating" />
      </div>
    </main>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import DashboardHeader from './DashboardHeader.vue';
import EmptyDashboard from './EmptyDashboard.vue';
import NotebookGrid from './NotebookGrid.vue';

export default {
  name: 'DashboardView',
  components: {
    DashboardHeader,
    EmptyDashboard,
    NotebookGrid,
  },
  props: {
    notebooks: {
      type: Array,
      default: () => []
    },
    isLoading: {
      type: Boolean,
      default: false
    },
    isCreating: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapGetters({
      user: 'auth/user'
    }),
    hasNotebooks() {
      return this.notebooks && this.notebooks.length > 0;
    }
  }
};
</script>
