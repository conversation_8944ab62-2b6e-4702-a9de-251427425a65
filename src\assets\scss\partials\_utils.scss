.very-weak {
	color: #FF4500;
}

.weak {
	color: orange;
}

.strong {
	color: #9ACD32;
}

.very-strong {
	color: #008000;
}

.pointer {
	cursor: pointer;
}

.text-light-gray {
	color: #d5dae4;
}

::-webkit-scrollbar-track
{
	-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
	background-color: #F2F2F2;
	border-radius: 10px;
}

::-webkit-scrollbar
{
	width: 6px;
	height: 6px;
	background-color: #F2F2F2;
	border-radius: 10px;
}

::-webkit-scrollbar-thumb
{
	background-color: #9FA6B2;
	border-radius: 10px;
}

.scrollable-container-cropper {
	overflow: auto;
	scrollbar-width: thin; /* For Firefox */
	scrollbar-color: transparent transparent; /* For Firefox */
}

/* For WebKit browsers (Chrome, Safari) */
.scrollable-container-cropper::-webkit-scrollbar {
	width: 6px;
}

.scrollable-container-cropper::-webkit-scrollbar-track {
	background-color: transparent;
	-webkit-box-shadow: none;
}

.scrollable-container-cropper::-webkit-scrollbar-thumb {
	background-color: transparent;
}

figure {
	margin: 0;
	display: grid;
	grid-template-rows: 1fr auto;
	margin-bottom: 10px;
	break-inside: avoid;
  }
  
  figure > img {
	grid-row: 1 / -1;
	grid-column: 1;
  }
  
  figure a {
	color: black;
	text-decoration: none;
  }
  
  figcaption {
	grid-row: 2;
	grid-column: 1;
	background-color: rgba(255,255,255,.5);
	padding: .2em .5em;
  }

  .c-text {
	cursor: text;
}



.CodeMirror-hscrollbar {
	height: 8px;
}

.disabled{
	pointer-events: none;
	background-color: #F4F4F4;
}

.disabled-input{
	pointer-events: none;
	background-color: #F4F4F4;
  color: #999;
}

.disabled-pointer{
	pointer-events: none;
}

.default-cursor {
	cursor: default;
}

//   new evolution using ratio on css hehehehehehehe hayuuk
.section-gallery {
	display: grid;
	gap: 20px;
	grid-template-columns: repeat(auto-fill, minmax(100px, 1fr)); /* Play with min-value */
  }
  
  .img-gallery {
	background-color: gainsboro; /* To visualize empty space */
	aspect-ratio: 16/9; 
	/*
	  "contain" to see full original image with eventual empty space
	  "cover" to fill empty space with truncating
	  "fill" to stretch
	*/
	object-fit: contain;
	width: 100%;
  }



.video-wrapper {
	position: relative;
	height: calc(100vh - 140px);
	overflow: hidden;
  }
  
  .video-wrapper video {
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	
  }

.rounded-template-selected {
	border-radius: 0.5rem;
	border-style: solid;
}

.rounded-template {
	border-radius: 0.5rem;
	border-style: solid;
	border-width: 0.5px;
}

.form-checkbox {
	/* Set the color for the unchecked checkbox's background */
	background-color: rgba(128, 128, 128, 0.221); /* Replace #fff with your desired background color */
	/* Add some padding for better spacing (optional) */
	padding: 2px;
	/* Optional: Remove default browser styles */
	appearance: none;
	-webkit-appearance: none;
	-moz-appearance: none;
  }

  
.mx-input {
  min-height: 42px!important;
}
