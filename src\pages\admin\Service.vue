<template>
    <loader-circle v-if="isFetching" />
    <div>
        <div class="flex justify-between m-2">
            <div class="mt-2 ml-4">
                <label for="table-header" class="font-medium text-gray-800">Service Item</label>
            </div>
            <div class="flex">
                <div class="relative mr-2">
                    <input type="text" v-model="keyword" v-value="keyword" @input="onInputSearch"
                        class="py-[7px] ps-10 pe-8 block w-full bg-white border-gray-200 rounded-md text-sm focus:bg-white focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none"
                        placeholder="Search" />
                    <div class="absolute inset-y-0 start-0 flex items-center pointer-events-none ps-4">
                        <svg class="shrink-0 size-4 text-gray-500 dark:text-neutral-500"
                            xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
                        </svg>
                    </div>
                </div>
                <div>
                    <t-button :color="`primary-solid`" @click="add()">
                        <PlusIcon class="h-5 w-5 text-white" />
                        <span class="text-sm text-white">Add New</span>
                    </t-button>
                </div>
            </div>
        </div>
        <!-- Table Section -->
        <div v-if="!isFetching && items.length"
            class="m-2 overflow-x-auto [&::-webkit-scrollbar]:h-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300">
            <div class="min-w-full inline-block align-middle">
                <!-- Table -->
                <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                        <tr>
                            <th scope="col" class="grow w-[1rem] mr-2">
                                <div class="text-gray-800"></div>
                            </th>
                            <th scope="col" class="min-w-[150px]">
                                <div
                                    class="pe-4 text-start flex items-center gap-x-1 text-sm font-medium text-gray-800">
                                    <!-- Sort Dropdown -->
                                    <div class="hs-dropdown relative inline-flex w-full cursor-pointer">
                                        <button id="hs-pro-dutnms" type="button"
                                            class="py-2.5 text-start font-semibold w-full flex items-center gap-x-1 text-sm text-nowrap  text-black-500 focus:outline-none focus:bg-gray-100"
                                            aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                                            Full Name
                                            <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24"
                                                height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="m7 15 5 5 5-5" />
                                                <path d="m7 9 5-5 5 5" /></svg>
                                        </button>

                                        <!-- Dropdown -->
                                        <div class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-40 transition-[opacity,margin] duration opacity-0 hidden z-10 bg-white rounded-sm shadow-[0_10px_40px_10px_rgba(0,0,0,0.08)]"
                                            role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-dutnms">
                                            <div class="p-1">
                                                <button type="button" @click="sortAsc()"
                                                    class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg"
                                                        width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                        stroke-linejoin="round">
                                                        <path d="m5 12 7-7 7 7" />
                                                        <path d="M12 19V5" /></svg>
                                                    Sort ascending
                                                </button>
                                                <button type="button" @click="sortDsc()"
                                                    class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg"
                                                        width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                        stroke-linejoin="round">
                                                        <path d="M12 5v14" />
                                                        <path d="m19 12-7 7-7-7" /></svg>
                                                    Sort descending
                                                </button>
                                            </div>
                                        </div>
                                        <!-- End Dropdown -->
                                    </div>
                                    <!-- End Sort Dropdown -->
                                </div>
                            </th>
                            <th scope="col" class="min-w-10">
                                <div class="text-start flex items-center gap-x-1 text-sm font-medium text-gray-800">
                                    <!-- Sort Dropdown -->
                                    <div class="hs-dropdown relative inline-flex w-full cursor-pointer">
                                        <button id="hs-pro-dutnms" type="button"
                                            class="px-4 py-2.5 text-start font-semibold w-full flex items-center gap-x-1 text-sm text-nowrap  text-black-500 focus:outline-none focus:bg-gray-100"
                                            aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                                            Price CP (SP)
                                            <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24"
                                                height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="m7 15 5 5 5-5" />
                                                <path d="m7 9 5-5 5 5" /></svg>
                                        </button>

                                        <!-- Dropdown -->
                                        <div class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-40 transition-[opacity,margin] duration opacity-0 hidden z-10 bg-white rounded-sm shadow-[0_10px_40px_10px_rgba(0,0,0,0.08)]"
                                            role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-dutnms">
                                            <div class="p-1">
                                                <button type="button" @click="sortAsc()"
                                                    class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg"
                                                        width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                        stroke-linejoin="round">
                                                        <path d="m5 12 7-7 7 7" />
                                                        <path d="M12 19V5" /></svg>
                                                    Sort ascending
                                                </button>
                                                <button type="button" @click="sortDsc()"
                                                    class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg"
                                                        width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                        stroke-linejoin="round">
                                                        <path d="M12 5v14" />
                                                        <path d="m19 12-7 7-7-7" /></svg>
                                                    Sort descending
                                                </button>
                                            </div>
                                        </div>
                                        <!-- End Dropdown -->
                                    </div>
                                    <!-- End Sort Dropdown -->
                                </div>
                            </th>
                            <th scope="col" class="min-w-10">
                                <div class="text-start flex items-center gap-x-1 text-sm font-medium text-gray-800">
                                    <!-- Sort Dropdown -->
                                    <div class="hs-dropdown relative inline-flex w-full cursor-pointer">
                                        <button id="hs-pro-dutnms" type="button"
                                            class="px-4 py-2.5 text-start font-semibold w-full flex items-center gap-x-1 text-sm text-nowrap  text-black-500 focus:outline-none focus:bg-gray-100"
                                            aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                                            Delivery Time
                                            <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24"
                                                height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="m7 15 5 5 5-5" />
                                                <path d="m7 9 5-5 5 5" /></svg>
                                        </button>

                                        <!-- Dropdown -->
                                        <div class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-40 transition-[opacity,margin] duration opacity-0 hidden z-10 bg-white rounded-sm shadow-[0_10px_40px_10px_rgba(0,0,0,0.08)]"
                                            role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-dutnms">
                                            <div class="p-1">
                                                <button type="button" @click="sortAsc()"
                                                    class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg"
                                                        width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                        stroke-linejoin="round">
                                                        <path d="m5 12 7-7 7 7" />
                                                        <path d="M12 19V5" /></svg>
                                                    Sort ascending
                                                </button>
                                                <button type="button" @click="sortDsc()"
                                                    class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg"
                                                        width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                        stroke-linejoin="round">
                                                        <path d="M12 5v14" />
                                                        <path d="m19 12-7 7-7-7" /></svg>
                                                    Sort descending
                                                </button>
                                            </div>
                                        </div>
                                        <!-- End Dropdown -->
                                    </div>
                                    <!-- End Sort Dropdown -->
                                </div>
                            </th>
                            <th scope="col">
                                <div
                                    class="px-4 py-3 text-start flex items-center gap-x-1 text-sm font-medium text-gray-800">
                                </div>
                            </th>
                        </tr>
                    </thead>

                    <tbody class="divide-y divide-gray-200">
                        <tr v-for="(item, index) in items" :key="item.id" @mouseover="onHover(item)"
                            :class="{'bg-slate-100': item.id === curerentActive}">
                            <td class="whitespace-nowrap py-3">
                                <div class="w-[50px] flex items-center">
                                    <div class="grow">
                                        <span class="text-sm font-medium ml-4 text-gray-800">
                                            {{ index + 1 + (page - 1) * limit }}
                                        </span>
                                    </div>
                                </div>
                            </td>
                            <td class="whitespace-nowrap pe-4 py-2" @dblclick="edit(item)">
                                <div class="w-full flex items-center gap-x-3">
                                    <div class="grow">
                                        <span class="text-sm font-normal text-gray-800">
                                            {{ item.name }}
                                        </span>
                                    </div>
                                    <div class="w-[32px]">
                                        <div class="hs-dropdown hs-dropdown-example relative inline-flex w-[32px]"
                                            :class="{'hidden': item.id !== curerentActive}">
                                            <button id="hs-dropdown-example" type="button"
                                                @click="this.selectedItem = item"
                                                class="max-w-[32px] h-[30px] text-start w-full flex items-center text-sm text-nowrap font-normal pl-1 text-gray-800 shadow outline-gray-200 border border-gray-200 rounded focus:bg-gray-100"
                                                aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                                    stroke-width="1.5" stroke="currentColor" class="size-5">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        d="M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z" />
                                                </svg>
                                            </button>

                                            <div class="hs-dropdown-menu z-50 transition-[opacity,margin] duration hs-dropdown-open:opacity-100 opacity-0 
                                            hidden min-w-60 bg-white shadow-md rounded-md p-1 space-y-0.5 !mt-[-0.5rem] dark:bg-neutral-800 dark:border dark:border-neutral-700"
                                                role="menu" aria-orientation="vertical"
                                                aria-labelledby="hs-dropdown-custom-icon-trigger">
                                                <a data-hs-overlay="#drawer-right" @click="edit(item)"
                                                    class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700"
                                                    href="#">
                                                    Edit
                                                </a>
                                                <!-- <a @click="addNew(item)"
                                                    class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700"
                                                    href="#">
                                                    Duplicate
                                                </a> -->
                                                <hr class="border-1">
                                                <a @click="openDeleteModal(item)"
                                                    class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700"
                                                    href="#">
                                                    Delete
                                                </a>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </td>
                            <td class="whitespace-nowrap px-4 py-3">
                                <span class="text-sm text-gray-600">
                                    NOK. {{ isAdmin ? `${__currencyWithoutSymbol(item.costPrice)} (${__currencyWithoutSymbol(item.sellingPrice)})` : `${__currencyWithoutSymbol(item.costPrice)}` }}
                                </span>
                            </td>
                            <td class="whitespace-nowrap px-4 py-3">
                                <span class="text-sm text-gray-600">
                                    {{ item.deliveryTime }} {{ item.deliveryType === 'hour' ? 'Hr' : 'Day' }}
                                </span>
                            </td>
                            <td class="whitespace-nowrap px-4 py-3">
                                <div class="flex">

                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <!-- End Table -->
            </div>
        </div>
        <!-- End Table Section -->
        <!-- Footer -->
        <div v-if="!isFetching && items.length" class="mt-5 flex flex-wrap justify-between items-center gap-2">
            <p class="text-sm ml-4">
                <span class="font-medium text-stone-800 mr-1">{{ total }}</span>
                <span class="font-medium text-stone-800">Results</span>
            </p>
            <!-- Pagination -->
            <nav class="flex justify-end items-center gap-x-1" aria-label="Pagination">
                <button type="button" v-if="page > 1" @click="prevPage()"
                    class="py-2 inline-flex justify-center items-center gap-x-2 text-sm rounded-mdtext-stone-800 hover:bg-stone-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none"
                    aria-label="Previous">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round">
                        <path d="m15 18-6-6 6-6"></path>
                    </svg>
                    <span class="sr-only">Previous</span>
                </button>
                <div class="flex items-center gap-x-1 mr-2 ml-2">
                    <span
                        class="h-[26px] w-[26px] flex justify-center items-center bg-gray-300 py-2 text-sm rounded-md disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-700"
                        aria-current="page">{{ page }}</span>
                    <span
                        class="min-h-[38px] flex justify-center items-center text-stone-500 py-2 px-1.5 text-sm dark:text-neutral-500">of</span>
                    <span
                        class="min-h-[38px] flex justify-center items-center text-stone-500 py-2 px-1.5 text-sm dark:text-neutral-500">{{ maxPage }}</span>
                </div>
                <button type="button" v-if="page < maxPage" @click="nextPage()"
                    class="py-2 inline-flex justify-center items-center gap-x-2 text-sm rounded-mdtext-stone-800 hover:bg-stone-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none"
                    aria-label="Next">
                    <span class="sr-only">Next</span>
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round">
                        <path d="m9 18 6-6-6-6"></path>
                    </svg>
                </button>
            </nav>
            <!-- End Pagination -->
        </div>
        <!-- End Footer -->
        <DrawerRight :id="drawer-right" ref="drawerRight">
            <template #header>
                <div class="">
                    <div class="flex mt-4 pb-2">
                        
                        <p v-if="!isEdit" id="hs-pro-dutoo-label" class="text-sm font-semibold text-gray-800 ml-6 ">
                        Add Service
                        </p>
                        <p v-if="isEdit" id="hs-pro-dutoo-label" class="text-sm font-semibold text-gray-800 ml-6 ">
                        Edit {{ selectedItem.name }}
                        </p>
                    </div>
                </div>
            </template>
            <template #body>
                <div
                    class="mt-5 px-5 flex-1 flex flex-col overflow-y-auto overflow-hidden [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300">
                    <!-- List Item -->
                    <div class="pb-4 grid grid-cols-1 gap-x-4 border-gray-200">
                        <div class="col-span-1">
                            <label class="block text-sm font-semibold text-gray-700">Name</label>
                            <t-input v-model="name" :value="name" :type="text" placeholder="Type in Service name" :tabIndex="1">
                            </t-input>
                            <span v-if="errors.name" class="text-red-500 text-sm">{{ errors.name }}</span>
                        </div>
                    </div>
                    <!-- End List Item -->

                    <!-- List Item -->
                    <div class="pb-4 grid grid-cols-1 gap-x-4 border-gray-200">
                        <label class="block text-sm font-semibold text-gray-700">Description</label>
                        <t-input v-model="description" :value="description" :type="area"
                            placeholder="Type in service description" :tabIndex="2">
                        </t-input>
                    </div>
                    <!-- End List Item -->
                </div>
                <div class="border-t-2 p-0"></div>
                <div
                    class="my-6 px-5 flex-1 flex flex-col overflow-y-auto overflow-hidden [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300">
                    <label class="text-sm font-bold" for="cost">Cost Price</label>
                    <div class="">
                        <!-- Increased padding for a more spacious layout -->
                        <div>
                            <div class="grid grid-cols-4 items-center mb-4 p-0 m-0">
                                <!-- Column 1: Radio Button -->
                                <div class="col-span-1 flex items-center">
                                    <input id="hourly" type="radio" @change="onHourlyToggle()" :checked="isHourly"
                                        class="form-radio text-blue-600 focus:ring-blue-500" :tabIndex="3" />
                                    <label for="hourly" class="text-sm ml-2 text-gray-700">Hourly</label>
                                </div>
                                <!-- Column 2: Input Field -->
                                <div class="col-span-3">
                                    <t-input :is-disabled="!isHourly" v-model="costPrice" :value="costPrice"
                                        class="w-full" :type="`text-pre-input`" placeholder="Type in Hourly price" :tabIndex="4">
                                    </t-input>
                                    <span v-if="errors.costPrice" class="text-red-500 text-sm">{{ errors.costPrice }}</span>
                                </div>
                            </div>

                            <div class="grid grid-cols-4 items-center mb-4">
                                <!-- Column 1: Radio Button -->
                                <div class="col-span-1 flex items-center">
                                    <input id="unit" type="radio" @change="onUnitoggle()" :checked="!isHourly"
                                        class="form-radio text-blue-600 focus:ring-blue-500" :tabIndex="5" />
                                    <label for="unit" class="text-sm ml-2 text-gray-700">Unit</label>
                                </div>
                                <!-- Column 2: Input Field -->
                                <div class="col-span-3">
                                    <t-input :is-disabled="isHourly" v-model="unitPrice" :value="unitPrice"
                                        class="w-full" :type="`text-pre-input`" placeholder="Type in Unit price" :tabIndex="6">
                                    </t-input>
                                    <span v-if="errors.costPrice" class="text-red-500 text-sm">{{ errors.costPrice }}</span>
                                </div>
                            </div>

                            <div class="grid grid-cols-42 items-center">
                                <!-- Column 1: Label -->
                                <h3 class="col-span-1 text-sm font-semibold text-gray-700">Selling Price</h3>
                                <!-- Column 2: Input Field -->
                                <div class="col-span-3">
                                    <t-input v-model="sellingPrice" :value="sellingPrice" class="w-full"
                                        :type="`text-pre-input`" placeholder="Type in Selling price" :tabIndex="7"></t-input>
                                    <span v-if="errors.sellingPrice" class="text-red-500 text-sm">{{ errors.sellingPrice }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="border-t-2"></div>
                <div
                    class="my-6 px-5 flex-1 flex flex-col overflow-y-auto overflow-hidden [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300">
                    <label class="text-sm font-semibold mb-2"> Estimated Delivery Time </label>
                    <div class="flex items-center space-x-4">
                        <!-- Input Number -->
                        <div class="flex items-center bg-white border border-gray-200 rounded-lg dark:bg-neutral-700 dark:border-neutral-700"
                            data-hs-input-number="" style="min-width: 150px; height: 38px;">
                            <!-- Input Field -->
                            <input
                                class="w-full h-full p-0 text-center bg-transparent border-0 text-gray-800 focus:ring-0 dark:text-white"
                                style="appearance: textfield; -moz-appearance: textfield;" type="number"
                                aria-roledescription="Number field" v-model="deliveryTime" :value="deliveryTime" :tabIndex="8" />
                            <!-- Increment/Decrement Buttons in a Row -->
                            <div
                                class="flex divide-x divide-gray-200 border-l border-gray-200 dark:divide-neutral-700 dark:border-neutral-700">
                                <button @click="downDeliveryTime()" type="button"
                                    class="w-8 h-full flex justify-center items-center bg-white text-gray-800 hover:bg-gray-50 focus:outline-none dark:bg-neutral-900 dark:text-white dark:hover:bg-neutral-800 dark:focus:bg-neutral-800"
                                    aria-label="Decrease" :tabIndex="9">
                                    <svg class="w-[22px] h-[22px]" xmlns="http://www.w3.org/2000/svg" fill="none"
                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <path d="M5 12h14"></path>
                                    </svg>
                                </button>
                                <button @click="upDeliveryTime()" type="button"
                                    class="w-8 h-full flex justify-center items-center bg-white text-gray-800 hover:bg-gray-50 focus:outline-none dark:bg-neutral-900 dark:text-white dark:hover:bg-neutral-800 dark:focus:bg-neutral-800"
                                    aria-label="Increase" :tabIndex="10">
                                    <svg class="w-[22px] h-[22px]" xmlns="http://www.w3.org/2000/svg" fill="none"
                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <path d="M5 12h14"></path>
                                        <path d="M12 5v14"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <!-- End Input Number -->

                        <!-- Dropdown -->
                        <div class="hs-dropdown relative">
                            <button id="hs-dropdown-default" type="button"
                                class="hs-dropdown-toggle inline-flex items-center justify-between px-4 py-2 text-sm font-medium text-gray-800 bg-white border border-gray-200 rounded-lg shadow-sm hover:bg-gray-50 focus:outline-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700"
                                aria-haspopup="menu" aria-expanded="false" :tabIndex="11">
                                {{ deliveryType === "hour" ? "Hour" : "Day(s)"}}
                                <svg class="w-4 h-4 ml-2 hs-dropdown-open:rotate-180" xmlns="http://www.w3.org/2000/svg"
                                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m6 9 6 6 6-6" />
                                </svg>
                            </button>

                            <div class="hs-dropdown-menu z-50 hidden mt-2 min-w-[120px] bg-white shadow-md rounded-lg p-1 space-y-0.5 dark:bg-neutral-800 dark:border dark:border-neutral-700 dark:divide-neutral-700"
                                role="menu" aria-orientation="vertical">
                                <a @click="deliveryType = 'days'"
                                    class="flex items-center px-3 py-2 text-sm text-gray-800 rounded-lg hover:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700">
                                    Day(s)
                                </a>
                                <a @click="deliveryType = 'hour'"
                                    class="flex items-center px-3 py-2 text-sm text-gray-800 rounded-lg hover:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700">
                                    Hour
                                </a>
                            </div>
                        </div>
                        <!-- End Dropdown -->
                    </div>

                    <div class="flex mb-4">
                        <span class="text-xs font-bold mt-4"> Revision included in delivery time </span>
                        <div class="relative inline-block mt-2 ml-auto">
                            <input type="checkbox" id="hs-small-switch-with-icons" @click="onChangeRevision"
                                :checked="isRevision"
                                class="peer relative w-11 h-6 p-px bg-gray-100 border-transparent text-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:ring-blue-600 disabled:opacity-50 disabled:pointer-events-none checked:bg-none checked:text-blue-600 checked:border-blue-600 focus:checked:border-blue-600 dark:bg-neutral-800 dark:border-neutral-700 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-600 before:inline-block before:size-5 before:bg-white checked:before:bg-blue-200 before:translate-x-0 checked:before:translate-x-full before:rounded-full before:shadow before:transform before:ring-0 before:transition before:ease-in-out before:duration-200 dark:before:bg-neutral-400 dark:checked:before:bg-blue-200"
                                :tabIndex="12" />
                            <label for="hs-small-switch-with-icons" class="sr-only">switch</label>
                            <span v-if="!isRevision"
                                class="peer-checked:text-white text-gray-500 size-5 absolute top-[3px] start-0.5 flex justify-center items-center pointer-events-none transition-colors ease-in-out duration-200 dark:text-neutral-500">
                                <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M18 6 6 18"></path>
                                    <path d="m6 6 12 12"></path>
                                </svg>
                            </span>
                            <span v-if="isRevision"
                                class="peer-checked:text-blue-600 text-gray-500 size-5 absolute top-[3px] end-0.5 flex justify-center items-center pointer-events-none transition-colors ease-in-out duration-200 dark:text-neutral-500">
                                <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <polyline points="20 6 9 17 4 12"></polyline>
                                </svg>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="border-t-2 p-0"></div>
                <div>
                    <div class="mx-5 my-6">
                        <div class="mb-4">
                            <label class="block text-sm font-semibold text-gray-700 mb-1">Assigned Expertise</label>
                            <div class="relative">
                                <select v-model="selectedExpertise"
                                    class="text-sm block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" :tabIndex="13">
                                    <option value="" disabled selected>Select Expertise</option>
                                    <option v-for="(expertise, index) in expertises" :key="expertise.id"
                                        :value="expertise.id">
                                        {{ expertise.name }}
                                    </option>
                                </select>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">Only visible to selected job title</p>
                        </div>

                        <div class="mb-4">
                            <label class="block text-sm font-semibold text-gray-700 mb-1">Expertise Level</label>
                            <div class="relative">
                                <select v-model="selectedExpertiseLevel"
                                    class="text-sm block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" :tabIndex="14">
                                    <option key="none" value="" disabled selected>Select Level</option>
                                    <option key="Basic" value="Basic">Basic</option>
                                    <option key="Intermediate" value="Intermediate">Intermediate</option>
                                    <option key="Advanced" value="Advanced">Advanced</option>
                                </select>
                            </div>
                        </div>

                        <div clas="mt-2" >
                            <label for="assigned-user" class="text-sm font-semibold">Assigned User (Preferred)</label>
                            <!-- Select -->
                            <Multiselect :disabled="isUsersDisabled" v-model="selectedUser" :options="users" :custom-label="customLabel" no-result="No matches found"
                                :show-labels="false" placeholder="Select user" :tabIndex="15">
                                <template #singleLabel="{ option }">
                                    <div class="selected-item flex ">
                                        <img v-if="option.fullName" width="26" height="26" :src="getUserImage(option)"
                                            class="user-image" />
                                        <span class="m-0 p-0">{{ option.fullName || "Select User" }} </span>
                                    </div>
                                </template>
                                <template #caret>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="absolute right-[11px] top-[13px] w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M6 9l6 6 6-6"></path>
                                    </svg>
                                </template>
                                <template #option="{ option }">
                                    <div class="option-item">
                                        <img v-if="option.fullName" width="26" height="26" :src="getUserImage(option)"
                                            class="user-image" />
                                        <span class="m-0 p-0">{{ option.fullName || "Select User" }} </span>
                                    </div>
                                </template>
                                <template #noResult>
                                  <span>No matches found</span>
                                </template>
                            </Multiselect>
                            
                            <!-- End Select -->
                            <p class="text-xs">Only visible to selected user, and if they choose to accept it</p>
                        </div>
                    </div>
                </div>
                <div class="border-t-2 p-0"></div>
                <div
                    class="my-6 px-5 flex-1 flex flex-col overflow-y-auto overflow-hidden [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300">
                    <label class="text-sm font-semibold mb-2"> Task Information </label>
                    <div class="mb-2 mt-2">
                        <label class="block text-sm font-semibold text-gray-700">Task Name</label>
                        <t-input  v-model="taskName" :value="taskName"
                            placeholder="Type in Task name" :tabIndex="16"> </t-input>
                    </div>
                    <div class="mb-2">
                        <label class="block text-sm font-semibold text-gray-700 mb-1">Task Column</label>
                        <div class="relative">
                            <select @change="onColumnChange" v-model="selectedColumn"
                                class="block w-full px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 h-[38px]" :tabIndex="17">
                                <option key="service" value="Back-log">Back-log</option>
                                <option key="product" value="Pre Production" selected="true">Pre Production</option>
                                <option key="task" value="Production">Production</option>
                                <option key="task" value="Post Production">Post Production</option>
                                <option key="task" value="Revision">Revision</option>
                                <option key="task" value="Completed">Completed</option>
                            </select>
                        </div>
                    </div>
                    <!-- <div class="mb-2">
                        <label class="block text-sm font-semibold text-gray-700 mb-1">Task File</label>
                        <div class="relative">
                            <FileUpload @update:fileUrl="onUpload"/>
                        </div>
                    </div> -->
                    <div class="flex ">
                        <span class="text-xs font-bold mt-4"> Automatically assign resource</span>
                        <div class="relative inline-block mt-2 ml-auto">
                            <input type="checkbox" id="hs-small-switch-with-icons" @click="onChangeAutoAssign"
                                :checked="isAutoAssign"
                                class="peer relative w-11 h-6 p-px bg-gray-100 border-transparent text-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:ring-blue-600 disabled:opacity-50 disabled:pointer-events-none checked:bg-none checked:text-blue-600 checked:border-blue-600 focus:checked:border-blue-600 dark:bg-neutral-800 dark:border-neutral-700 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-600 before:inline-block before:size-5 before:bg-white checked:before:bg-blue-200 before:translate-x-0 checked:before:translate-x-full before:rounded-full before:shadow before:transform before:ring-0 before:transition before:ease-in-out before:duration-200 dark:before:bg-neutral-400 dark:checked:before:bg-blue-200"
                                :tabIndex="18" />
                            <label for="hs-small-switch-with-icons" class="sr-only">switch</label>
                            <span v-if="!isAutoAssign"
                                class="peer-checked:text-white text-gray-500 size-5 absolute top-[3px] start-0.5 flex justify-center items-center pointer-events-none transition-colors ease-in-out duration-200 dark:text-neutral-500">
                                <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M18 6 6 18"></path>
                                    <path d="m6 6 12 12"></path>
                                </svg>
                            </span>
                            <span v-if="isAutoAssign"
                                class="peer-checked:text-blue-600 text-gray-500 size-5 absolute top-[3px] end-0.5 flex justify-center items-center pointer-events-none transition-colors ease-in-out duration-200 dark:text-neutral-500">
                                <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <polyline points="20 6 9 17 4 12"></polyline>
                                </svg>
                            </span>
                        </div>
                    </div>
                    
                    <div class="flex mb-4">
                        <span class="text-xs font-bold mt-4"> Due date started on project creation </span>
                        <div class="relative inline-block mt-2 ml-auto">
                            <input type="checkbox" id="hs-small-switch-with-icons" @click="onChangeAutoDueDate"
                                :checked="isAutoDueDate"
                                class="peer relative w-11 h-6 p-px bg-gray-100 border-transparent text-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:ring-blue-600 disabled:opacity-50 disabled:pointer-events-none checked:bg-none checked:text-blue-600 checked:border-blue-600 focus:checked:border-blue-600 dark:bg-neutral-800 dark:border-neutral-700 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-600 before:inline-block before:size-5 before:bg-white checked:before:bg-blue-200 before:translate-x-0 checked:before:translate-x-full before:rounded-full before:shadow before:transform before:ring-0 before:transition before:ease-in-out before:duration-200 dark:before:bg-neutral-400 dark:checked:before:bg-blue-200"
                                :tabIndex="19" />
                            <label for="hs-small-switch-with-icons" class="sr-only">switch</label>
                            <span v-if="!isAutoDueDate"
                                class="peer-checked:text-white text-gray-500 size-5 absolute top-[3px] start-0.5 flex justify-center items-center pointer-events-none transition-colors ease-in-out duration-200 dark:text-neutral-500">
                                <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M18 6 6 18"></path>
                                    <path d="m6 6 12 12"></path>
                                </svg>
                            </span>
                            <span v-if="isAutoDueDate"
                                class="peer-checked:text-blue-600 text-gray-500 size-5 absolute top-[3px] end-0.5 flex justify-center items-center pointer-events-none transition-colors ease-in-out duration-200 dark:text-neutral-500">
                                <svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <polyline points="20 6 9 17 4 12"></polyline>
                                </svg>
                            </span>
                        </div>
                    </div>
                </div>
            </template>
            <template #footer>
                <div class="p-5 border-t border-gray-200">
                    <div class="flex items-center gap-x-2 justify-end">
                        <!-- Button -->
                        <t-button :color="`secondary-solid`" @click="closeDrawer" :tabIndex="20">
                            Cancel
                        </t-button>
                        <!-- End Button -->
                        <!-- Button -->
                        <t-button :color="`primary-solid`" @click="save()" :isLoading="isSaving" :isDisabled="isSaving"   :tabIndex="21">
                            {{ $t('Save Changes') }}
                        </t-button>
                        <!-- End Button -->
                    </div>
                </div>
            </template>
        </DrawerRight>
        <Confirmation :id="`confirm`">
            <template #header>
                <p class="text-base font-bold text-gray-800 dark:text-white">Delete item</p>
            </template>
            <template #body>
                <!-- <div class="text-xs text-gray-800 dark:text-neutral-400"> -->
                <p class="text-sm text-gray-800 dark:text-neutral-400">
                    Are you sure want to delete this item?
                </p>
                <!-- </div> -->
            </template>
            <template #footer>
                <button type="button" data-hs-overlay="#confirm"
                    class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 focus:outline-none focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
                    Cancel
                </button>
                <button type="button" @click="confirmDelete()" data-hs-overlay="#confirm"
                    class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-red-600 text-white hover:bg-red-700 focus:outline-none focus:bg-red-700 disabled:opacity-50 disabled:pointer-events-none">
                    Delete
                </button>
            </template>
        </Confirmation>
    </div>
    <div v-if="!isFetching && !items.length" class="font-bold text-center w-[300px] top-0 bottom-0 my-auto mx-auto right-0 left-0 absolute h-10 w-10 z-50">
    {{ $t('No records found') }}
  </div>
</template>

<script>
    import {
        AdjustmentsIcon,
        UserCircleIcon
    } from "@heroicons/vue/solid";
    import TInput from "@/components/form/Input.vue";
    // import TRadio from '@/components/form/RadioButton.vue'
    import {
        PlusIcon
    } from "@heroicons/vue/solid";
    import expertiseApi from "@/api/expertise";
    import serviceApi from "@/api/service";
    import userApi from "@/api/user";

    import DrawerRight from "@/components/form/DrawerRight.vue";
    import Confirmation from "@/components/modal/Confirmation.vue";
    import {
        HSOverlay,
        HSDropdown,
        HSSelect
    } from "preline/preline";
    import {
        HSStaticMethods
    } from "preline";
    import Multiselect from 'vue-multiselect';
    import "vue-multiselect/dist/vue-multiselect.css";
    import {
		mapGetters
	} from 'vuex';
    // import FileUpload from '@/components/form/fileUpload.vue';

    export default {
        name: "userService",
        watch: {
            isHourly() {
                if (this.isHourly) this.unitPrice = 0
                if (!this.isHourly) this.costPrice = 0
            },
        },
        components: {
            AdjustmentsIcon,
            UserCircleIcon,
            PlusIcon,
            DrawerRight,
            TInput,
            Confirmation,
            Multiselect,
            // FileUpload
        },
        computed: {
            ...mapGetters({
                user: 'auth/user',
                isAdmin: 'auth/isAdmin',
            }),
            selectedUserList() {
                return this.selecteduser
            },
            isUsersDisabled() {
                let isFilled = this.selectedExpertiseLevel && this.selectedExpertise
                if (isFilled) {
                    this.getAllUser()
                    return false
                }
                return true
            }
        },
        data() {
            return {
                orderBy: "name",
                sortBy: "asc",
                page: 1,
                total: 0,
                maxPage: 1,
                limit: 10,
                name: "",
                taskName: "",
                description: "",
                fileUrl: "",
                isHourly: true,
                isUnit: false,
                isAutoAssign: false,
                isAutoDueDate: false,
                isRevision: false,
                isMultiQuantity: false,
                costPrice: "",
                unitPrice: "",
                sellingPrice: "",
                selectedExpertiseLevel: null,
                selectedExpertise: "",
                selectedColumn: "",
                selectedUser: [],
                deliveryTime: 1,
                deliveryType: 'hour',
                expertises: [],
                curerentActive: -1,
                selectedItem: null,
                itemId: null,
                items: [],
                meta: null,
                isEdit: false,
                keyword: "",
                userIds: [],
                users: [],
                selectedUserKey: 0,
                isSaving: false,
                isFetching: false,
                debounceGetAll: null,
                errors: {
                    name: null,
                    costPrice: null,
                    unitPrice: null,
                    sellingPrice: null,
                    selectedExpertise: null,
                    selectedUser: null,
                },
            };
        },
        created() {
            this.debounceGetAll = this.debounce(this.getAll, 300);
            this.getAll();
            this.getAllExpertise();
            this.getAllUser();
        },
        mounted() {
            
        },
        methods: {
            validateForm() {
                this.errors.name = !this.name ? 'Service name is required' : null;
                // this.errors.costPrice = !this.costPrice && !this.isHourly ? 'Cost price is required & cannot be 0' : null;
                // this.errors.unitPrice = !this.unitPrice && this.isHourly ? 'Cost price is required & cannot be 0' : null;
                this.errors.sellingPrice = !this.sellingPrice ? 'Selling price is required & cannot be 0' : null;
                // this.errors.selectedExpertise = (this.selectedExpertise) ? 'Stock quantity is required' : null;
                // this.errors.selectedUser = (this.selectedUser) ? 'Prefered user is required' : null;

                return Object.values(this.errors).every(error => !error);
            },
            onUpload(url) {
                this.fileUrl = url;
                console.log(url)
            },
            sortAsc() {
                this.sortBy = "asc"
                this.getAll()
            },
            sortDsc() {
                this.sortBy = "desc"
                this.getAll()
            },
            onHover(item) {
                this.curerentActive = item.id;
            },
            onHoverOut(item) {
                this.curerentActive = -1;
            },
            hsSelectOption(user) {
                return {
                    'data-hs-select-option': JSON.stringify({
                        description: `${user.fullName}`,
                        icon: `<img class="inline-block rounded-full" src="${this.getUserImage(user)}" />`
                    })
                }
            },
            onMultiQuantityChange(e) {
                this.isMultiQuantity = e.target.checked;
            },
            openDropdown(item) {
                console.log("Asdasdasd")
                const el = HSDropdown.getInstance('.hs-dropdown-example');
                console.log(el)
                this.selectedItem = item
            },
            openDeleteModal(item) {
                HSOverlay.open('#confirm');
                this.selectedItem = item
            },
            onChangeRevision(e) {
                this.isRevision = !this.isRevision;
            },
            onChangeAutoAssign(e) {
                this.isAutoAssign = !this.isAutoAssign
            },
            onChangeAutoDueDate(e) {
                this.isAutoDueDate = !this.isAutoDueDate
            },
            customLabel({
                username
            }) {
                return `${username}`;
            },
            getUserImage(user) {
                if (user.imgUrl) {
                    return user.imgUrl;
                } else {
                    return this.__generateInitialCanvas(user.fullName);
                }
            },
            getAllUser() {
                const callback = (response) => {
                    const data = response.data;
                    this.users = data.filter(item => item?.role?.id !== 6);
                }
                const errCallback = (err) => {
                    console.log(err);
                }

                const params = {
                    orderBy: "fullName",
                    sortBy: "desc",
                    page: 1,
                    limit: 9999,
                    expertiseLevel: this.selectedExpertiseLevel,
                    expertiseId: this.selectedExpertise,
                }
                userApi.getList(params, callback, errCallback);
            },
            upDeliveryTime(e) {
                this.deliveryTime = this.deliveryTime + 1;
            },
            downDeliveryTime(e) {
                this.deliveryTime = this.deliveryTime - 1;
            },
            getAllExpertise() {
                const callback = (response) => {
                    const data = response.data;
                    this.expertises = data;
                };
                const errCallback = (err) => {
                    console.log(err);
                };

                const params = {
                    orderBy: "name",
                    sortBy: "asc",
                    page: 1,
                    limit: 999,
                };
                expertiseApi.getList(params, callback, errCallback);
            },
            onHourlyToggle() {
                this.isHourly = true;
            },
            onUnitoggle() {
                this.isHourly = false;
            },
            nextPage() {
                this.page = this.page + 1;
                this.getAll();

            },
            prevPage() {
                this.page = this.page - 1;
                this.getAll();

            },
            debounce(func, wait) {
                let timeout;
                return function (...args) {
                    clearTimeout(timeout);
                    timeout = setTimeout(() => {
                        func.apply(this, args);
                    }, wait);
                };
            },
            getAll(keyword = null) {
                this.isFetching = true;
                const callback = (response) => {
                    const data = response.data;
                    const meta = response.meta;
                    this.items = data;
                    this.meta = meta;
                    this.page = meta.currentPage;
                    this.maxPage = meta.lastPage;
                    this.total = meta.total;
                    setTimeout(() => {
                        HSStaticMethods.autoInit();
                    }, 500);
                    if (keyword) {
                        this.page = 1;
                    }
                    this.isFetching = false;
                };
                const errCallback = (err) => {
                    const message = err?.response?.data?.message;
                    this.__showNotif('error', 'Error', message);
                    this.isFetching = false;
                };

                const params = {
                    orderBy: this.orderBy,
                    sortBy: this.sortBy,
                    page: this.page,
                    limit: this.limit,
                };
                if (keyword) params.keyword = keyword;
                serviceApi.getList(params, callback, errCallback);
            },
            onInputSearch() {
                // this.debounce(this.getAll(this.keyword), 300); // 300ms debounce
                this.debounceGetAll(this.keyword);
            },
            confirmDelete() {
                console.log("testing");
                if (this.selectedItem) {
                    const callback = (response) => {
                        const data = response.data;
                        const message = response.message;
                        const isDelete = true;
                        if (this.items.length <= 1  && this.page > 1) this.page = this.page - 1
                        this.getAll();
                        this.__showNotif("success", "Success", message);
                    };
                    const errCallback = (err) => {
                        const message = err?.response?.data?.message;
                        this.__showNotif('error', 'Error', message);
                    };
                    const id = this.selectedItem.id;
                    serviceApi.delete(id, callback, errCallback);
                }
            },
            closeDrawer() {
                this.$refs.drawerRight.visibleRight = false;
            },
            reset() {
                this.errors =  {
                    name: null,
                    costPrice: null,
                    unitPrice: null,
                    sellingPrice: null,
                    selectedExpertise: null,
                    selectedUser: null,
                },

                this.name =  ""
                this.taskName =  ""
                this.description =  ""
                this.isHourly =  true
                this.isUnit =  false
                this.isAutoAssign =  false
                this.isAutoDueDate =  false
                this.isRevision =  false
                this.isMultiQuantity =  false
                this.costPrice =  ""
                this.unitPrice =  ""
                this.sellingPrice =  ""
                this.selectedExpertiseLevel =  null
                this.selectedExpertise =  ""
                this.selectedColumn =  ""
                this.selectedUser =  []
                this.deliveryTime =  1
                this.deliveryType =  'hour'
            },
            save() {
                if (this.validateForm()) {
                    if (!this.isEdit) {
                        this.addNew()
                    } else {
                        this.updateItem()
                    }
                    this.$refs.drawerRight.visibleRight = false;
                }
            },
            add() {
                this.isEdit = false;
                this.reset(); 
                this.edit();
                setTimeout(() => {
                    HSStaticMethods.autoInit();
                }, 500);
            },
            edit(item) {
                this.$refs.drawerRight.visibleRight = true;
                if (item) {
                    this.isEdit = true;
                    this.selectedItem = item;
                    this.selectedUserKey++
                    this.name = item.name
                    this.description = item.description
                    this.isHourly = item.isHourly
                    this.isRevision = item.isRevision
                    this.isAutoAssign = item.isAutoAssign
                    this.isAutoDueDate = item.isAutoDueDate
                    if (!this.isHourly) this.unitPrice = item.costPrice
                    if (this.isHourly) this.costPrice = item.costPrice

                    this.sellingPrice = item.sellingPrice
                    this.selectedExpertiseLevel = item.expertiseLevel
                    this.selectedExpertise = item.expertiseId
                    this.deliveryTime = item.deliveryTime
                    this.deliveryType = item.deliveryType
                    this.taskName = item.taskName
                    this.selectedColumn = item.taskColumn
                    const currentAssignTo = item.assignTo.length>=1 ?  item.assignTo[0] : null
                    if (currentAssignTo) this.selectedUser = this.users.find((user) => user.id === currentAssignTo.id) 
                    this.isMultiQuantity = item.isMultiQuantity
                    setTimeout(() => {
                        HSStaticMethods.autoInit();
                    }, 500);
                }
            },
            updateItem() {
                // this.isSaving = true;
                const callback = (response) => {
                    const message = response.message;
                    this.getAll();
                    this.__showNotif("success", "Success", message);
                    this.isSaving = false;
                };
                const errCallback = (err) => {
                    const message = err?.response?.data?.message;
                    this.__showNotif('error', 'Error', message);
                    this.isSaving = false;
                };
                const params = {
                    name: this.name,
                    description: this.description,
                    isHourly: this.isHourly,
                    isRevision: this.isRevision,
                    isAutoAssign: this.isAutoAssign,
                    isAutoDueDate: this.isAutoDueDate,
                    costPrice: this.costPrice,
                    sellingPrice: this.sellingPrice,
                    expertiseId: this.selectedExpertise,
                    expertiseLevel: this.selectedExpertiseLevel,
                    deliveryTime: this.deliveryTime,
                    deliveryType: this.deliveryType,
                    taskName: this.taskName,
                    taskColumn: this.selectedColumn,
                    assignTo: this.selectedUser?.id,
                    isMultiQuantity: this.isMultiQuantity,
                };
                if (!this.isHourly) params.costPrice = this.unitPrice
                if (this.isHourly) params.costPrice = this.costPrice
                this.isEdit = false;
                serviceApi.update(this.selectedItem.id, params, callback, errCallback);
            },
            addNew(item = null) {
                this.isSaving = true;
                if (item) {
                    this.name = item.name
                    this.description = item.description
                    this.isHourly = item.isHourly
                    this.isRevision = item.isRevision
                    this.isAutoAssign = item.isAutoAssign
                    this.isAutoDueDate = item.isAutoDueDate
                    if (!this.isHourly) this.unitPrice = item.costPrice
                    if (this.isHourly) this.costPrice = item.costPrice
                    this.sellingPrice = item.sellingPrice
                    this.selectedExpertiseLevel = item.expertiseLevel
                    this.selectedExpertise = item.expertiseId
                    this.deliveryTime = item.deliveryTime
                    this.deliveryType = item.deliveryType
                    this.taskName = item.taskName
                    this.selectedColumn = item.taskColumn
                    this.selectedUser.id = item.assignTo
                    this.isMultiQuantity = item.isMultiQuantity
                }
                const callback = (response) => {
                    const data = response.data;
                    const message = response.message;
                    this.getAll();
                    this.__showNotif("success", "Success", message);
                    this.isSaving = false;
                };
                const errCallback = (err) => {
                    const message = err?.response?.data?.message;
                    this.__showNotif('error', 'Error', message);
                    this.isSaving = false;
                };
                const params = {
                    name: this.name,
                    description: this.description,
                    isHourly: this.isHourly,
                    isRevision: this.isRevision,
                    isAutoAssign: this.isAutoAssign,
                    isAutoDueDate: this.isAutoDueDate,
                    costPrice: this.costPrice,
                    sellingPrice: this.sellingPrice,
                    expertiseId: this.selectedExpertise,
                    expertiseLevel: this.selectedExpertiseLevel,
                    deliveryTime: this.deliveryTime,
                    deliveryType: this.deliveryType,
                    taskName: this.taskName,
                    taskColumn: this.selectedColumn,
                    assignTo: this.selectedUser.id,
                    isMultiQuantity: this.isMultiQuantity,
                };
                if (!this.isHourly) params.costPrice = this.unitPrice
                if (this.isHourly) params.costPrice = this.costPrice
                serviceApi.create(params, callback, errCallback);
            },
            
        },
    };
</script>

<style>
    .user-image {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        object-fit: cover;
        margin-right: 10px;
    }

    .selected-item,
    .option-item {
        display: flex;
        align-items: center;
    }
</style>