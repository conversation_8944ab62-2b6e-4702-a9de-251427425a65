<template>
  <div class="assignee-selector-subtask" :class="{'assignee-selector-subtask-idle': !showDropdown, 'assignee-selector-subtask': showDropdown}">
    <multiselect 
      v-model="selectedUser" 
      :options="users" 
      :searchable="true" 
      :show-labels="false"
      :close-on-select="true"
      :custom-label="customLabel"
      no-result="No matches found"
      placeholder="Search for user"
      @close="closeDropdown"
      @select="select"
      @remove="removeAssign"
      @open="openDropdown"
      track-by="id"
      value="id"
      :class="{'custom-multiselect-subtask-direct-width-idle': !showDropdown, 'custom-multiselect-subtask-direct-width': showDropdown}"
      class="custom-multiselect-subtask-direct"
      label="fullName">
      <template #placeholder>
        <div class="pointer w-[24px] h-[24px] flex items-center border border-dashed border-gray-400 rounded-full pl-[6px]">
          <UserAddIcon class="h-3 w-3 text-gray-500" />
        </div>
      </template>
      <template #singleLabel="{ option }">
        <div class="flex items-center">
            <img
              v-if="option.imgUrl" 
              class="rounded-full size-7 object-cover border-2"
              :src="option.imgUrl"
              alt="avatar-image"
              referrerpolicy="no-referrer"
            >
          <div class="flex items-center " v-else>
            <div :style="{ backgroundColor: __getColorByInitial(option.fullName[0]) }" class="text-[10px] mr-2 rounded-full w-[26px] h-[26px] pt-[2px] font-medium text-center bg-white uppercase border-[1px]">
              {{ __generateInitial(option.fullName) }} 
            </div>
          </div>
        </div>
      </template>
      <template #option="{ option }">
        <div class="flex items-center">
            <img
              v-if="option.imgUrl" 
              class="rounded-full size-7 object-cover border-2"
              :src="option.imgUrl"
              alt="avatar-image"
              referrerpolicy="no-referrer"
            >
          <div class="flex items-center" v-else>
            <div :style="{ backgroundColor: __getColorByInitial(option.fullName[0]) }" class="text-[10px] text-black mr-2 rounded-full w-[26px] h-[26px] pt-[4px] font-medium text-center bg-white uppercase border-[1px]">
              {{ __generateInitial(option.fullName) }} 
            </div>
          </div>
          <span class="">{{option.fullName}}</span>
        </div>
      </template>
      <template #noResult>
        <span>No matches found</span>
      </template>
    </multiselect>
  </div>
</template>

<script>
import {
  UserAddIcon,
} from '@heroicons/vue/outline';
import Multiselect from 'vue-multiselect';
import "vue-multiselect/dist/vue-multiselect.css";
export default {
  components: { Multiselect, UserAddIcon },
  data() {
    return {
      keyword: '',
      selectedUser: null,
      showDropdown: false,
      defaultAvatar: 'path-to-default-avatar'
    };
  },
  props: {
    users: {
      type: Array,
      default: [],
    },
    currentAssignee: {
      type: Object
    },
    task: {}
  },
  mounted() {
    if (this.currentAssignee) {
      this.selectedUser = this.currentAssignee
    }
  },
  methods: {
    select(event) {
      this.$emit('select', event, this.task)
    },
    removeAssign() {
      this.$emit('remove', this.task)
    },
    closeDropdown() {
      this.showDropdown = false;
    },
    openDropdown() {
      this.showDropdown = true;
    },
    customLabel({ fullName, email }) {
      return `${fullName}`;
    },
  },
  created() {
  },
};
</script>

<style lang="scss">
.custom-multiselect-subtask-direct {

  .multiselect {
    min-height: 20px!important;
    max-height: 20px!important;
    height: 20px!important;
  }

  .multiselect__placeholder {
    // margin: 0px!important;
  }
  
  .multiselect__element {
    display: flex;
    align-items: center;
  }

  .multiselect__element img {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin-right: 8px;
  }

  .multiselect__tags {
    padding: 0!important;
    margin-top: 0;
    margin-bottom: 0;
    border: none!important;
    background-color: transparent!important;
    margin-right: -20px!important;
    margin-left: 10px;
    max-height: 20px!important;
    min-height: 20px!important;
  }

  .multiselect__select {
    display: none;
  }

  .multiselect__input {
    height: 30px!important;
    margin-left: -20px!important
  }

  .multiselect__single {
    padding-left: 0px!important;
    width: 30px;
    background-color: transparent!important;
  }

  .multiselect__content-wrapper {
    min-width: 330px!important;
    max-width: 330px!important;
    border-top: 1px rgb(216, 216, 216) solid;
    margin-left: -160px;
    margin-top: 20px;
    
  }

  .multiselect__option {
    min-width: 320px!important;
    max-width: 320px!important;
  }
}
.custom-multiselect-subtask-direct.multiselect {
    min-height: 30px!important;
  }
.custom-multiselect-subtask-direct-width {
  width: 100%;
}

.custom-multiselect-subtask-direct-width-idle {
  width: 20px;
  margin-right: 10px;
}

.assignee-selector-subtask {
  width: 100%;
  position: relative;
}

.assignee-selector-subtask-idle {
  width: 20px;
  margin-right: 10px;
  position: relative;
}
</style>
