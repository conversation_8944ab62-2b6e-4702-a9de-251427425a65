<template>
	<span class="hidden">{{ $i18n.locale }}</span>
	<Layout>
		<router-view />
	</Layout>

	<!-- Notifications -->
	<notifications
		class="mb-[70px] rounded-lg mr-3 shadow-md"
		group="app"
		:ignoreDuplicates="true"
		position="bottom right"
		:max="3"
	>
		<template #body="props">
			<div
				class="max-w-[26em] w-full bg-white rounded-lg pointer-events-auto border-1 overflow-hidden mt-2 z-[999]"
			>
				<div class="p-4">
					<div class="flex items-start">
						<div class="flex">
							<CheckCircleIcon
								v-if="props.item.type === 'success'"
								class="h-6 w-6 ml-1 text-green-600"
								aria-hidden="true"
							/>
							<XCircleIcon
								v-if="props.item.type === 'error'"
								class="h-6 w-6 ml-1 text-red-600"
								aria-hidden="true"
							/>
							<InformationCircleIcon
								v-if="props.item.type === 'warn' || props.item.type === 'warning'"
								class="h-6 w-6 ml-1 text-orange-600"
								aria-hidden="true"
							/>
							<div
								v-if="props.item.type === 'register'"
							>
								<svg
									style="
								width:24px;height:24px"
									viewBox="0 0 24 24"
								>
									<path
										fill="#3fb14d"
										d="M21.1,12.5L22.5,13.91L15.97,20.5L12.5,17L13.9,15.59L15.97,17.67L21.1,12.5M10,17L13,20H3V18C3,15.79 6.58,14 11,14L12.89,14.11L10,17M11,4A4,4 0 0,1 15,8A4,4 0 0,1 11,12A4,4 0 0,1 7,8A4,4 0 0,1 11,4Z"
									/>
								</svg>
							</div>
						</div>
						<div class="ml-3 w-0 flex-1 pt-0.5">
							<p
								class="text-sm text-gray-500"
								v-html="props.item.text"
							/>
						</div>
						<div
							class="close"
							@click="close"
						>
							<button
								class="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
								@click="props.close"
							>
								<span class="sr-only">Close</span>
								<XIcon
									class="h-5 w-5"
									aria-hidden="true"
								/>
							</button>
						</div>
					</div>
				</div>
			</div>
		</template>
	</notifications>

	<!-- with button -->
	<notifications
		class="mb-3 rounded-lg mr-3"
		group="button"
		:ignoreDuplicates="true"
		position="bottom right"
		:max="3"
		:closeOnClick="true"
	>
		<template #body="props">
			<div
				class="max-w-[26em] w-full bg-white rounded-lg pointer-events-auto border-1 overflow-hidden mt-2"
			>
				<div class="p-4">
					<div class="flex items-start">
						<div class="flex-shrink-0">
							<CheckCircleIcon
								v-if="props.item.type === 'success'"
								class="h-6 w-6 text-green-600"
								aria-hidden="true"
							/>
							<XCircleIcon
								v-if="props.item.type === 'error'"
								class="h-6 w-6 text-red-600"
								aria-hidden="true"
							/>
							<InformationCircleIcon
								v-if="props.item.type === 'warn' || props.item.type === 'warning'"
								class="h-6 w-6 text-orange-600"
								aria-hidden="true"
							/>
						</div>
						<div class="ml-3 w-0 flex-1 pt-0.5">
							<p class="text-sm text-gray-500">
								{{ props.item.text }}
							</p>
						</div>
						<div
							class="close"
							@click="close"
						>
							<button
								class="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
								@click="props.close"
							>
								<span class="sr-only">Close</span>
								<XIcon
									class="h-5 w-5"
									aria-hidden="true"
								/>
							</button>
						</div>
					</div>
					<t-button
						:color="`secondary-solid`"
						class="ml-9 mt-2 ml-0 text-black"
						@click="openUrl(props.item.data.url)"
					>
						{{ props.item.data.btnCaption }} 
					</t-button>	
				</div>
			</div>
		</template>
	</notifications>

	<!-- Notification refresh for new content -->
	<div
		aria-live="assertive"
		class="fixed inset-0 flex items-end px-4 py-6 pointer-events-none sm:items-start z-50"
	>
		<div class="w-full flex flex-col items-center space-y-4 sm:items-start fixed bottom-0 sm:m-4 left-0 ml-0 mb-4">
			<!-- Notification panel, dynamically insert this into the live region when it needs to be displayed -->
			<transition
				enterActiveClass="transform ease-out duration-300 transition"
				enterFromClass="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2"
				enterToClass="translate-y-0 opacity-100 sm:translate-x-0"
				leaveActiveClass="transition ease-in duration-100"
				leaveFromClass="opacity-100"
				leaveToClass="opacity-0"
			>
				<div
					v-if="__needRefresh"
					class="max-w-[24em] sm:min-w-[23em] w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden"
				>
					<div class="py-4 px-2">
						<div class="flex items-start">
							<div class="flex-shrink-0">
								<RefreshIcon
									class="h-6 w-6 text-gray-400"
									aria-hidden="true"
								/>
							</div>
							<div class="ml-3 w-0 flex-1 pt-0.5">
								<p class="text-sm font-medium text-gray-900">
									Updates available
								</p>
								<p class="mt-1 text-sm text-gray-500 sm:min-w-[23em]">
									Updates and has been downloaded <br>
									Click refresh to load the new version of Desidia
								</p>
								<div class="mt-3 flex space-x-7">
									<button
										type="button"
										class="py-2 bg-white rounded-md text-sm font-medium text-primary-600 hover:text-primary-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
										@click="refreshPage()"
									>
										Refresh
									</button>
									<button
										type="button"
										class="px-3 py-2 bg-white rounded-md text-sm font-medium text-gray-700 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
										@click="__closePromptUpdateSW"
									>
										Dismiss
									</button>
								</div>
							</div>
							<div class="ml-4 flex-shrink-0 flex">
								<button
									class="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
									@click="__closePromptUpdateSW"
								>
									<span class="sr-only">Close</span>
									<XIcon
										class="h-5 w-5"
										aria-hidden="true"
									/>
								</button>
							</div>
						</div>
					</div>
				</div>
			</transition>
		</div>
	</div>
</template>

<script>
import localforage from 'localforage';
import { mapGetters, mapActions } from 'vuex';
import useRegisterSW from '@/mixins/useRegisterSW';
import Layout from '@/layouts/index.vue';
import { RefreshIcon, InformationCircleIcon, CheckCircleIcon, XCircleIcon } from '@heroicons/vue/outline';
import { XIcon, ExclamationIcon } from '@heroicons/vue/solid';
import TButton from '@/components/global/Button.vue';
import TModal from '@/components/global/Modal.vue';

export default {
	components: {
		Layout,
		RefreshIcon,
		XIcon,
		TButton,
		InformationCircleIcon,
		CheckCircleIcon,
		XCircleIcon,
	},
	mixins: [useRegisterSW],
	setup() {},
	data () {
		return {
			text: {
				next_cta: 'NEXT',
				prev_cta: 'PREV',
				restart_cta: 'RESTART',
			},
			theme: {
				color: '#333333 ',
				radius: '2px',
			},
			section: '',
		};
	},
	computed: {
		...mapGetters({
			user: 'auth/user',
			getToken: 'auth/getToken',
			isShowOnboard: 'auth/getIsShowOnboard'
		}),
		notAllowOnBoardPage() {
			const allowedPaths = [
				'/login',
				'/logout',
				'/forgot-password',
				'/password/reset',
				'/register',
				'/magic-link',
				'/change-password',
				'/auto_login',
				'/user/verify',
				'/confirmation-email',
				'/resend-email',
				'/embed',
				'/twitter',
				'/shareable_form',
				'/not-found',
				'/reset',
				'/templates/preview',
				'/user/verify',
			];
			const currentPath = this.$route.path;
			// Check if the current path is not included in allowedPaths
			if (!allowedPaths.includes(currentPath)) {
				// Perform actions for paths not in the allowedPaths array
				return false;
			}
			return true;
		}
	},
	created() {
		this.init();
		this.removeExpiredItems();
	},
	mounted() {
		this.$nextTick( () => {
			// this.$soketio.on('connect', (data) => {
			// });
			
			// this.$soketio.on('user_updated', (data) => {
			// 	this.setUser(data);
			// });
		});
	},
	beforeUnmount () {
		const roomId = `user_${this.user && this.user.id ? this.user.id : ''}`;
		// this.$soketio.emit('leave', roomId);
		window.removeEventListener('resize', this.onResize);

		
		// remove listener on all data in local storage
		const renderList = JSON.parse(localStorage.getItem('renderList'));
		if (renderList && renderList.length > 0) {
			for (let index = 0; index < renderList.length; index++) {
				const element = renderList[index];
				// this.$soketio.removeListener(element.key);
				// this.$soketio.removeListener(`finish_${element.key}`);
				// disconnect bite render
				const roomIdBite = `bite_${element.bite.id}`;
				// this.$soketio.emit('leave', roomIdBite);
			}
		}
		
		// this.$soketio.removeListener('connect');
		// this.$soketio.removeListener('bite_finish_render');
		// this.$soketio.removeListener('bite_progress_render');
		// this.$soketio.removeListener('disconnect');
	},
	methods: {
		...mapActions({
			fetchUser: 'auth/fetchUser',
			setUser: 'auth/setUser',
			setIsShowOnboard: 'auth/setIsShowOnboard'
		}),
		convertRelated(data) {
			const topic = JSON.stringify(data);
			const result = JSON.parse(topic);
			return result;
		},
		
		init() {
			const outside = this.$route.query.outside;
			let isOutside = false;
			if (typeof(sceneIndex) !== 'undefined' && outside === '1') isOutside = true;
			if (this.getToken && !this.user && !isOutside) this.fetchUser();
			const roomId = `user_${this.user && this.user.id ? this.user.id : ''}`;
			// this.$soketio.emit('join', roomId);
		},
		openUrl(url) {
			this.$router.push(url);
		},
		refreshPage() {
			this.__updateServiceWorker();
			window.location.reload();
		},

		// remove all cache based on expired date
		removeExpiredItems() {
			const currentTimestamp = Date.now();
			// Iterate over all keys in LocalForage
			localforage.keys().then((keys) => {
				keys.forEach((key) => {
					localforage.getItem(key).then((item) => {
						if (item && item.expires && item.expires <= currentTimestamp) {
							// Item has expired, remove it from LocalForage
							localforage.removeItem(key).then(() => {
								console.log('Expired item removed:', key);
							}).catch((error) => {
								console.error('Error removing expired item:', key, error);
							});
						}
					}).catch((error) => {
						console.error('Error retrieving item:', key, error);
					});
				});
			}).catch((error) => {
				console.error('Error retrieving keys:', error);
			});
		}
	}
};
</script>

<style lang="scss">
	@import '@/assets/scss/main.scss';
	.flex-related {
		display: flex;
		flex-wrap: wrap;
	}

	.related-topics {
		height: 70.25px!important;
	}

	.line-break-related {
		width: 100%;
	}

</style>