<template>
  <div class="calendar-add" >
    <VueDatePicker 
      @blur="datePickerBlur" 
      @date-update="datePickerSelect" 
      :class="{
        'custom-datepicker-empty dp__input_icon_pad_empty': !date,
        'custom-datepicker dp__input_icon_pad': date,
        'text-green': isTodayOrTomorrow,
        'text-red': isYesterday
      }"
      v-model="date" 
      :markers="markers" 
      auto-apply 
      :enable-time-picker="false" 
      :clearable="true" 
      :format="formatDate" 
      :disabled-dates="disablePastDates">

      <!-- Month-year navigation template -->
      <template #month-year="{ month, year, months, years, updateMonthYear, handleMonthYearChange }">
        <!-- Month/Year controls here -->
      </template>

      <!-- Marker template -->
      <template #marker="{ marker, day, date }">
        <span class="custom-marker"></span>
      </template>

      <!-- Action buttons -->
      <template #action="{ clearValue }">
        <button class="custom-clear-button" @click="clearValue">Clear</button>
      </template>
      
      <template #action-extra="{ clearValue }">
        <button class="custom-clear-button mt-4 mb-2" @click="clearDate">Clear</button>
      </template>
    </VueDatePicker>

  </div>
</template>


<script>
import VueDatePicker from "@vuepic/vue-datepicker";
import "@vuepic/vue-datepicker/dist/main.css";
import addDays from 'date-fns/addDays';
import isBefore from 'date-fns/isBefore';
import startOfDay from 'date-fns/startOfDay';
import TButton from '@/components/global/Button.vue';
import { ref } from 'vue';

export default {
  components: { VueDatePicker, TButton },
  data() {
    return {
      isSaving: false,
      date: null,
      markers2: [
        { date: addDays(new Date(), 1), type: 'dot', tooltip: [{ text: 'Dot with tooltip', color: 'green' }] },
        { date: addDays(new Date(), 2), type: 'line', tooltip: [{ text: 'First tooltip', color: 'blue' }, { text: 'Second tooltip', color: 'yellow' }] },
        { date: addDays(new Date(), 3), type: 'dot', color: 'yellow' },
      ],
      fullMonths: [
        { text: 'January', value: 0 }, { text: 'February', value: 1 }, { text: 'March', value: 2 },
        { text: 'April', value: 3 }, { text: 'May', value: 4 }, { text: 'June', value: 5 },
        { text: 'July', value: 6 }, { text: 'August', value: 7 }, { text: 'September', value: 8 },
        { text: 'October', value: 9 }, { text: 'November', value: 10 }, { text: 'December', value: 11 },
      ],
      hours: 12, // Default hour
      ampm: 'PM', // Default AM/PM
      hoursExtend: [
        { label: '2 HRS', value: 2 },
        { label: '4 HRS', value: 4 },
        { label: '8 HRS', value: 8 },
      ],
      duration: 2,
    };
  },
  props: {
    isShowEstimated: {
      type: Boolean,
      default: false,
    },
    isButtonAction: {
      type: Boolean,
      default: false,
    },
    isBooking: {
      type: Boolean,
      default: false,
    },
    schedules: {
      type: Array,
      default: [],
    },
    currentDate: {
      type: String,
    }
  },
  computed: {
    hoursArray() {
      return Array.from({ length: 12 }, (_, i) => ({
        value: i + 1,
        text: (i + 1).toString().padStart(2, '0'),
      }));
    },
    isFormValid() {
      return this.date ;
    },
    
    markers() {
      return this.schedules
      .filter(item => item.status === "Not Available")
      .map(item => ({
        date: new Date(item.date),
        type: 'dot'
      }));
    },
    formattedDate() {
      if (!this.date) return '';
      const options = { day: 'numeric', month: 'short' };
      return new Intl.DateTimeFormat('en-US', options).format(this.date);
    },
    isTodayOrTomorrow() {
      const diffInDays = this.getDiffInDays(this.date);
      return diffInDays === 0 || diffInDays === 1;
    },
    isYesterday() {
      const diffInDays = this.getDiffInDays(this.date);
      return diffInDays === -1;
    }
  },
  mounted() {
    this.duration = 2,
    this.$emit('updateDatepicker', this.date);
    if (this.currentDate) {
      this.date = this.currentDate
    }
  },
  updated() {
  },
  methods: {
    datePickerSelect(date) {
      this.$emit('select', date)
    },
    clearDate() {
      this.date = null;
      this.$emit('select', null)
      this.$emit('updateDatepicker', null);
    },
    getDiffInDays(date) {
    if (!date) return null;

      const currentDate = startOfDay(new Date()); // normalize current date to start of the day
      const selectedDate = startOfDay(new Date(date)); // normalize selected date to start of the day

      const diffInTime = selectedDate - currentDate; // difference in milliseconds
      return Math.floor(diffInTime / (1000 * 60 * 60 * 24)); // convert milliseconds to days
    },
    formatDate(date) {
      const diffInDays = this.getDiffInDays(date);
      if (diffInDays === 0) {
        return 'Today';
      } else if (diffInDays === -1) {
        return 'Yesterday';
      } else if (diffInDays === 1) {
        return 'Tomorrow';
      } else {
        const options = { day: 'numeric', month: 'short' };
        return new Intl.DateTimeFormat('en-US', options).format(date);
      }
    },
    selectDuration(hour) {
      this.duration = hour.value;
      this.$emit('updateDuration', hour.value);
    },
    bookStudio() {
      this.$emit('actionRight');
    },
    customizeEvent() {
      this.$emit('actionLeft');
    },
    onHourChange(value) {
      this.hours = value;
      this.updateDate();
    },
    onAmPmChange(value) {
      this.ampm = value;
      this.updateDate();
    },
    updateDate() {
      let hours = parseInt(this.hours);
      if (this.ampm === 'PM' && hours !== 12) {
        hours += 12;
      } else if (this.ampm === 'AM' && hours === 12) {
        hours = 0;
      }

      const date = new Date(this.date);
      date.setHours(hours);
      this.date = date;
    },
    updateMonth(event, updateMonthYear, year) {
      updateMonthYear(+event.target.value, year);
    },
    updateYear(event, updateMonthYear, month) {
      updateMonthYear(month, +event.target.value);
    },
    isHourUnavailable(hour) {
      if (!this.date) return false;

      const selectedDate = this.date.toISOString().split('T')[0];
      const unavailableEntry = this.schedules.find(schedule => schedule.date === selectedDate);
      
      if (!unavailableEntry) return false;

      let convertedHour = parseInt(hour);
      if (this.ampm === 'PM' && convertedHour !== 12) {
        convertedHour += 12;
      } else if (this.ampm === 'AM' && convertedHour === 12) {
        convertedHour = 0;
      }

      const hourString = convertedHour.toString().padStart(2, '0') + ':00';
      return unavailableEntry.hours.includes(hourString);
    },
    disablePastDates(date) {
      return isBefore(date, startOfDay(new Date()));
    },
  },
  watch: {
    date() {
      // Emit the updated date with minutes set to 00
      this.$emit('updateDatepicker', this.date);
    },
  },
};
</script>



<style lang="scss">
.calendar-add {
  .dp--clear-btn {
      display: none; /* Hide the clear button */
    }
    .dp__input {
      font-size: 12px;
    }
  .custom-datepicker .dp__input_icons {
    border: 1px dashed gray;
    border-radius:100%;
    width: 14px;
    height: 14px;
    stroke-width: 2px!important;
    padding: 4px;
    display: none;
  }
  .custom-datepicker-empty .dp__input_icons {
    border: 1px dashed gray;
    border-radius:100%;
    width: 14px;
    height: 14px;
    stroke-width: 2px!important;
    padding: 4px;
  }
    .dp__pointer {
      border: none;
      margin-right: -70px;
      background: transparent!important;
    }
    .dp__input_icon_pad_empty {
      width: 20px;
      padding-left: 0px;
      margin: 0px
    }
    .dp__input_icon_pad {
      width: 62px;
      padding: 0px;
      margin: 0px
    }
  .dp__arrow_top {
    display: none;
    margin-right: -30px
    
  }
  .dp-input {
    padding-right: 0px;
  }
  .dp--clear-btn {
    margin-right: -14px;
    margin: 0;
    padding: 0px;
  }
    .dp__menu {
      border: none;
      padding: 5px;
      margin-left: 120px;
    }
    .dp__menu_inner {
      padding: 0;
    }

    .dp__flex_display {
      display: block !important;
    }

    .custom-time-picker-component {
      align-items: center;
      justify-content: center;
    }

    .dp__today {
      border: none;
      font-weight: bold;
      color: var(--dp-primary-color);
    }

    .time-input {
      background-image: none;
    }

    .dp__calendar_header {
      font-weight: lighter;
      color: grey;
    }

    .dp__calendar_row {
      justify-content: space-between;
      padding: 0;
      margin: 0;
    }

    .dp__active_date {
      border-radius: 100%;
      color: white;
      // font-weight: lighter;
      background-color: #007bff!important;
    }

    .dp__calendar_item {
      box-sizing: border-box;
      /* Include padding and border in the element's total width and height */
      border: 1px solid #ccc;
      /* Border color */
      /* Adjust padding for better spacing */
      margin: 0;
      /* Remove margin to align items properly */
      text-align: center;
      /* Center align text */
      position: relative;
      /* Relative position for markers */
      width: 38px;
    }
    

    .dp__calendar_item[data-marker] {
      background-color: #f8d7da;
      /* Background color for marked dates */
      border: 1px solid #f5c6cb;
      /* Border color for marked dates */
    }

    .month-input {
      border: none;
      min-width: 80px;
      padding-right: 1rem;
      background-image: none;
    }

    .year-input {
      border: none;
      width: 80px;
      padding-right: 0.5rem;
      background-image: none;
    }

    .custom-month-year-component {
      display: flex;
      align-items: center;
      margin: 0 auto;
    }

    .icons {
      display: flex;
      box-sizing: border-box;
    }

    .custom-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      width: 25px;
      color: var(--dp-icon-color);
      text-align: center;

      svg {
        height: 20px;
        width: 20px;
      }

      &:hover {
        background: var(--dp-hover-color);
      }
    }


    .custom-marker {
      position: absolute;
      top: -6px;
      left: -11px;
      height: 45px;
      width: 58px;
      background-color: #ED7070;
      pointer-events: none;
      /* Disable pointer events on markers */
      z-index: -1;
    }

    .dp__calendar_item.selected {
      background-color: #007bff;
      /* Background color for selected date */
      color: white;
      /* Text color for selected date */
    }

    .dp__pointer {
      z-index: 2;
    }

    .custom-clear-button {
    background-color: #2563EB;
    color: white;
    padding: 5px 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 10px;
  }

  .custom-clear-button:hover {
    background-color: #2563EB;
  }

  .text-green .dp__input_icon_pad {
    border: none;
    margin-right: -70px;
    background: transparent!important;
    color: green!important;
  }

  .text-red .dp__input_icon_pad {
    border: none;
    margin-right: -70px;
    background: transparent!important;
    color: red!important;

  }

}
</style>
