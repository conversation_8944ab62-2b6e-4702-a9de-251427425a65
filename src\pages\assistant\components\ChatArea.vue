<template>
  <div class="h-full bg-white flex flex-col">
    <!-- Chat Messages -->
    <div class="flex-1 overflow-y-auto p-6" ref="messagesContainer">
      <!-- Welcome Message -->
      <div v-if="messages.length === 0" class="text-center py-16">
        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">
          {{ hasSource ? 'Ask questions about your sources' : 'Upload sources to get started' }}
        </h3>
        <p class="text-gray-600 max-w-md mx-auto">
          {{ hasSource 
            ? 'I can help you analyze, summarize, and answer questions about your uploaded documents.' 
            : 'Add documents, websites, or audio files to start chatting with your sources.'
          }}
        </p>
      </div>
      
      <!-- Messages -->
      <div v-else class="space-y-6">
        <div 
          v-for="message in messages" 
          :key="message.id"
          :class="[
            'flex',
            message.role === 'user' ? 'justify-end' : 'justify-start'
          ]"
        >
          <div 
            :class="[
              'max-w-[80%] rounded-lg px-4 py-3',
              message.role === 'user' 
                ? 'bg-blue-600 text-white' 
                : 'bg-gray-100 text-gray-900'
            ]"
          >
            <!-- User Message -->
            <div v-if="message.role === 'user'" class="text-sm">
              {{ message.content }}
            </div>
            
            <!-- Assistant Message -->
            <div v-else class="space-y-3">
              <MarkdownRenderer
                :content="message.content"
                :citations="message.citations || []"
                @citation-click="handleCitationClick"
              />

              <!-- Save to Note Button -->
              <div class="flex justify-start mt-2">
                <SaveToNoteButton
                  :content="message.content"
                  :notebook-id="notebookId"
                />
              </div>
            </div>
          </div>
        </div>
        
        <!-- Typing Indicator -->
        <div v-if="isTyping" class="flex justify-start">
          <div class="bg-gray-100 rounded-lg px-4 py-3 max-w-[80%]">
            <div class="flex space-x-1">
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Input Area -->
    <div class="border-t border-gray-200 p-4">
      <div class="flex space-x-3">
        <div class="flex-1">
          <textarea
            v-model="inputMessage"
            @keydown="handleKeyDown"
            :placeholder="hasSource ? 'Ask a question about your sources...' : 'Upload sources first to start chatting'"
            :disabled="!hasSource || isSending"
            class="w-full resize-none border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500"
            rows="3"
            ref="messageInput"
          ></textarea>
        </div>
        <div class="flex flex-col justify-end">
          <button
            @click="sendMessage"
            :disabled="!canSend"
            :class="[
              'p-3 rounded-lg transition-colors',
              canSend
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            ]"
          >
            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
            </svg>
          </button>
        </div>
      </div>
      
      <!-- Suggested Questions -->
      <div v-if="hasSource && messages.length === 0" class="mt-4">
        <div class="text-xs text-gray-500 mb-2">Suggested questions:</div>
        <div class="flex flex-wrap gap-2">
          <button
            v-for="suggestion in suggestedQuestions"
            :key="suggestion"
            @click="inputMessage = suggestion"
            class="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors"
          >
            {{ suggestion }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import TButton from '@/components/global/Button.vue';
import MarkdownRenderer from './MarkdownRenderer.vue';
import SaveToNoteButton from './SaveToNoteButton.vue';

export default {
  name: 'ChatArea',
  components: {
    TButton,
    MarkdownRenderer,
    SaveToNoteButton,
  },
  props: {
    hasSource: {
      type: Boolean,
      default: false
    },
    notebookId: {
      type: String,
      required: true
    },
    notebook: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      inputMessage: '',
      messages: [],
      isTyping: false,
      isSending: false,
      suggestedQuestions: [
        'Summarize the main points',
        'What are the key findings?',
        'Create an outline',
        'What questions does this raise?',
        'Extract important quotes',
        'Compare different viewpoints',
        'Generate action items',
        'Identify potential issues'
      ]
    };
  },
  computed: {
    canSend() {
      return this.hasSource && this.inputMessage.trim() && !this.isSending;
    }
  },
  methods: {
    async sendMessage() {
      if (!this.canSend) return;
      
      const userMessage = {
        id: Date.now().toString(),
        role: 'user',
        content: this.inputMessage.trim(),
        timestamp: new Date().toISOString()
      };
      
      this.messages.push(userMessage);
      const messageContent = this.inputMessage;
      this.inputMessage = '';
      this.isSending = true;
      this.isTyping = true;
      
      // Scroll to bottom
      this.$nextTick(() => {
        this.scrollToBottom();
      });
      
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        const assistantMessage = {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: {
            segments: [
              { text: `I understand you're asking about "${messageContent}". Based on your uploaded sources, here's what I found:\n\n` },
              {
                text: "This is a simulated response that would normally be generated by analyzing your documents.",
                citation_id: 1
              },
              { text: " The AI would provide insights, summaries, and answers based on the content you've uploaded." },
              {
                text: " Here's another piece of information from a different source.",
                citation_id: 2
              }
            ],
            citations: [
              {
                citation_id: 1,
                source_id: 'source-1',
                source_title: 'Research Document.pdf',
                source_type: 'pdf',
                chunk_lines_from: 15,
                chunk_lines_to: 25,
                chunk_index: 1,
                excerpt: 'This is a sample citation from your uploaded document that supports the response.'
              },
              {
                citation_id: 2,
                source_id: 'source-2',
                source_title: 'Technical Guidelines.docx',
                source_type: 'docx',
                chunk_lines_from: 42,
                chunk_lines_to: 48,
                chunk_index: 2,
                excerpt: 'Additional supporting information from another document.'
              }
            ]
          },
          timestamp: new Date().toISOString()
        };
        
        this.messages.push(assistantMessage);
        
        // Scroll to bottom
        this.$nextTick(() => {
          this.scrollToBottom();
        });
        
      } catch (error) {
        console.error('Failed to send message:', error);
        this.__showNotif('error', 'Error', 'Failed to send message');
      } finally {
        this.isTyping = false;
        this.isSending = false;
      }
    },
    
    handleKeyDown(event) {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        this.sendMessage();
      }
    },
    
    handleCitationClick(citation) {
      console.log('ChatArea: Citation clicked, emitting to parent', {
        citationId: citation.citation_id,
        sourceId: citation.source_id,
        sourceTitle: citation.source_title,
        chunkLinesFrom: citation.chunk_lines_from,
        chunkLinesTo: citation.chunk_lines_to
      });
      this.$emit('citation-click', citation);
    },
    
    scrollToBottom() {
      const container = this.$refs.messagesContainer;
      if (container) {
        container.scrollTop = container.scrollHeight;
      }
    }
  }
};
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
