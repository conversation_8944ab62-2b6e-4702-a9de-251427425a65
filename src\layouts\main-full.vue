<template>
	<!-- Meta -->
	<div class="h-screen flex overflow-hidden bg-gray-100">
		<main class="flex-1 relative overflow-y-auto focus:outline-none">
			<div>
				<router-view v-slot="{ Component }">
					<!-- <transition name="slide"> -->
					<component :is="Component" />
				<!-- </transition> -->
				</router-view>
			</div>
		</main>
	</div>
</template>

<script>

export default {
	components: {
	},
	setup() {
	},
	data () {
		return {
			isChrome: false,
		};
	},
	created () {
		this.browserName();
	},
	methods: {
		browserName(agent) {        
			let userAgent = navigator.userAgent;
			let browserName;
			if (userAgent.match(/chrome|chromium|crios/i)) {
				browserName = "chrome";
			} else if (userAgent.match(/firefox|fxios/i)) {
				browserName = "firefox";
			}  else if (userAgent.match(/safari/i)) {
				browserName = "safari";
			} else if (userAgent.match(/opr\//i)) {
				browserName = "opera";
			} else if (userAgent.match(/edg/i)) {
				browserName = "edge";
			} else {
				browserName="No browser detection";
			}
			if (browserName != 'chrome') { 
				this.isChrome = false;
			} else {
				this.isChrome = true;
			}
		},
	},
};
</script>