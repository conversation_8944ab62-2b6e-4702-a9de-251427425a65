.vue-notification-group {
    display: block;
    position: fixed;
    z-index: 999899999!important;
	bottom: 0 !important;
}
.vue-notification {
	// styling
	margin: 0 5px 5px;
	padding: 12px 10px !important;
	border-radius: 6px;
	color: #ffffff;
	font-size: 14px !important;

	// default (primary)
	background: #F24822;
	border-left: 5px solid #d73502;

	// types (green, amber, red)
	&.success {
		background: #68cd86;
		border-left-color: #42a85f;
	}

	&.warn {
		background: #ffb648;
		border-left-color: #f48a06;
	}

	&.error {
		background: #e54d42;
		border-left-color: #b82e24;
	}
}