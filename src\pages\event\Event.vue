<template>
  <!-- ===  -->
  <!-- STEP 1 -->
  <!-- ===  -->
  <!-- event type -->
  <div v-show="!isBookingComplete">
    <div v-if="isShowSelectType && !isCustomize" class="relative">
      <div class="flex justify-center mb-4">
        <div class="w-full md:w-[480px] min-h-[70vh]">
          <!-- tab -->
          <div class="">
            <div class="border-b border-gray-200 dark:border-neutral-700">
              <nav class="flex gap-x-1 w-full" aria-label="Tabs" role="tablist" aria-orientation="horizontal">
                <button type="button"
                  class="hs-tab-active:font-semibold hs-tab-active:border-blue-600 hs-tab-active:text-blue-600 py-4 px-1 inline-flex items-center gap-x-2 border-b-2 border-transparent text-sm whitespace-nowrap text-gray-500 hover:text-blue-600 focus:outline-none focus:text-blue-600 disabled:opacity-50 disabled:pointer-events-none active"
                  id="tabs-with-underline-item-1" aria-selected="true" data-hs-tab="#tabs-with-underline-1"
                  aria-controls="tabs-with-underline-1" role="tab">
                  <div class="flex items-center">
                    {{ $t('Event Type') }}
                    <div data-hs-overlay="#drawer-right-information" class="ml-2 pointer">
                      <InformationCircleIcon class="h-6 w-6 text-primary-600" aria-hidden="true"></InformationCircleIcon>
                    </div>
                  </div>
                </button>
                <button type="button"
                  v-if="previousPackages.length"
                  class="hs-tab-active:font-semibold hs-tab-active:border-blue-600 hs-tab-active:text-blue-600 py-4 px-1 inline-flex items-center gap-x-2 border-b-2 border-transparent text-sm whitespace-nowrap text-gray-500 hover:text-blue-600 focus:outline-none focus:text-blue-600 disabled:opacity-50 disabled:pointer-events-none"
                  id="tabs-with-underline-item-2" aria-selected="false" data-hs-tab="#tabs-with-underline-2"
                  aria-controls="tabs-with-underline-2" role="tab">
                  {{ $t('Previous Event') }}
                </button>
              </nav>
            </div>

            <div class="mt-3">
              <div id="tabs-with-underline-1" role="tabpanel" aria-labelledby="tabs-with-underline-item-1">
                <VueMultiselect @select="typeEventCustom = ''" v-model="typeEvent" :options="packages" :multiple="false" :closeOnSelect="true"
                  placeholder="What is the type of your event" label="name" trackBy="id" />
              </div>
              <div id="tabs-with-underline-2" class="hidden" role="tabpanel" aria-labelledby="tabs-with-underline-item-2">
                <VueMultiselect @select="typeEvent = ''" v-model="typeEventCustom" :options="previousPackages" :multiple="false" :closeOnSelect="true"
                  placeholder="Select from your previous event" label="name" trackBy="id" />
              </div>
              <div class="flex justify-end mt-4">
                <t-button :color="`primary-solid`" :isLoading="isContinue" :is-disabled="!typeEvent && !typeEventCustom"
                  @click="continueBooking()">
                  {{ $t('Continue') }}
                </t-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- ===  -->
    <!-- STEP 2 -->
    <!-- ===  -->
    <!-- select booking date -->
    <div v-show="!isShowSelectType && !isCustomize">
      <!-- <div class="flex justify-center mb-4">
        <label class="w-full md:w-[480px] text-left text-2xl font-medium">{{ $t('Book Now') }}</label>
      </div> -->
      <div class="flex justify-center mb-4">
        <div class="w-full md:w-[480px]">
          <div class="flex items-center mb-2">
            <div class="text-sm font-bold">{{ $t('What is the type of your event?') }}</div>
            <div class="pointer" data-hs-overlay="#drawer-right-information">
              <InformationCircleIcon class="h-6 w-6 text-primary-600 ml-1" aria-hidden="true"></InformationCircleIcon>
            </div>
          </div>
          <VueMultiselect @select="reInitType()" v-if="typeEvent" v-model="typeEvent" :options="packages"
            :multiple="false" :closeOnSelect="true" placeholder="What is the type of your event" label="name" trackBy="id" />
          <VueMultiselect @select="reInitType()" v-if="typeEventCustom" v-model="typeEventCustom" :options="previousPackages"
            :multiple="false" :closeOnSelect="true" placeholder="What is the type of your event" label="name" trackBy="id" />
        </div>
      </div>
      <div class="">
        <div class="flex justify-center mb-2">
          <div class="w-full md:w-[480px] text-xs text-gray-400">
            {{ $t('Based on your selection') }}
          </div>
        </div>
        <div class="flex justify-center">
          <div
            class="p-4 w-full md:w-[480px] min-h-[70px] shadow rounded-md w-full border border-gray-300 hover:bg-[#2563EB] hover:text-white mb-6" :class="{'flex items-center': !typeEvent?.studio && typeEventCustom?.studio}">
            <div class="font-bold">{{ typeEvent?.studio || typeEventCustom?.studio || 'Off-site Event' }}</div>
            <div class="font-light text-xs">{{ typeEvent?.description || typeEventCustom?.description }}</div>
          </div>
        </div>
      </div>
      <!-- link to event type / previous event -->
      <div class="flex justify-center mb-2">
        <!-- datepicker -->
        <div class="w-full md:w-[480px] pointer">
          <div class="text-sm font-bold mb-2">{{ $t('When is the event?') }}</div>
          <datepicker ref="datePickerBooking" class=" bg-white" :schedules="schedules" :isBooking="isBooking" :isShowEstimated="true" :isButtonAction="true" @actionLeft="customizeEvent"
            @actionRight="bookingEvent" @updateDatepicker="updateDatepicker" @updateDuration="updateDuration" @fetchShcedules="getProjectsSchedule"/>
        </div>
      </div>
    </div>

    <!-- ===  -->
    <!-- CUSTOM EVENT -->
    <!-- ===  -->
    <!-- select items -->
    <div v-show="isCustomize">
      <itemPackage :packageItems="packageItems" @selectDateCustomize="selectDateCustomize"></itemPackage>
    </div>

    <!-- support -->
    <div class="flex justify-center my-6 pointer text-primary-600">
      <div class="w-full md:w-[480px] flex items-start">
        <ChatAlt2Icon class="h-8 w-8 ml-2" aria-hidden="true"></ChatAlt2Icon>
        <div class="ml-2 text-sm ">
          {{ $t('Can not find what you need for your event? contact our representative to help you') }}</div>
      </div>
    </div>

    <!-- drawer information -->
    <PrelineDrawer :id="'drawer-right-information'" ref="PrelineDrawer">
      <template #header>
        <div class="">
          <div class="flex mt-4 pb-2">
            <p id="hs-pro-dutoo-label" class="text-sm font-semibold text-gray-800 ml-6 ">
              {{ $t('Information') }}
            </p>
          </div>
        </div>
      </template>
      <template #body>
        <div
          class="mt-5 h-full flex-1 flex flex-col overflow-y-auto overflow-hidden [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300">
          <div class="p-5 flex items-center font-bold">
            <InformationCircleIcon class="h-6 w-6 text-primary-600" aria-hidden="true"></InformationCircleIcon>
            <div class="ml-2">{{ $t('What is the type of your event?') }}</div>
          </div>
        </div>
      </template>
      <template #footer>
        <div class="p-5 border-t border-gray-200">
          <div class="flex items-center gap-x-2 justify-end">
            <!-- Button -->
            <t-button :color="`primary-solid`" data-hs-overlay="#drawer-right-information">
              {{ $t('OK, got it') }}
            </t-button>
            <!-- End Button -->
          </div>
        </div>
      </template>
    </PrelineDrawer>
  </div>

  <!-- confirmation success -->
  <!-- confirmation -->
  <Confirmation :closeButton="false" :id="'confirm-book'">
    <template #header>
      <p id="hs-pro-dutoo-label" class="text-sm font-semibold text-gray-800 ml-6 ">
          {{ $t('Booking Confirmation') }}
      </p>
    </template>
    <template #body>
      <!-- <div class="text-xs text-gray-800 dark:text-neutral-400"> -->
      <p class="text-sm text-gray-800 dark:text-neutral-400">  {{$t('Thank You')}}! </p>
      <p class="text-sm text-gray-800 dark:text-neutral-400">  {{$t('We have received your')}} {{ typeEvent?.name || typeEventCustom?.name }} {{ $t('event booking') }} {{ __dateFormatHuman(model.startDate) }}.</p>
      <p class="text-sm text-gray-800 dark:text-neutral-400">  {{$t('We will contact you you soon with the quotation on your registered email')}} </p>
      <!-- </div> -->
    </template>
    <template #footer>
      <t-button
          :color="`primary-solid`"
          @click="goToHome()"
          data-hs-overlay="#confirm-bookproject"
        >
          {{ $t('OK') }}
        </t-button>
    </template>
  </Confirmation>
</template>

<script>
  /* eslint-disable vue/html-closing-bracket-spacing */
  import {
    ArrowNarrowRightIcon,
    XIcon,
  } from '@heroicons/vue/solid';
  import {
    ChatAlt2Icon,
    InformationCircleIcon,
  } from '@heroicons/vue/outline';
  import datepicker from "@/components/form/Datepicker.vue";
  import itemPackage from "@/pages/event/EventItems.vue";

  import TSwitch from '@/components/form/Switch.vue';
  import TButton from '@/components/global/Button.vue';
  import TTextarea from '@/components/form/Textarea.vue';
  import Confirmation from "@/components/modal/Confirmation.vue";
  import 'vue-multiselect/dist/vue-multiselect.css';
  import VueMultiselect from 'vue-multiselect';
  import PrelineDrawer from '@/components/form/PrelineDrawer.vue';

  // api
  import packageApi from "@/api/package";
  import projectApi from "@/api/project";
  import packageItemApi from "@/api/packageItem";
  import moment from 'moment';

  export default {
    components: {
      ArrowNarrowRightIcon,
      datepicker,
      ChatAlt2Icon,
      TSwitch,
      TButton,
      XIcon,
      TTextarea,
      Confirmation,
      VueMultiselect,
      InformationCircleIcon,
      PrelineDrawer,
      itemPackage
    },
    props: {},
    data() {
      return {
        studios: [{
            value: 'studio_1',
            name: 'Studio 1',
            descripstion: 'For Large Event 50 pax capacity'
          },
          {
            value: 'studio_2',
            name: 'Studio 2',
            descripstion: 'For Large Event 50 pax capacity'
          },
          {
            value: 'podcast',
            name: 'Podcast Studio',
            descripstion: 'For Large Event 50 pax capacity'
          }
        ],
        date: null,
        isShowSelectType: true,
        isSavingChanges: false,
        typeEvent: null,
        typeEventCustom: null,
        isContinue: false,
        packages: [],
        previousPackages: [],
        packageItems: [],
        isCustomize: false,
        isBooking: false,
        isBookingComplete: false,
        schedules: [],
        isGetSchedule: false,

        // params
        model: {
          name: null,
          description: null,
          packageId: null,
          startDate: null,
          endDate: null,
          duration: 2,
          fileUrl: '',
          notes: '',
          customItems: null,
        },
      };
    },
    computed: {
      currentQuery() {
        return this.$route.query;
      }
    },
    watch: {
    },
    created() {
      this.getAllPackage();
      this.getAllPackagePrevious();
    },
    mounted() {},
    beforeUnmount() {},
    methods: {
      getProjectsSchedule(event) {
        this.isGetSchedule = true;
        const callback = (response) => {
          const data = response.data;
          this.schedules = data;
          this.isGetSchedule = false;
        }
        const errCallback = (err) => {
          console.log(err)
          this.isGetSchedule = false;
        }
        let studioFinal = this.typeEvent?.studio || this.typeEventCustom?.studio
        const params = {
          month: event?.month + 1 || new Date().getMonth() + 1,
          years: event?.year || new Date().getFullYear(),
          studio: studioFinal || ''
        }
        projectApi.getSchedule(params, callback, errCallback)
      },
      updateDatepicker(date) {
        this.model.startDate = this.__dateFormatDateBooking(date)
        this.addingHours(this.model.duration)
      },
      updateDuration(hour) {
        this.model.duration = hour
        this.addingHours(hour)
      },
      addingHours(hour) {
        // Parse the date with moment
        let momentDate = moment(this.model.startDate, "YYYYMM-DD HH:mm");
        // Add 2 hours
        let newDate = momentDate.add(hour, 'hours');
        // Format the new date
        let formattedNewDate = newDate.format("YYYY-MM-DD HH:mm");
        this.model.endDate = formattedNewDate
      },
      submit() {
        this.isBooking = true;
        const callback = (response) => {
          const data = response.data;
          const message = response.message;
          this.isBookingComplete = true;
          this.isBooking = false;

          // Trigger the drawer open using data-hs-overlay
          const drawerTrigger = document.querySelector('[data-hs-overlay="#confirm-book"]');
          if (drawerTrigger) {
              drawerTrigger.click();
          }
        }
        const errCallback = (err) => {
          console.log(err)
          const message = err.response.data.message;
          this.__showNotif('error', 'Error', message || 'Error User');
          this.resetModel();
          this.isBooking = false;
        }
        this.model.name = this.typeEvent.name || this.typeEventCustom.name;
        this.model.description = this.typeEvent.description;
        if (this.typeEvent?.id) {
          this.model.packageId = this.typeEvent?.id
          projectApi.create(this.model, callback, errCallback)
        } 
        if (this.typeEventCustom?.id) {
          this.model.packageId = this.typeEventCustom?.id
          projectApi.create(this.model, callback, errCallback)
        }
      },
      resetModel() {
        this.model = {
          name: null,
          description: null,
          packageId: null,
          startDate: null,
          endDate: null,
          duration: 2,
          fileUrl: '',
          notes: '',
          customItems: null,
        }
      },
      getAllPackage() {
        const callback = (response) => {
          const data = response.data;
          this.packages = data;
        }
        const errCallback = (err) => {
          console.log(err)
        }

        const params = {
          orderBy: 'createdAt',
          sortBy: 'desc',
          page: 1,
          limit: 9999,
        }
        packageApi.getList(params, callback, errCallback)
      },
      getAllPackagePrevious() {
        const callback = (response) => {
          const data = response.data;
          this.previousPackages = data;
        }
        const errCallback = (err) => {
          console.log(err)
        }

        const params = {
          orderBy: 'createdAt',
          sortBy: 'desc',
          page: 1,
          limit: 9999,
        }
        packageApi.getListPrevious(params, callback, errCallback)
      },
      getAllPackageItem() {
        const callback = (response) => {
          const data = response.data;
          this.packageItems = data;
          // Initialize your data here if needed, for example:
          this.packageItems = this.packageItems.map(item => ({
            ...item,
            quantity: item.quantity, // Default quantity
            isEnabled: 1, // Default isEnabled state
          }));
        }
        const errCallback = (err) => {
          console.log(err)
        }

        const params = {
          orderBy: 'createdAt',
          sortBy: 'desc',
          page: 1,
          limit: 9999,
          packageId: this.typeEvent.id || this.typeEventCustom.id
        }
        packageItemApi.getList(params, callback, errCallback)
      },
      customizeEvent() {
        this.isCustomize = true;
      },
      bookingEvent() {
        this.submit()
      },
      continueBooking() {
        this.isShowSelectType = false;
        this.getAllPackageItem();
        this.getProjectsSchedule()
      },
      reInitType() {
        this.isShowSelectType = false;
        this.getAllPackageItem();
        this.getProjectsSchedule();
      },
      selectDateCustomize(payloadCustomize) {
        this.model.customItems = JSON.stringify(payloadCustomize.customItems)
        this.model.fileUrl = payloadCustomize.fileUrl
        this.model.notes = payloadCustomize.notes
        this.isCustomize = false;
      },
      goToHome() {
        window.location.href = `${import.meta.env.VITE_APP_URL}/projects`;
      }
    },
  };
</script>