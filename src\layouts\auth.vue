<template>
	<!-- <div
		v-show="!isChrome"
		class="top-0 px-6 w-full py-2 [h-30px] lg:flex lg:items-center bg-red-600 text-white text-sm justify-center"
	>
		{{ $t('Bannerbite performs better with Chrome based browser') }}
		<a
			href="https://www.google.co.id/chrome/?brand=CHBD&gclid=CjwKCAjwiOv7BRBREiwAXHbv3AWBmeej6mPeNXp8FwCu2rVTeVyKHmOTY8PZwdTt57cJPs2tReOVzBoCC8YQAvD_BwE&gclsrc=aw.ds"
			target="_blank"
			class="underline pointer text-white pl-1"
		>{{ $t('Download here') }}</a>
	</div> -->
	<!-- Meta -->
	<div class="h-screen flex overflow-hidden bg-gray-100">
		<main class="flex-1 relative overflow-y-auto focus:outline-none">
			<div>
				<router-view v-slot="{ Component }">
					<!-- <transition name="slide"> -->
					<component :is="Component" />
				<!-- </transition> -->
				</router-view>
			</div>
		</main>
	</div>
</template>

<script>

export default {
	components: {
	},
	setup() {
	},
	data () {
		return {
			isChrome: false,
		};
	},
	created () {
		this.browserName();
	},
	methods: {
		browserName(agent) {        
			let userAgent = navigator.userAgent;
			let browserName;
			if (userAgent.match(/chrome|chromium|crios/i)) {
				browserName = "chrome";
			} else if (userAgent.match(/firefox|fxios/i)) {
				browserName = "firefox";
			}  else if (userAgent.match(/safari/i)) {
				browserName = "safari";
			} else if (userAgent.match(/opr\//i)) {
				browserName = "opera";
			} else if (userAgent.match(/edg/i)) {
				browserName = "edge";
			} else {
				browserName="No browser detection";
			}
			if (browserName != 'chrome') { 
				this.isChrome = false;
			} else {
				this.isChrome = true;
			}
		},
	},
};
</script>