<template>
   <svg
      class="cursor-pointer hover:scale-110"
      width="15"
      height="15"
      xmlns="http://www.w3.org/2000/svg"
   >
      <g fill="#828FA3" fill-rule="evenodd">
         <path d="m12.728 0 2.122 2.122L2.122 14.85 0 12.728z" />
         <path d="M0 2.122 2.122 0 14.85 12.728l-2.122 2.122z" />
      </g>
   </svg>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
   import store from "../../pages/kanban/store.js"
</script>

<style scoped></style>
