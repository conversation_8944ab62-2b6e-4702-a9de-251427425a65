<template>
	<div
		v-if="preview"
		id="app"
		class="w-full items-center justify-center text-center w-full"
	>
		<div 
			class="mt-1 border-2 border-gray-300 border-dashed rounded-md px-6 pt-5 pb-6 flex justify-center"
			@dragover="dragover"
			@dragleave="dragleave"
			@drop="drop"
		>
			<div class="space-y-1 text-center">
				<img
					v-show="filelist.length > 0 && type !== 'file'"
					id="previewUpload"
					class="object-cover max-h-[100px] max-w-[200px]"
					src="#"
					alt="Planlagt"
				>
				<svg
					v-show="filelist.length === 0 && type === 'image'"
					class="mx-auto h-12 w-12 text-gray-400"
					stroke="currentColor"
					fill="none"
					viewBox="0 0 48 48"
					aria-hidden="true"
				>
					<path
						d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
						stroke-width="2"
						stroke-linecap="round"
						stroke-linejoin="round"
					/>
				</svg>
				<div class="flex text-sm text-gray-600">
					<label
						for="fileUploadInput"
						class="relative cursor-pointer rounded-md font-medium text-primary-600 hover:text-primary-500 focus-within:outline-none"
					>
						<span
							v-if="filelist.length === 0"
							for="fileUploadInput"
						>{{ $t('Upload a file') }}</span>
						<input 
							id="fileUploadInput"
							ref="file"
							:accept="acceptedFiles"
							:multiple="multiple"
							name="fileUploadInput" 
							type="file"
							class="sr-only"
							@change="onChange"
						>
					</label>
					<p
						v-if="filelist.length === 0"
						class="pl-1"
					>
						{{ $t('or drag and drop') }}
					</p>
					<!-- display name -->
					<ul
						v-if="filelist.length && multiple && type !== 'excel'"
						v-cloak
						class="mt-4"
					>
						<li
							v-for="file, index in filelist"
							:key="index"
							class="text-sm p-1"
						>
							{{ file.name }}
							<button
								class="ml-2 text-red-400"
								type="button"
								title="Remove file"
								@click="remove(filelist.indexOf(file))"
							>
								{{ $t('remove') }}
							</button>
						</li>
					</ul>
					<div
						v-if="filelist.length && !multiple && type !== 'excel'"
						:key="index"
						class="text-sm p-1"
					>
						{{ filelist[0].name }}
						<button
							class="ml-2 text-red-400"
							type="button"
							title="Remove file"
							@click="remove(filelist.indexOf(file))"
						>
							{{ $t('remove') }}
						</button>
					</div>
				</div>
				<p class="text-xs text-gray-500">
				<!-- PNG, JPG, GIF up to 10MB -->
				</p>
			</div>
		</div>
	</div>
	<div v-else>
		<label
			for="fileUploadInput"
			class="h-[36px] w-40 bg-[#EEEEEE] p-2 px-6 relative cursor-pointer rounded-md font-medium focus-within:outline-none"
		>
			<span
				v-if="filelist.length === 0 && type !== 'excel'"
				for="fileUploadInput"
			>{{ title }}</span>
			<input 
				id="fileUploadInput"
				ref="file"
				:accept="acceptedFiles"
				:multiple="multiple"
				name="fileUploadInput" 
				type="file"
				class="sr-only"
				@change="onChange"
			>
		</label>
	</div>
</template>

<script>
import TButton from '@/components/global/Button.vue';

export default {
	name: 'DropArea',
	components: {
	},
	props: {
		role: {
			type: String,
			default: 'admin',
		},
		multiple: {
			type: Boolean,
			default: true,
		},
		maxSize: {
			type: Number,
			default: 100,
		},
		type: {
			type: String,
			default: 'image',
		},
		preview: {
			type: Boolean,
			default: false,
		},
		title: {
			type: String,
			default: 'Upload',
		},
	},
	data() {
		return {
			isDraggedOver: false,
			filelist: [],
			fileTypes: {
				document: ['application/pdf', 'application/msword', 'application/vnd.ms-excel'],
				video: ['video/mp4, video/quicktime'],
				image: ['image/png', 'image/jpg', 'image/jpeg'],
				excel: ['.xlsx, .xls']
			},
		};
	},
	computed: {
		acceptedFiles() {
			return this.fileTypes[this.type] ? this.fileTypes[this.type].join(',') : '';
		},
	},
	methods: {
		onChange() {
			this.filelist = [...this.$refs.file.files];
			if (this.filelist[0] && this.filelist[0].type.includes('image')) {
				previewUpload.src = URL.createObjectURL(this.filelist[0]);
				this.$emit('upload', this.filelist);
			} else {
				this.$emit('upload', this.filelist);
			}
		},
		remove(i) {
			this.filelist.splice(i, 1);
			this.$emit('remove');
		},
		dragover(event) {
			event.preventDefault();
			// Add some visual fluff to show the user can drop its files
			if (!event.currentTarget.classList.contains('bg-primary-300')) {
				event.currentTarget.classList.remove('bg-gray-100');
				event.currentTarget.classList.add('bg-primary-300');
			}
		},
		dragleave(event) {
		// Clean up
			event.currentTarget.classList.add('bg-gray-100');
			event.currentTarget.classList.remove('bg-primary-300');
		},
		drop(event) {
			event.preventDefault();
			this.$refs.file.files = event.dataTransfer.files;
			this.onChange(); // Trigger the onChange event manually
			// Clean up
			event.currentTarget.classList.add('bg-gray-100');
			event.currentTarget.classList.remove('bg-primary-300');
		}
	},
};
</script>

