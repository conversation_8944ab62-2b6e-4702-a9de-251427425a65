<template>
  <article class="w-[17.5rem] rounded-lg bg-white dark:bg-kb_dark_grey dark:text-white shadow-card cursor-pointer">
    <div class="dark:bg-kb_dark_grey rounded-md p-2 shadow-sm my-2 relative border-gray-100 border-[1px]" :class="{'bg-[#F7FCEB]': isCompleted, 'bg-[#FFF4F4]': isDueDateReached}">
      <div class="flex items-center pt-2">
        <CheckCircleIcon @click.stop="updateComplete(task)" class="h-4 w-4 ml-2 pointer text-gray-400 mr-2"
          :class="{'text-green-600': task.status === 'completed'}" aria-hidden="true"></CheckCircleIcon>
        <div class="text-xs font-medium">{{ task.name }}</div>
      </div>
      <!-- assignee / due date -->
      <div class="pl-2 mt-6 flex items-center">
        <AssigneeDirect @click.stop="" @select="assigneeSelected" @remove="assigneeRemoved" :key="task.assign?.id" class="w-full"
          :currentAssignee="task.assign" :users="users"></AssigneeDirect>
        <div class="absolute left-[50px]">
          <DatePickerTask class="font-medium" @select="datePickerSelect" :key="task.dueDate" :currentDate="task.dueDate">
          </DatePickerTask>
        </div>
        <div v-if="task && task.meta && task.meta.totalComments" class="absolute right-[8px] flex items-center mr-1">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-4">
            <path stroke-linecap="round" stroke-linejoin="round" d="M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.************.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z" />
          </svg>
          <div class="text-xs ml-1" >{{ task.meta?.totalComments }}</div>
        </div>
      </div>
    </div>
  </article>
</template>

<script>
  import {
    mapGetters,
    mapActions
  } from 'vuex';
  import {
    CheckCircleIcon,
  } from '@heroicons/vue/solid';
  import {
    CalendarIcon
  } from '@heroicons/vue/outline';
  import {
    ref,
    toRaw,
    onMounted
  } from 'vue';
  import {
    onKeyStroke
  } from '@vueuse/core';
  import kbInput from "@/components/kanban/kbInput.vue";
  import TButton from '@/components/global/Button.vue';
  import AssigneeDirect from "@/components/kanban/AssigneeDirect.vue";
  import DatePickerTask from "@/components/kanban/DatePickerTask.vue";
  import tasksApi from "@/api/tasks";
  import startOfDay from 'date-fns/startOfDay';



  export default {
    name: 'YourComponentName',
    components: {
      kbInput,
      CheckCircleIcon,
      CalendarIcon,
      TButton,
      AssigneeDirect,
      DatePickerTask
    },
    props: {
      task: {
        type: Object,
        default: null,
      },
      index: {
        type: Number,
        default: 0,
      },
      users: {},
    },
    data() {
      return {
        ph: 'Task name',
        dueDate: '',
        assignTo: null,
        name: '',
      };
    },
    created() {
      
    },
    computed: {
      ...mapGetters({
        getOnlineUsers: 'application/getOnlineUsers',
        getActiveProject: 'application/getActiveProject',
        getActiveColumn: 'application/getActiveColumn',
        getActiveTask: 'application/getActiveTask',
        getBoardColsLength: 'application/getBoardColsLength',
      }),
      project() {
        return this.getActiveProject;
      },
      isCompleted() {
        return this.task?.status === 'completed'
      },
      isDueDateReached() {
        if (this.task.dueDate) {
          const today = startOfDay(new Date()); // Normalize today's date to start of day
          const dueDate = startOfDay(new Date(this.task.dueDate)); // Normalize dueDate to start of day
          return dueDate < today; // Check if the due date is today or in the past
        }
        return false; // Return false if there's no dueDate
      }
    },
    methods: {
      ...mapActions({
        clearOnlineUsers: 'application/clearOnlineUsers',
        setData: 'application/setData',
        changeStatus: 'application/changeStatus',
        updateTaskInStore: 'application/updateTaskInStore',
        deleteTaskInStore: 'application/deleteTaskInStore',

      }),
      datePickerSelect(date) {
        this.dueDate = date ? this.__dateFormatISO(date) : '';
        if (this.task?.id) {
          this.updateTask(); // need to optimze
        }
      },
      assigneeSelected(event) {
        this.assignTo = event?.id || this.task?.assign?.id
        if (this.task?.id) {
          this.assignTask();
        }
      },
      assignTask() {
        // Define the callback function that will be called after the API request is successful
        const callback = (response) => {
          const data = response.data;
          // Assuming 'data' contains the updated task details
          let finalData = {
            ...this.task, // Fetch the existing task data from the store
            ...data // Merge with the new data from the API response
          };
          this.updateTaskInStore(finalData);
        };

        // Define the error callback function to handle any errors during the API request
        const errCallback = (err) => {
          const message = err?.response?.data?.message || 'Error occurred while updating the task';
          this.__showNotif('error', 'Error', message);
          // this.handleErrors(err.response.data);
        };

        const params = {
          assignTo: this.assignTo,
        }
        // Make the API request to update the task
        tasksApi.updateTask(this.task?.id, params, callback, errCallback);
      },
      assigneeRemoved() {
        this.assignTo = null
        if (this.task?.id) {
          this.assignTask()
        }
      },
      updateDatepicker(date) {
        this.dueDate = date ? this.__dateFormatISO(date) : '';
      },
      close() {},
      updateTask() {
        const callback = (response) => {
          const data = response.data;
          const finalData = {
            ...this.getActiveTask, // Existing task data
            ...data, // New data from the server
          };

          this.updateTaskInStore(finalData);
        }
        const errCallback = (err) => {
          const message = err.response.data.message;
          this.__showNotif('error', 'Error', message || 'Error User');
          // this.handleErrors(err.response.data)
        }
        let params = {
          name: this.name,
          dueDate: this.dueDate ? this.__dateFormatISO(this.dueDate) : '',
          assignTo: this.assignTo || this.task?.assign?.id,
          projectId: this.project?.id,
          type: this.$store.state.application.task.type
        }
        tasksApi.update(this.task?.id, params, callback, errCallback)
      },
      updateComplete(task) {
        this.$store.state.application.task.addDirectly = false;

        // Update the task properties in the store
        this.$store.state.application.task = task;
        this.$store.state.application.task.active = task.id;
        this.$store.state.application.task.status = task.status;
        this.$store.state.application.task.type = task.type;

        const callback = (response) => {
          const data = response.data;

          // Merge the updated data with the existing task data
          const finalData = {
            ...this.getActiveTask, // Existing task data
            ...data, // New data from the server
          };
          this.updateTaskInStore(finalData);
        };
        let params = {
          status: !task.status || task.status === 'incomplete' ? 'complete' : 'incomplete'
        }

        // Make the API call to update the task on the server
        tasksApi.compeleteTask(task.id, params, callback, (error) => {
          console.error('Error updating task:', error);
        });
      },
    },
    mounted() {
      onKeyStroke('Escape', this.close);
    }
  };
</script>