<template>
  <div class="relative">
    <div class="flex justify-center mb-4">
      <div class="md:w-[480px] flex">
        <label class="text-left font-medium mr-2">{{ $t('Items are included in your event') }}</label>
        <InformationCircleIcon class="h-6 w-6 text-primary-600" aria-hidden="true"></InformationCircleIcon>
      </div>
    </div>
    <div class="">
      <div class="flex justify-center">
        <!-- items and switch -->
        <div class="md:w-[480px] min-h-[90px] h-[68vh] shadow rounded-md w-full border border-gray-300">
          <div class="p-4 mb-5 h-[calc(68vh-108px)] overflow-y-auto">
            <div>
              <div class="flex justify-between pb-4" v-for="(item, index) in packageItems" :key="item.id">
                <div>
                  <div class="font-bold">{{ item.name }}</div>
                  <div class="font-light text-xs">{{ item.description }}</div>
                </div>
                <t-switch v-model="packageItems[index].isEnabled" :value="packageItems[index].isEnabled" />
              </div>
            </div>
            <!-- <button @click="sendPayload">Submit</button> -->
          </div>
          <!-- bottom action notes -->
          <div>
            <div class="border border-gray-300 w-full"></div>
            <div class="flex justify-between items-center mt-3">
              <div class="p-4">
                <t-button :color="`primary-white`" :isLoading="isSavingChanges"
                  @click="isShowNoteDrawer = !isShowNoteDrawer">
                  <div class="flex items-center px-4 py-2">
                    <DocumentAddIcon class="h-4 w-4 mr-2" aria-hidden="true"></DocumentAddIcon>
                    {{ $t('Add Note') }}
                  </div>
                </t-button>
              </div>
              <!-- notes editor -->
              <div v-if="isShowNoteDrawer" class="flex justify-center z-[9999] absolute bottom-0 ml-[-1px]">
                <div class="md:w-[480px] border border-gray-300 shadow bg-white w-full">
                  <div>
                    <div class="flex justify-between p-4 ">
                      <div class="text-sm">{{ $t('Add Notes') }}</div>
                      <XIcon @click="isShowNoteDrawer = !isShowNoteDrawer" class="h-4 w-4" aria-hidden="true"></XIcon>
                    </div>
                    <div class="border border-gray-200 w-full"></div>
                    <div class="px-4 py-2">
                      <t-textarea v-model="notes" :rows="6" :value="notes"
                        :placeholder="$t('Type in short description about this user')"></t-textarea>
                    </div>
                    <div class="flex justify-between items-center px-4 pb-4 pt-0">
                      <!-- <div class="flex item-center">
                        <div><PaperClipIcon @click="isShowNoteDrawer = !isShowNoteDrawer" class="h-4 w-4" aria-hidden="true"></PaperClipIcon></div>
                        <div class="text-sm">{{ $t('Attach a File') }}</div>
                      </div> -->
                      <div class="flex justify-center">
                        <label for="fileUploadInput" class="relative cursor-pointer ">
                          <div for="fileUploadInput" class="flex item-center">
                            <div>
                              <PaperClipIcon @click="isShowNoteDrawer = !isShowNoteDrawer" class="h-4 w-4"
                                aria-hidden="true"></PaperClipIcon>
                            </div>
                            <div v-if="!fileName" class="text-sm">{{ $t('Attach a File') }}</div>
                            <div v-else class="text-sm truncate w-[250px]">{{ fileName }}</div>
                          </div>
                          <input id="fileUploadInput" ref="file" accept="image/*" name="fileUploadInput" type="file"
                            class="sr-only" @change="upload">
                        </label>
                      </div>
                      <t-button :type="'submit'" :color="`primary-solid`" :isLoading="isSavingChanges"
                        @click="sendPayload(); isShowNoteDrawer = false;">
                        {{ $t('Save Changes') }}
                      </t-button>
                    </div>
                  </div>
                </div>
              </div>
              <div class="p-4">
                <t-button :type="'submit'" :color="`primary-solid`" :isLoading="isSavingChanges" @click="sendPayload()">
                  {{ $t('Save Changes') }}
                </t-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  /* eslint-disable vue/html-closing-bracket-spacing */
  import {
    XIcon,
  } from '@heroicons/vue/solid';
  import {
    InformationCircleIcon,
    DocumentAddIcon,
    PaperClipIcon
  } from '@heroicons/vue/outline';
  import TSwitch from '@/components/form/Switch.vue';
  import TButton from '@/components/global/Button.vue';
  import TTextarea from '@/components/form/Textarea.vue';
  import packageItemApi from "@/api/packageItem";
  import fileApi from '@/api/files';

  export default {
    components: {
      TSwitch,
      TButton,
      DocumentAddIcon,
      XIcon,
      TTextarea,
      PaperClipIcon,
    },
    props: {
      packageItems: {
        type: Array,
        default: [],
      },
    },
    data() {
      return {
        isUploadingfile: false,
        isShowNoteDrawer: false,
        notes: '',
        fileName: '',
        fileUrl: null,
        packageItemsParams: null,
      };
    },
    computed: {},
    watch: {},
    created() {},
    mounted() {},
    beforeUnmount() {},
    methods: {
      sendPayload() {
        const payload = this.packageItems.map(item => ({
          id: item.id,
          quantity: item.quantity,
          isEnabled: item.isEnabled ? 1 : 0, // Convert boolean to 0 or 1
        }));
        this.packageItemsParams = payload
        this.selectDateCustomize();
        // Send payload to server or use it as needed
      },
      selectDateCustomize() {
        let payloadCustomize = {
          customItems: this.packageItemsParams,
          notes: this.notes,
          fileUrl: this.fileUrl
        }
        this.$emit('selectDateCustomize', payloadCustomize)
      },
      upload(e) {
        const files = e.target.files;
        if (files.length > 0) {
          if (files[0]) {
            this.isUploadingFile = true;
            const file = files[0];
            if (!file) {
              this.__showNotif('warning', 'Upload File', this.$t('Sorry, currently we can\'t upload the file'));
              return;
            }
            const params = new FormData();
            params.append('file', file);
            const callback = (response) => {
              const file = response.data;
              this.fileUrl = file;
              this.fileName = files[0].name;
              this.isUploadingfile = false;
            };
            const errorCallback = () => {
              this.isUploadingFile = false;
              this.__showNotif('warning', 'Upload File', this.$t('Sorry, currently we can\'t upload the file'));
            };
            fileApi.upload(params, callback, errorCallback);
          } else {
            this.__showNotif('warning', 'Upload File', this.$t('Unsupported File'));
            // eslint-disable-next-line
            return;
          }
        }
      },
    },
  };
</script>