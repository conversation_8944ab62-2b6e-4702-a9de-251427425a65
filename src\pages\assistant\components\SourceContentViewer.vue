<template>
  <div class="h-full bg-white flex flex-col overflow-hidden">
    <!-- Header -->
    <div class="p-4 border-b border-gray-200 flex-shrink-0">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-medium text-gray-900">{{ citation?.source_title || 'Source Document' }}</h3>
        <button 
          @click="$emit('close')"
          class="p-1 text-gray-400 hover:text-gray-600 transition-colors"
        >
          <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Source Guide Accordion -->
    <div v-if="sourceSummary" class="border-b border-gray-200 flex-shrink-0">
      <div class="accordion">
        <div class="accordion-item border-0">
          <button 
            @click="toggleAccordion"
            class="accordion-trigger w-full px-4 py-3 text-sm font-medium hover:no-underline hover:bg-blue-50 flex items-center justify-between"
            :style="{ color: '#234776' }"
          >
            <div class="flex items-center space-x-2">
              <svg xmlns="http://www.w3.org/2000/svg" height="16px" viewBox="0 -960 960 960" width="16px" fill="#234776">
                <path d="M166.67-120.67 120-167.33l317.67-318L254-531l194-121-16.33-228 175 147L818-818.33l-85.67 211.66L880-432l-228.67-16.67-120.66 194L485-438.33 166.67-120.67Zm24.66-536L120-728l72-72 71.33 71.33-72 72Zm366.34 233 58-94.33 111 8.33-72-85 41.66-102.66-102.66 41.66-85-71.66L517-616.67l-94.33 59 108 26.67 27 107.33Zm171 303.67-71.34-72 71.34-71.33 71.33 72L728.67-120ZM575-576Z"/>
              </svg>
              <span>Source guide</span>
            </div>
            <svg 
              class="h-4 w-4 transition-transform duration-200"
              :class="{ 'rotate-180': isAccordionOpen }"
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>
          <div 
            v-show="isAccordionOpen"
            class="accordion-content px-4 pb-4"
          >
            <div class="text-sm text-gray-700 space-y-4">
              <div>
                <h4 class="font-medium mb-2">Summary</h4>
                <p class="leading-relaxed">{{ sourceSummary }}</p>
              </div>
              
              <div v-if="sourceScenario">
                <h4 class="font-medium mb-2">Scenario</h4>
                <p class="leading-relaxed">{{ sourceScenario }}</p>
              </div>
              
              <div v-if="sourceDescription">
                <h4 class="font-medium mb-2">Description</h4>
                <p class="leading-relaxed">{{ sourceDescription }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Source Content -->
    <div class="flex-1 overflow-hidden">
      <div class="h-full overflow-y-auto p-4" ref="contentContainer">
        <div v-if="isLoading" class="flex items-center justify-center h-32">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
        
        <div v-else-if="sourceContent" class="space-y-1" ref="highlightedContent">
          <div 
            v-for="(line, index) in contentLines" 
            :key="index"
            :class="[
              'py-2 px-3 rounded leading-relaxed',
              isLineHighlighted(index + 1) ? 'border-l-4 font-medium' : 'hover:bg-gray-50'
            ]"
            :style="isLineHighlighted(index + 1) ? {
              backgroundColor: '#eadef9',
              borderLeftColor: '#9333ea'
            } : {}"
          >
            <span :class="isLineHighlighted(index + 1) ? 'font-medium' : ''">{{ line }}</span>
          </div>
        </div>
        
        <div v-else class="text-center text-gray-500 py-8">
          <svg class="h-12 w-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <p>No content available</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SourceContentViewer',
  props: {
    citation: {
      type: Object,
      default: null
    },
    sourceContent: {
      type: String,
      default: ''
    },
    sourceSummary: {
      type: String,
      default: ''
    },
    sourceScenario: {
      type: String,
      default: ''
    },
    sourceDescription: {
      type: String,
      default: ''
    },
    isOpenedFromSourceList: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isLoading: false,
      isAccordionOpen: false
    };
  },
  computed: {
    contentLines() {
      return this.sourceContent ? this.sourceContent.split('\n') : [];
    },
    
    hasValidCitationLines() {
      return this.citation && 
        typeof this.citation.chunk_lines_from === 'number' && 
        typeof this.citation.chunk_lines_to === 'number' &&
        this.citation.chunk_lines_from > 0;
    }
  },
  watch: {
    citation: {
      handler(newCitation) {
        console.log('SourceContentViewer: Citation changed', {
          citationId: newCitation?.citation_id,
          sourceId: newCitation?.source_id,
          sourceTitle: newCitation?.source_title,
          chunkLinesFrom: newCitation?.chunk_lines_from,
          chunkLinesTo: newCitation?.chunk_lines_to,
          hasValidCitationLines: this.hasValidCitationLines,
          isOpenedFromSourceList: this.isOpenedFromSourceList
        });

        if (this.isOpenedFromSourceList) {
          this.isAccordionOpen = true;
        } else if (this.hasValidCitationLines) {
          this.isAccordionOpen = false;
          this.$nextTick(() => {
            console.log('SourceContentViewer: Scrolling to highlighted lines');
            this.scrollToHighlightedLines();
          });
        }
      },
      immediate: true
    }
  },
  methods: {
    toggleAccordion() {
      this.isAccordionOpen = !this.isAccordionOpen;
    },
    
    isLineHighlighted(lineNumber) {
      if (!this.hasValidCitationLines) return false;
      
      return lineNumber >= this.citation.chunk_lines_from && 
             lineNumber <= this.citation.chunk_lines_to;
    },
    
    scrollToHighlightedLines() {
      if (!this.hasValidCitationLines) return;
      
      const container = this.$refs.contentContainer;
      const highlightedContent = this.$refs.highlightedContent;
      
      if (container && highlightedContent) {
        // Find the first highlighted line
        const firstHighlightedLine = highlightedContent.children[this.citation.chunk_lines_from - 1];
        
        if (firstHighlightedLine) {
          // Calculate scroll position to center the highlighted content
          const containerHeight = container.clientHeight;
          const lineTop = firstHighlightedLine.offsetTop;
          const scrollTop = Math.max(0, lineTop - containerHeight / 2);
          
          container.scrollTo({
            top: scrollTop,
            behavior: 'smooth'
          });
        }
      }
    }
  }
};
</script>

<style scoped>
.accordion-trigger {
  text-align: left;
  border: none;
  background: none;
  outline: none;
}

.accordion-trigger:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.accordion-content {
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.rotate-180 {
  transform: rotate(180deg);
}
</style>
