import { client } from '@/libraries/http-client';
import { buildQuery } from '@/libraries/helper';

const endpoint = '/v1/social';

export default {
	// Get List
	getFromUrl(url, cb, errorCb) {
		const responseHandler = (response) => {
			if (cb) cb(response.data);
		};
		const errorHandler = (e) => {
			if (errorCb) errorCb(e);
		};
		client.get(url)
			.then(responseHandler)
			.catch(errorHandler);
	},

};
