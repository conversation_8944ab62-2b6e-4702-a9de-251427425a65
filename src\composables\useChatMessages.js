import { ref, computed } from 'vue'
import { db, supabase } from '@/integrations/supabase/client.js'

export function useChatMessages(notebookId) {
  const messages = ref([])
  const isLoading = ref(false)
  const isSending = ref(false)
  const isDeletingChatHistory = ref(false)

  // Load messages from Supabase
  const loadMessages = async () => {
    if (!notebookId) return
    
    isLoading.value = true
    try {
      const { data, error } = await db.getChatMessages(notebookId)
      if (error) throw error
      
      // Transform Supabase data to match expected format
      messages.value = data.map(msg => ({
        id: msg.id,
        role: msg.role,
        content: msg.content,
        timestamp: msg.created_at,
        citations: msg.citations || []
      }))
    } catch (error) {
      console.error('Error loading chat messages:', error)
    } finally {
      isLoading.value = false
    }
  }

  // Send a message
  const sendMessage = async (messageText) => {
    if (!notebookId || !messageText.trim()) return
    
    isSending.value = true
    
    try {
      // Create user message
      const userMessage = {
        notebook_id: notebookId,
        role: 'user',
        content: messageText.trim(),
        created_at: new Date().toISOString()
      }
      
      const { data: savedUserMessage, error: userError } = await db.createChatMessage(userMessage)
      if (userError) throw userError
      
      // Add to local messages
      messages.value.push({
        id: savedUserMessage.id,
        role: 'user',
        content: messageText.trim(),
        timestamp: savedUserMessage.created_at
      })
      
      // Call Supabase Edge Function to generate AI response
      const { data: aiResponse, error: aiError } = await supabase.functions.invoke('chat-with-sources', {
        body: {
          notebookId,
          message: messageText.trim(),
          messageHistory: messages.value.slice(-10) // Send last 10 messages for context
        }
      })
      
      if (aiError) {
        console.error('AI Response Error:', aiError)
        // Fallback to dummy response for development
        const dummyResponse = createDummyResponse(messageText)
        const assistantMessage = {
          notebook_id: notebookId,
          role: 'assistant',
          content: dummyResponse.content,
          citations: dummyResponse.citations,
          created_at: new Date().toISOString()
        }
        
        const { data: savedAssistantMessage, error: assistantError } = await db.createChatMessage(assistantMessage)
        if (assistantError) throw assistantError
        
        messages.value.push({
          id: savedAssistantMessage.id,
          role: 'assistant',
          content: dummyResponse.content,
          timestamp: savedAssistantMessage.created_at,
          citations: dummyResponse.citations
        })
      } else {
        // Save AI response to database
        const assistantMessage = {
          notebook_id: notebookId,
          role: 'assistant',
          content: aiResponse.content,
          citations: aiResponse.citations || [],
          created_at: new Date().toISOString()
        }
        
        const { data: savedAssistantMessage, error: assistantError } = await db.createChatMessage(assistantMessage)
        if (assistantError) throw assistantError
        
        messages.value.push({
          id: savedAssistantMessage.id,
          role: 'assistant',
          content: aiResponse.content,
          timestamp: savedAssistantMessage.created_at,
          citations: aiResponse.citations || []
        })
      }
      
    } catch (error) {
      console.error('Error sending message:', error)
      throw error
    } finally {
      isSending.value = false
    }
  }

  // Delete chat history
  const deleteChatHistory = async () => {
    if (!notebookId) return
    
    isDeletingChatHistory.value = true
    try {
      const { error } = await db.deleteChatMessages(notebookId)
      if (error) throw error
      
      messages.value = []
    } catch (error) {
      console.error('Error deleting chat history:', error)
      throw error
    } finally {
      isDeletingChatHistory.value = false
    }
  }

  // Create dummy response for development/fallback
  const createDummyResponse = (userMessage) => {
    return {
      content: {
        segments: [
          { text: `I understand you're asking about "${userMessage}". Based on your uploaded sources, here's what I found:\n\n` },
          {
            text: "This is a simulated response that would normally be generated by analyzing your documents.",
            citation_id: 1
          },
          { text: " The AI would provide insights, summaries, and answers based on the content you've uploaded." },
          {
            text: " Here's another piece of information from a different source.",
            citation_id: 2
          }
        ],
        citations: [
          {
            citation_id: 1,
            source_id: 'source-1',
            source_title: 'Research Document.pdf',
            source_type: 'pdf',
            chunk_lines_from: 15,
            chunk_lines_to: 25,
            chunk_index: 1,
            excerpt: 'This is a sample citation from your uploaded document that supports the response.'
          },
          {
            citation_id: 2,
            source_id: 'source-2',
            source_title: 'Technical Guidelines.docx',
            source_type: 'docx',
            chunk_lines_from: 42,
            chunk_lines_to: 48,
            chunk_index: 2,
            excerpt: 'Additional supporting information from another document.'
          }
        ]
      },
      citations: [
        {
          citation_id: 1,
          source_id: 'source-1',
          source_title: 'Research Document.pdf',
          source_type: 'pdf',
          chunk_lines_from: 15,
          chunk_lines_to: 25,
          chunk_index: 1,
          excerpt: 'This is a sample citation from your uploaded document that supports the response.'
        },
        {
          citation_id: 2,
          source_id: 'source-2',
          source_title: 'Technical Guidelines.docx',
          source_type: 'docx',
          chunk_lines_from: 42,
          chunk_lines_to: 48,
          chunk_index: 2,
          excerpt: 'Additional supporting information from another document.'
        }
      ]
    }
  }

  return {
    messages: computed(() => messages.value),
    isLoading: computed(() => isLoading.value),
    isSending: computed(() => isSending.value),
    isDeletingChatHistory: computed(() => isDeletingChatHistory.value),
    loadMessages,
    sendMessage,
    deleteChatHistory
  }
}
