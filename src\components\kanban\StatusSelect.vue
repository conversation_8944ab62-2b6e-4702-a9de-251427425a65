<template>
  <div ref="statusWrapper" class="text-sm relative bg-white flex flex-row items-center cursor-pointer">
    <!-- Active board name -->
    <p class="py-2 ml-4 w-full body_l text-kb_black dark:text-white" @click="toggleMenu">
      {{ selItem }}
    </p>
    <!-- chevron down -->
    <svg v-if="!showMenu" @click="toggleMenu" class="mr-3" width="24" height="7" xmlns="http://www.w3.org/2000/svg">
      <path stroke="gray" stroke-width="2" fill="none" d="m1 1 4 4 4-4" />
    </svg>
    <!-- chevron up -->
    <svg v-else @click="toggleMenu" class="mr-3" width="24" height="7" xmlns="http://www.w3.org/2000/svg">
      <path stroke="gray" stroke-width="2" fill="none" d="M9 6 5 2 1 6" />
    </svg>
    <!-- show menu -->
    <div ref="menu" v-if="showMenu"
      class="absolute right-2 top-7 min-w-[150px] z-10 w-full bg-white dark:bg-kb_very_dark_grey rounded-lg shadow-board_menu">
      <a @click="clicked(item)"
        class="block ml-4 first:mt-4 last:mb-4 mt-2 body_l  hover:text-kb_dark_grey dark:hover:text-kb_light_grey"
        href="#" v-for="item in selectItems" :key="item.index">
        {{ item.title }}
      </a>
    </div>
  </div>
</template>

<script>
import { onClickOutside } from '@vueuse/core';
import { delay } from '@/libraries/helper';
import {
  mapGetters,
  mapActions
} from 'vuex';
import store from "../../pages/kanban/store.js";
import tasksApi from "@/api/tasks";
import { toRaw  } from 'vue';

export default {
  name: 'YourComponentName',
  props: {
    _mode: {
      type: String,
      required: true
    }
  },
  computed: {
    ...mapGetters({
      getOnlineUsers: 'application/getOnlineUsers',
      getActiveProject: 'application/getActiveProject',
      getActiveBoard: 'application/getActiveBoard',
      getActiveColumn: 'application/getActiveColumn',
      getActiveTask: 'application/getActiveTask',
      getBoardColsLength: 'application/getBoardColsLength',
    }),
    cols() {
      return this.getActiveBoard?.columns;
    },
  },
  emits: ['change'],
  data() {
    return {
      showMenu: false,
      selectItems: [],
      selItem: '',
      workData: null,
      isShow: true,
    };
  },
  mounted() {
    this.initializeData();
    this.workData = JSON.parse(JSON.stringify(toRaw(this.getActiveTask)));

    // We are now targeting the wrapper div around the textarea
    const statusWrapperElement = this.$refs.statusWrapper;

    // onClickOutside from VueUse to detect outside clicks
    onClickOutside(statusWrapperElement, () => {
      this.showMenu = false; // Close textarea on outside click
    });
  },
  beforeUnmount() {
    // Clean up any listeners before unmounting the component
    document.removeEventListener('click', this.handleClickOutside);
  },
  created() {
    this.$soketio.on('kanban_update', (kanbanData) => {
      this.fetchKanban();
    });
    // need add additionalInfoAddingColumn
    this.$soketio.on('kanban_add', (data) => {
      this.fetchKanban();
    });
    this.$soketio.on('kanban_delete', (data) => {
      this.fetchKanban();
    });
    // this.$soketio.on('task_reorder', (reorderData) => {
    //   setTimeout(() => {
    //     this.initializeData();
    //     this.workData = JSON.parse(JSON.stringify(toRaw(this.getActiveTask)));
    //   }, 200);
    // });
  },
  watch: {
    '$route.params.slug': function(newRoute) {
      this.initializeData()
    }
  },
  methods: {
  ...mapActions({
    clearOnlineUsers: 'application/clearOnlineUsers',
    setData: 'application/setData',
    changeStatus: 'application/changeStatus',
    updateTaskInStore: 'application/updateTaskInStore',
  }),
    // Function to close the textarea when clicked outside
    handleClickOutside(event) {
      const textareaElement = this.$refs.statusWrapper?.$el || this.$refs.statusWrapper;
      if (textareaElement && !textareaElement.contains(event.target)) {
        this.showMenu = false;
        // Remove the click listener after hiding the textarea
        document.removeEventListener('click', this.handleClickOutside);
      }
    },
  fetchKanban() {
    this.selectItems = []
    let board = this.getActiveBoard;
    board.columns.forEach((element, index) => {
      this.selectItems.push({
        title: element.title,
        index
      });
    });
    if (!this.getActiveColumn) {
      this.selItem = this.$store.state.application.task.type;
    }
  },
  initializeData() {
    this.selectItems = []
    let board = this.getActiveBoard;
    board.columns.forEach((element, index) => {
      this.selectItems.push({
        title: element.title,
        index
      });
    });
    this.selItem = this.$store.state.application.task.type;
  },
  toggleMenu() {
    this.showMenu = !this.showMenu;
    document.addEventListener('click', this.handleClickOutside);

  },
  clicked(item) {
    // Store the previous column before updating
    if (item.title !== this.selItem) {
      // Update the selected item
      this.selItem = item.title;
      this.workData.type = this.selItem;
      this.workData.dueDate = this.workData.dueDate ? this.__dateFormatISO(this.workData.dueDate) : '';

      // Close the menu
      this.showMenu = false;

      // Update the task and handle column changes
      this.$nextTick(() => {
        this.updateTask();
      });
    }
  },
  


  updateTask() {
      // Update the task properties in the store
      const callback = (response) => {
        const data = response.data;
      }
      const errCallback = (err) => {
        const message = err.response.data.message;
        this.__showNotif('error', 'Error', message || 'Error User');
      }

      delay(() => {
      const columnIndex = this.cols.findIndex(col => col.title === this.workData.type);
      let indexColumn = columnIndex
      this.cols[indexColumn].tasks.push(this.workData)
      const payload = this.cols.map((col) => {
        return {
          type: col.title,  
          taskIds: col.tasks.map(task => task.id)
        };
      });
      let finalParams = payload[indexColumn]
      finalParams.taskId = this.workData.id

      tasksApi.reorder(finalParams, callback, errCallback)
    }, 500);

      
    },
}
};
</script>

<style scoped>
</style>
