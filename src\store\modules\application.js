// import createPersistedState from "vuex-persistedstate";
import {
	useWindowSize
} from "@vueuse/core";

const {
	width,
	height
} = useWindowSize();
import tasksApi from "@/api/tasks";

export default {
	namespaced: true,
	// plugins: [createPersistedState()]
	state: () => ({
		projectSelected: null,
		onlineUsers: {},
		version: "1.0.00",
		activeAddTaskColumnIndex: null,
		wWidth: width,
		wHeight: height,
		data: null,
		hideAside: false,
		lightMode: true,
		isModal: false,
		mutate: false,
		project: null,
		starredProject: null,
		board: {
			active: "", // name of the active board
			activeIndex: 0,
			add: false,
			edit: false,
			delete: false,
		},
		task: {
			id: null,
			title: '',
			projectName: '',
			assign: '',
			creator: null,
			assignTo: '',
			startDate: '',
			dueDate: '',
			parentId: '',
			updatedAt: '',
			description: '',
			createdAt: '',
			status: '',
			index: '',
			type: '',
      meta: null,
			subTask: [],
			collaborator: [],
			comments: [],
			active: "", // name of the active task (in case of edit)
			activeIndex: -1, // index of active task
			columnIndex: -1, // index of column of activeTask
			status: "", // name of the column (when add / edit task) to which the task belongs
			add: false,
			addDirectly: false,
			edit: false,
			delete: false,
			show: undefined, // holds the task object, in taskShow
      slug: null,
		}
	}),
	mutations: {
		setOnlineUsers(state, items) {
			state.onlineUsers = items;
		},
		setData(state, d) {
			state.data = d.boards;
			if (JSON.stringify(state.data) != "{}") {
				state.board.active = state.data[0].title;
			}
		},
		setDataProject(state, project) {
			if (project) state.project = project;
		},
		setDatastarredProject(state, projects) {
			if (projects) state.starredProject = projects;
		},
		resetDataProject(state) {
			state.project = null;
		},
		showDetailTask(state, slug) {
			if (!slug) return;
			const callback = (response) => {
				const data = response.data;
				state.task = data
				state.task.active = data.id;
				state.task.status = data.status;
				state.task.projectName = data.project.name,
				state.task.type = data.type;
				state.task.addDirectly = false;
				state.task.edit = true;
				state.task.add = false;
				state.mutate = true;
			};
			const errCallback = (err) => {
				console.error(err);
			};
			tasksApi.get(slug, callback, errCallback);
		},
		changeStatus(state, colIndex) {
			let task = state.data[state.board.activeIndex].columns[
				state.task.columnIndex
			].tasks.splice(state.task.activeIndex, 1);

			// push it in the new status column
			state.data[state.board.activeIndex].columns[colIndex].tasks.push(task[0]);
		},
		updateTaskInStore(state, task) {
      const column = state.data[state.board.activeIndex].columns.find(col => col.title === task.type);
      if (column) {
          const taskIndex = column.tasks.findIndex(t => t.id === task.id);
          if (taskIndex !== -1) {
              Object.assign(column.tasks[taskIndex], task); // Update existing task
          } else {
              if (task.position === 'top') {
                  column.tasks.unshift(task); // Add new task at the beginning
              } else {
                  column.tasks.push(task); // Add new task at the end
              }
          }
      }
    },
  
		updateTaskInStore2(state, task) {
			const column = state.data[state.board.activeIndex].columns.find(col => col.title === task.type);
			if (column) {
				const taskIndex = column.tasks.findIndex(t => t.id === task.id);
				if (taskIndex !== -1) {
					column.tasks[taskIndex] = task; // Direct replacement triggers reactivity in Vue 3
				}
			}
		},

    deleteTaskInStore(state, task) {
      const column = state.data[state.board.activeIndex].columns.find(col => col.title === task.type);
      const taskIndex = column.tasks.findIndex(t => t.id === task.id);
      if (taskIndex !== -1) {
          column.tasks.splice(taskIndex, 1); // Remove task if found
      }
    },

    updateOneTaskInStoreSocket(state, task) {
      const column = state.data[state.board.activeIndex].columns.find(col => col.title === task.type);
      const taskIndex = column.tasks.findIndex(t => t.id === task.id);
      if (taskIndex !== -1) {
          Object.assign(column.tasks[taskIndex], task);
      }
    },

    updateTaskInStoreSocket(state, task) {
      // Find the new column where the task is supposed to go
      const newColumn = state.data[state.board.activeIndex].columns.find(col => col.title === task?.task?.type);
      // console.log(state.data[state.board.activeIndex].columns, task);
    
      // If newColumn exists, update or add the task to the new column
      if (newColumn) {
        const newTaskIndex = newColumn.tasks.findIndex(t => t.id === task?.task?.id);
    
        // If the task already exists in the new column, update it and adjust its position
        if (newTaskIndex !== -1) {
          // Update the task details
          Object.assign(newColumn.tasks[newTaskIndex], task?.task);
    
          // If the task is reordered within the same column, update its position
          newColumn.tasks.splice(newTaskIndex, 1); // Remove task from current position
          if (task?.task.position === 'top') {
            newColumn.tasks.unshift(task?.task); // Add to the top
          } else {
            newColumn.tasks.push(task?.task); // Add to the bottom
          }
        } else {
          // If task does not exist in the column, add it to the appropriate position
          if (task?.task.position === 'top') {
            newColumn.tasks.unshift(task?.task); // Add to the top
          } else {
            newColumn.tasks.push(task?.task); // Add to the bottom
          }
        }
      }
    
      // Find the old column where the task was before it was moved (only if it exists)
      if (task?.oldTask?.type !== task?.task?.type) { // Check if task moved to a different column
        const oldColumn = state.data[state.board.activeIndex].columns.find(col => col.title === task?.oldTask?.type);
    
        // If oldColumn exists, find and remove the task from the old column
        if (oldColumn) {
          const oldTaskIndex = oldColumn.tasks.findIndex(t => t.id === task?.oldTask?.id);
          if (oldTaskIndex !== -1) {
            oldColumn.tasks.splice(oldTaskIndex, 1); // Remove the old task
          }
        }
      }
    },
    

		resetStore(state) {
			state.data = null;
			state.hideAside = false;
			state.lightMode = true;
			state.isModal = false;
			state.mutate = false;
			state.board.active = "";
			state.board.activeIndex = 0;
			state.board.add = false;
			state.board.edit = false;
			state.board.delete = false;
			state.task = {
				id: null,
				title: '',
				assign: '',
				creator: null,
				assignTo: '',
				startDate: '',
				dueDate: '',
				parentId: '',
				updatedAt: '',
				description: '',
				createdAt: '',
				status: '',
				index: '',
				type: '',
				subTask: [],
				collaborator: [],
				comments: [],
				active: "",
				activeIndex: -1,
				columnIndex: -1,
				status: "",
				add: false,
				addDirectly: false,
				edit: false,
				delete: false,
				show: undefined,
			};
		},
		resetStoreTask(state) {
			state.task = {
				id: null,
				title: '',
				assign: '',
				creator: null,
				assignTo: '',
				startDate: '',
				dueDate: '',
				parentId: '',
				updatedAt: '',
				description: '',
				createdAt: '',
				status: '',
				index: '',
				type: '',
				subTask: [],
				collaborator: [],
				comments: [],
				active: "",
				activeIndex: -1,
				columnIndex: -1,
				status: "",
				add: false,
				addDirectly: false,
				edit: false,
				delete: false,
				show: undefined,
			};
		}
	},
	actions: {
		clearOnlineUsers({ commit }) {
			commit("setOnlineListManually", {});
		},
		setData({ commit }, d) {
			commit('setData', d);
		},
		setDataProject({ commit }, project) {
			commit('setDataProject', project);
		},
		setDatastarredProject({ commit }, projects) {
			commit('setDatastarredProject', projects);
		},
		changeStatus({ commit }, colIndex) {
			commit('changeStatus', colIndex);
		},
		updateTaskInStore({ commit }, task) {
			commit('updateTaskInStore', task);
		},
		updateTaskInStore2({ commit }, task) {
			commit('updateTaskInStore2', task);
		},
    updateTaskInStoreSocket({ commit }, task) {
			commit('updateTaskInStoreSocket', task);
		},
    updateOneTaskInStoreSocket({ commit }, task) {
			commit('updateOneTaskInStoreSocket', task);
		},
    deleteTaskInStore({ commit }, task) {
			commit('deleteTaskInStore', task);
		},
		resetStore({ commit }) {
			commit('resetStore');
		},
		resetStoreTask({ commit }) {
			commit('resetStoreTask');
		},
		resetDataProject({ commit }) {
			commit('resetDataProject');
		},
    showDetailTask({ commit }, id) {
			commit('showDetailTask', id);
		},
	},
	getters: {
		getOnlineUsers(state) {
			return state.onlineUsers;
		},
		getActiveProject: (state) => {
			return state.project;
		},
		getStarredProject: (state) => {
			return state.starredProject;
		},
		getActiveBoard: (state) => {
			if (state.data) {
				return state.data.find((el, index) => {
					if (el.title === state.board.active) {
						state.board.activeIndex = index;
						return el;
					}
				});
			}
		},
		getActiveColumn: (state, getters) => {
			if (state.data) {
				return getters.getActiveBoard.columns.find((el) => {
					if (el.title === state.task.type) {
						return el;
					}
				});
			}
		},
		getActiveTask: (state, getters) => {
      if (state.data) {
        if (getters.getActiveBoard.columns) {
          // Loop through all the columns in the active board
          for (const column of getters.getActiveBoard.columns) {
            // Search for the active task within the tasks array of each column
            const task = column.tasks.find((el, idx) => {
              if (el.id === state.task.active) {
                state.task.activeIndex = idx; // Store the index of the active task
                state.task.columnIndex = getters.getActiveBoard.columns.indexOf(column); // Store the index of the column
                return el;
              }
            });
      
            // If the task is found, return it
            if (task) {
              return task;
            }
          }
        }
      } else {
        // my task
        return state.task
      }
      
      // Return null if no task is found
      return null;
    },
		getBoardColsLength: (state, getters) => {
			if (state.data && state.data.length > 0) {
				return getters.getActiveBoard.columns.length;
			}
			return 0;
		}

	},
};