<!-- src/components/Counter.vue -->
<template>
    <div>
        <p data-test="counter">Counter: {{ count }}</p>
        <button data-test="increment" @click="increment">Increment</button>
    </div>
</template>

<script>
    export default {
        name: 'Counter',
        data() {
            return {
                count: 0,
            };
        },
        methods: {
            increment() {
                this.count++;
            },
        },
    };
</script>