<template>
  <loader-circle v-if="isFetching" />
    <div>
        <div class="flex justify-between m-2">
            <div class="mt-2 ml-4">
                <label for="table-header" class="font-medium text-gray-800">Offer - {{ package.name }}</label>
            </div>
            <div class="flex">
                <div>
                    <t-button :color="`primary-solid`" @click="add()">
                        <PlusIcon class="h-5 w-5 text-white" />
                        <span class="text-sm text-white">Add New</span>
                    </t-button>
                </div>
            </div>
        </div>
        <!-- Table Section -->
        <div v-if="!isFetching && items.length"
            class="m-2 overflow-x-auto [&::-webkit-scrollbar]:h-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300">
            <div class="min-w-full inline-block align-middle">
                <!-- Table -->
                <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                        <tr>
                            <th scope="col" class="w-[10px]">
                                <div class="text-gray-800"></div>
                            </th>
                            <th scope="col" class="min-w-[150px]">
                                <div
                                    class="pe-4 py-3 text-start flex items-center gap-x-1 text-sm font-medium text-gray-800">
                                    <!-- Sort Dropdown -->
                                    <div class="hs-dropdown relative inline-flex w-full cursor-pointer">
                                        <button id="hs-pro-dutnms" type="button"
                                            class="py-2.5 text-start font-semibold w-full flex items-center gap-x-1 text-sm text-nowrap  text-black-500 focus:outline-none focus:bg-gray-100"
                                            aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                                            Name
                                            <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24"
                                                height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="m7 15 5 5 5-5" />
                                                <path d="m7 9 5-5 5 5" /></svg>
                                        </button>

                                        <!-- Dropdown -->
                                        <div class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-40 transition-[opacity,margin] duration opacity-0 hidden z-10 bg-white rounded-sm shadow-[0_10px_40px_10px_rgba(0,0,0,0.08)]"
                                            role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-dutnms">
                                            <div class="p-1">
                                                <button type="button" @click="sortAsc()"
                                                    class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg"
                                                        width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                        stroke-linejoin="round">
                                                        <path d="m5 12 7-7 7 7" />
                                                        <path d="M12 19V5" /></svg>
                                                    Sort ascending
                                                </button>
                                                <button type="button" @click="sortDsc()"
                                                    class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                                                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg"
                                                        width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                        stroke-linejoin="round">
                                                        <path d="M12 5v14" />
                                                        <path d="m19 12-7 7-7-7" /></svg>
                                                    Sort descending
                                                </button>
                                            </div>
                                        </div>
                                        <!-- End Dropdown -->
                                    </div>
                                    <!-- End Sort Dropdown -->
                                </div>
                            </th>
                            <th scope="col" class="min-w-10">
                                <div
                                    class="px-4 py-3 text-start flex items-center gap-x-1 text-sm font-medium text-gray-800">
                                    <span> Price </span>
                                    <span>
                                        <svg class="flex-shrink-0 w-3.5 h-3.5 text-gray-500 dark:text-gray-500"
                                            xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round">
                                            <path d="m7 15 5 5 5-5" />
                                            <path d="m7 9 5-5 5 5" />
                                        </svg>
                                    </span>
                                </div>
                            </th>
                            <th scope="col" class="min-w-10">
                                <div
                                    class="px-4 py-3 text-start flex items-center gap-x-1 text-sm font-medium text-gray-800">
                                    <span> Quantity </span>
                                    <span>
                                        <svg class="flex-shrink-0 w-3.5 h-3.5 text-gray-500 dark:text-gray-500"
                                            xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round">
                                            <path d="m7 15 5 5 5-5" />
                                            <path d="m7 9 5-5 5 5" />
                                        </svg>
                                    </span>
                                </div>
                            </th>
                            <th scope="col">
                                <div
                                    class="px-4 py-3 text-start flex items-center gap-x-1 text-sm font-medium text-gray-800">
                                </div>
                            </th>
                        </tr>
                    </thead>

                    <tbody class="divide-y divide-gray-200">
                        <tr v-for="(item, index) in items" :key="item.id" @mouseover="onHover(item)"
                            :class="{'bg-slate-100': item.id === curerentActive}">
                            <td class="whitespace-nowrap py-3">
                                <div class="w-[50px] flex items-center">
                                    <div class="grow">
                                        <span class="text-sm font-normal ml-4 text-gray-800">
                                            {{ (index + 1) + ((page-1)*limit) }}
                                        </span>
                                    </div>
                                </div>
                            </td>
                            <td class="whitespace-nowrap pe-4 py-2" @dblclick="edit(item)">
                                <div class="w-full flex items-center gap-x-3">
                                    <div class="grow">
                                        <span class="text-sm font-normal text-gray-800">
                                            {{ parseItemName(item) }}
                                        </span>
                                    </div>
                                    <div class="w-[32px]">
                                        <div class="hs-dropdown hs-dropdown-example relative inline-flex w-[32px]"
                                            :class="{'hidden': item.id !== curerentActive}">
                                            <button id="hs-dropdown-example" type="button"
                                                @click="this.selectedItem = item"
                                                class="pl-1 h-[30px] text-start w-full flex items-center text-sm text-nowrap font-normal text-gray-800 shadow outline-gray-200 border border-gray-200 rounded focus:bg-gray-100"
                                                aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                                    stroke-width="2.5" stroke="currentColor" class="size-5 p-1">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        d="M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z" />
                                                </svg>
                                            </button>

                                            <div class="hs-dropdown-menu z-50 transition-[opacity,margin] duration hs-dropdown-open:opacity-100 opacity-0 
                                                hidden min-w-60 bg-white shadow-md rounded-md p-1 space-y-0.5 !mt-[-0.5rem] dark:bg-neutral-800 dark:border dark:border-neutral-700"
                                                role="menu" aria-orientation="vertical"
                                                aria-labelledby="hs-dropdown-custom-icon-trigger">
                                                <a data-hs-overlay="#drawer-right" @click="edit(item)"
                                                    class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700"
                                                    href="#">
                                                    Edit
                                                </a>
                                                <!-- <a @click="addNew(item)"
                                                    class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700"
                                                    href="#">
                                                    Duplicate
                                                </a> -->
                                                <hr class="border-1">
                                                <a @click="openDeleteModal(item)"
                                                    class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700"
                                                    href="#">
                                                    Delete
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="whitespace-nowrap px-4 py-3">
                                <span class="text-sm text-gray-600">
                                    NOK. {{ item.price }}
                                </span>
                            </td>
                            <td class="whitespace-nowrap px-4 py-3">
                                <span class="text-sm text-gray-600">
                                    {{ item.quantity }}
                                </span>
                            </td>
                            <td class="whitespace-nowrap px-4 py-3">
                                <div class="flex">

                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <!-- End Table -->
            </div>
        </div>
        <!-- End Table Section -->
        <!-- Footer -->
        <div v-if="!isFetching && items.length" class="mt-5 flex flex-wrap justify-between items-center gap-2">
            <p class="text-sm ml-4">
                <span class="font-medium text-stone-800 mr-1">{{ total }}</span>
                <span class="font-medium text-stone-800">Results</span>
            </p>
            <p class="text-sm ml-[8rem]">
                <span class="font-medium text-stone-800">Total Price</span>
                <span class="font-medium text-stone-800 ml-8 mr-1">NOK. {{ totalPrice }}</span>
            </p>
            <!-- Pagination -->
            <nav class="flex justify-end items-center gap-x-1" aria-label="Pagination">
                <button type="button" v-if="page > 1" @click="prevPage()"
                    class="py-2 inline-flex justify-center items-center gap-x-2 text-sm rounded-mdtext-stone-800 hover:bg-stone-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none"
                    aria-label="Previous">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round">
                        <path d="m15 18-6-6 6-6"></path>
                    </svg>
                    <span class="sr-only">Previous</span>
                </button>
                <div class="flex items-center gap-x-1 mr-2 ml-2">
                    <span
                        class="h-[26px] w-[26px] flex justify-center items-center bg-gray-300 py-2 text-sm rounded-md disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-700"
                        aria-current="page">{{ page }}</span>
                    <span
                        class="min-h-[38px] flex justify-center items-center text-stone-500 py-2 px-1.5 text-sm dark:text-neutral-500">of</span>
                    <span
                        class="min-h-[38px] flex justify-center items-center text-stone-500 py-2 px-1.5 text-sm dark:text-neutral-500">{{ maxPage }}</span>
                </div>
                <button type="button" v-if="page < maxPage" @click="nextPage()"
                    class="py-2 inline-flex justify-center items-center gap-x-2 text-sm rounded-mdtext-stone-800 hover:bg-stone-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none"
                    aria-label="Next">
                    <span class="sr-only">Next</span>
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round">
                        <path d="m9 18 6-6-6-6"></path>
                    </svg>
                </button>
            </nav>
            <!-- End Pagination -->
        </div>
        <!-- End Footer -->
        <DrawerRight :id="`drawer-right`" ref="drawerRight">
            <template #header>
                <div class="">
                    <div class="flex mt-4 pb-2">
                        
                        <p v-if="!isEdit" id="hs-pro-dutoo-label" class="text-sm font-semibold text-gray-800 ml-6 ">
                        Add Offer item
                        </p>
                        <p v-if="isEdit" id="hs-pro-dutoo-label" class="text-sm font-semibold text-gray-800 ml-6 ">
                        Edit {{ selectedItem.name }}
                        </p>
                    </div>
                </div>
            </template>
            <template #body>
                <div class="h-full px-5 overflow-y-auto overflow-hidden 
                  [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 
                  [&::-webkit-scrollbar-thumb]:bg-gray-300">
                    <!-- Item Type -->
                    <div class="mb-4 mt-4">
                        <label class="block text-sm font-semibold text-gray-700 mb-1">Item Type</label>
                        <div class="relative">
                            <select :tabIndex="1" @change="onOnTypeChange" v-model="selectedType"
                                class="block w-full px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option key="service" value="service">Service</option>
                                <option key="product" value="product" selected="true">Product</option>
                                <option key="task" value="task">Task</option>
                            </select>
                            <span v-if="errors.selectedType"
                                class="text-red-500 text-sm">{{ errors.selectedType }}</span>
                        </div>
                    </div>
                    <!-- Name -->
                    <div v-if="selectedType === 'task'" class="mb-4 grid grid-cols-1 gap-x-4 border-gray-200">
                        <label class="block text-sm font-semibold text-gray-700">Name</label>
                        <t-input :tabIndex="2" :type="`text-input`" v-model="name" :value="name"
                            placeholder="Type in Task name"></t-input>
                        <span v-if="errors.name" class="text-red-500 text-sm">{{ errors.name }}</span>
                    </div>
                    <!-- Product -->
                    <div class="mb-4" v-if="selectedType === 'product'">
                        <label class="block text-sm font-semibold text-gray-700 mb-1">Product</label>
                        <div class="relative">
                            <select :tabIndex="2" v-model="selectedProduct"
                                class="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="" disabled selected>Select Product item</option>
                                <option v-for="(product, index) in products" :key="product.id" :value="product.id">
                                    {{ product.name }}
                                </option>
                            </select>
                            <span v-if="errors.selectedProduct"
                                class="text-red-500 text-sm">{{ errors.selectedProduct }}</span>
                        </div>
                    </div>
                    <!-- Service -->
                    <div class="mb-4" v-if="selectedType === 'service'">
                        <label class="block text-sm font-semibold text-gray-700 mb-1">Service</label>
                        <div class="relative">
                            <select :tabIndex="2" v-model="selectedService"
                                class="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="" disabled selected>Select service item</option>
                                <option v-for="(service, index) in services" :key="service.id" :value="service.id">
                                    {{ service.name }}
                                </option>
                            </select>
                            <span v-if="errors.selectedService"
                                class="text-red-500 text-sm">{{ errors.selectedService }}</span>
                        </div>
                    </div>
                    <!-- Quantity -->
                    <div v-if="selectedType !== 'task'" class="mb-4 flex-1 flex flex-col overflow-y-auto overflow-hidden w-[250px]
                        [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 
                        [&::-webkit-scrollbar-thumb]:bg-gray-300">
                        <label class="text-sm font-semibold mb-2"> Quantity </label>
                        <div class="flex">
                            <!-- Input Number -->
                            <div class="mr-2 bg-white border border-gray-200 rounded-lg dark:bg-neutral-700 dark:border-neutral-700"
                                data-hs-input-number="">
                                <div class="w-full flex justify-between items-center gap-x-1">
                                    <div class="grow py-2 px-3">
                                        <input :tabIndex="3" class="w-full p-0 bg-transparent border-0 text-gray-800 focus:ring-0 
                                                [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none
                                                dark:text-white" style="-moz-appearance: textfield;" type="number"
                                            aria-roledescription="Number field" data-hs-input-number-input=""
                                            v-model="quantity" :value="quantity">
                                    </div>
                                    <div
                                        class="flex items-center -gap-y-px divide-x divide-gray-200 border-s border-gray-200 dark:divide-neutral-700 dark:border-neutral-700">
                                        <button :tabIndex="4" @click="this.quantity = this.quantity-1" type="button"
                                            class="size-10 inline-flex justify-center items-center gap-x-2 text-sm font-medium last:rounded-e-lg bg-white text-gray-800 hover:bg-gray-50 focus:outline-none focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:text-white dark:hover:bg-neutral-800 dark:focus:bg-neutral-800"
                                            aria-label="Decrease">
                                            <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24"
                                                height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M5 12h14"></path>
                                            </svg>
                                        </button>
                                        <button :tabIndex="5" @click="this.quantity = this.quantity+1" type="button"
                                            class="size-10 inline-flex justify-center items-center gap-x-2 text-sm font-medium last:rounded-e-lg bg-white text-gray-800 hover:bg-gray-50 focus:outline-none focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:text-white dark:hover:bg-neutral-800 dark:focus:bg-neutral-800"
                                            aria-label="Increase">
                                            <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24"
                                                height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M5 12h14"></path>
                                                <path d="M12 5v14"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <span v-if="errors.quantity" class="text-red-500 text-sm">{{ errors.quantity }}</span>
                        </div>
                    </div>
                    <!-- Description -->
                    <div class="grid grid-cols-1 gap-x-4 border-gray-200">
                        <label class="block text-sm font-semibold text-gray-700">Description</label>
                        <t-input :tabIndex="5" v-model="description" :value="description" :type="`area`"
                            placeholder="Type in service description"></t-input>
                        <span v-if="errors.description" class="text-red-500 text-sm">{{ errors.description }}</span>
                    </div>
                    <!-- Assign Task To -->
                    <div v-if="selectedType === 'task'" class="mt-3 mb-2 flex-1 flex flex-col overflow-y-auto overflow-hidden 
                        [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 
                        [&::-webkit-scrollbar-thumb]:bg-gray-300">
                        <label class="text-sm font-bold" for="cost">Assign Task To</label>
                        <div class="ml-2 mt-2 mb-2">
                            <div class="flex items-center mb-4">
                                <input id="hourly" type="radio" @change="this.isClient = true" :checked="isClient"
                                    class="form-radio text-blue-600 focus:ring-blue-500" />
                                <label for="hourly" class="text-sm ml-2 text-gray-700">Client </label>
                            </div>
                            <div class="flex items-center ">
                                <input id="unit" type="radio" @change="this.isClient = false" :checked="!isClient"
                                    class="form-radio text-blue-600 focus:ring-blue-500" />
                                <label for="unit" class="text-sm ml-2 text-gray-700">User</label>
                            </div>
                        </div>
                    </div>
                    <div clas="mt-2" v-if="!this.isClient && selectedType === 'task'">
                        <Multiselect v-model="selectedUser" :options="users" :custom-label="customLabel" no-result="No matches found" :allow-empty="true"
                            :show-labels="false" placeholder="Select user" :tabIndex="15">
                            <template #singleLabel="{ option }">
                                <div class="selected-item flex ">
                                    <img v-if="option.fullName" width="26" height="26" :src="getUserImage(option)"
                                        class="user-image" />
                                    <span class="m-0 p-0">{{ option.fullName || "Select User" }} </span>
                                </div>
                            </template>
                            <template #caret>
                                <svg xmlns="http://www.w3.org/2000/svg" class="absolute right-[11px] top-[13px] w-4 h-4"
                                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M6 9l6 6 6-6"></path>
                                </svg>
                            </template>
                            <template #option="{ option }">
                                <div class="option-item">
                                    <img v-if="option.fullName" width="26" height="26" :src="getUserImage(option)"
                                        class="user-image" />
                                    <span class="m-0 p-0">{{ option.fullName || "Select User" }} </span>
                                </div>
                            </template>
                            <template #noResult>
                                <span>No matches found</span>
                            </template>
                        </Multiselect>
                    </div>
                    <!-- Task Column -->
                    <div v-if="selectedType === 'task'" class="mb-4 mt-4">
                        <label class="block text-sm font-semibold text-gray-700 mb-1">Task Column</label>
                        <div class="relative">
                            <select @change="onColumnChange" v-model="selectedColumn"
                                class="block w-full px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option key="service" value="Back-log">Back-log</option>
                                <option key="product" value="Pre Production" selected="true">Pre Production</option>
                                <option key="task" value="Production">Production</option>
                                <option key="task" value="Post Production">Post Production</option>
                                <option key="task" value="Revision">Revision</option>
                                <option key="task" value="Completed">Completed</option>
                            </select>
                            <span v-if="errors.selectedColumn"
                                class="text-red-500 text-sm">{{ errors.selectedColumn }}</span>
                        </div>
                    </div>
                    <!-- <div v-if="selectedType === 'task'" class="mb-4 mt-4">
                        <label class="block text-sm font-semibold text-gray-700 mb-1">Task File</label>
                        <div class="relative">
                            <FileUpload @update:fileUrl="onUpload" :attchmentLength="attachmentlength(selectedItem)" />
                            
                        </div>
                    </div> -->
                    <!-- Due Date -->
                    <div v-show="selectedType === 'task'" class="mb-4 flex-1 flex flex-col overflow-y-auto overflow-hidden 
                    [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 
                    [&::-webkit-scrollbar-thumb]:bg-gray-300">
                        <label class="text-sm font-semibold mb-2"> Due Date </label>
                        <div class="flex">
                            <!-- Input Number -->
                            <div class="w-[150px] mr-2 bg-white border border-gray-200 rounded-lg dark:bg-neutral-700 dark:border-neutral-700"
                                data-hs-input-number="">
                                <div class="w-full flex justify-between items-center gap-x-1">
                                    <div class="grow px-3">
                                        <input class="w-full p-0 bg-transparent border-0 text-gray-800 focus:ring-0 
                            [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none
                            dark:text-white" style="-moz-appearance: textfield;" type="number"
                                            aria-roledescription="Number field" data-hs-input-number-input=""
                                            v-model="deliveryTime" :value="deliveryTime">
                                    </div>
                                    <div
                                        class="flex items-center -gap-y-px divide-x divide-gray-200 border-s border-gray-200 dark:divide-neutral-700 dark:border-neutral-700">
                                        <button @click="this.deliveryTime = this.deliveryTime-1" type="button"
                                            class="size-10 inline-flex justify-center items-center gap-x-2 text-sm font-medium last:rounded-e-lg bg-white text-gray-800 hover:bg-gray-50 focus:outline-none focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:text-white dark:hover:bg-neutral-800 dark:focus:bg-neutral-800"
                                            aria-label="Decrease" data-hs-input-number-decrement="">
                                            <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24"
                                                height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M5 12h14"></path>
                                            </svg>
                                        </button>
                                        <button @click="this.deliveryTime = this.deliveryTime+1" type="button"
                                            class="size-10 inline-flex justify-center items-center gap-x-2 text-sm font-medium last:rounded-e-lg bg-white text-gray-800 hover:bg-gray-50 focus:outline-none focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:text-white dark:hover:bg-neutral-800 dark:focus:bg-neutral-800"
                                            aria-label="Increase" data-hs-input-number-increment="">
                                            <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24"
                                                height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M5 12h14"></path>
                                                <path d="M12 5v14"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- dropdown -->
                            <div class="hs-dropdown w-[32px] ">
                                <button id="hs-dropdown-default" type="button" class="hs-dropdown-toggle py-3 px-4 inline-flex items-center 
                            gap-x-2 text-sm font-medium rounded-lg border border-gray-200 
                            bg-white text-gray-800 shadow-sm hover:bg-gray-50 focus:outline-none focus:bg-gray-50 
                            disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 
                            dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700"
                                    aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                                    {{ deliveryType === "hour" ? "Hour" : "Day(s)" }}
                                    <svg class="hs-dropdown-open:rotate-180 size-4" xmlns="http://www.w3.org/2000/svg"
                                        width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="m6 9 6 6 6-6" />
                                    </svg>
                                </button>

                                <div class="z-50 hs-dropdown-menu transition-[opacity,margin] duration hs-dropdown-open:opacity-100 
                            opacity-0 hidden min-w-60 bg-white shadow-md rounded-md p-1 space-y-0.5 mt-2 
                            dark:bg-neutral-800 dark:border dark:border-neutral-700 dark:divide-neutral-700 
                            after:h-4 after:absolute after:-bottom-4 after:start-0 after:w-full before:h-4 
                            before:absolute before:-top-4 before:start-0 before:w-full" role="menu"
                                    aria-orientation="vertical" aria-labelledby="hs-dropdown-default">
                                    <a @click="deliveryType='days'" class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 
                            focus:outline-none focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 
                            dark:focus:bg-neutral-700">
                                        Day(s)
                                    </a>
                                    <a @click="deliveryType='hour'" class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 
                            focus:outline-none focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 
                            dark:focus:bg-neutral-700">
                                        Hour
                                    </a>
                                </div>
                            </div>
                            <!-- End dropdown -->
                        </div>
                        <label class="block text-xs font-medium text-gray-400 mr-2"> Started from project creation date and task category</label>
                        <span v-if="errors.deliveryTime" class="text-red-500 text-sm">{{ errors.deliveryTime }}</span>
                    </div>
                    <div v-if="selectedType === 'task'" class="mb-4 mt-4">
                        <label class="block text-sm font-semibold text-gray-700 mb-1">Attachment(s)</label>
                        <span v-for="(attachment, index) in attachments">
                            <span class="flex"> <DocumentIcon class="w-4 mr-2" /> {{ printFileName(attachment) }} </span>
                            <!-- <Chip class="mt-2" :label="attachment" removable /> -->
                        </span>
                    </div>
                </div>
            </template>
            <template #footer>
                <div class="p-5 border-t border-gray-200 ">
                    
                    <div class="flex items-center gap-x-2 ">
                        <div v-if="selectedType === 'task'">
                            <FileUpload @update:fileUrl="onUpload" :attchmentLength="attachmentlength(selectedItem)" />
                        </div>
                        <div class="ml-auto">
                            <!-- Button -->
                            <t-button class="mr-2" :color="`secondary-solid`" @click="closeDrawer">
                                Cancel
                            </t-button>
                            <!-- End Button -->
                            <!-- Button -->
                            <t-button tabindex="10" :color="`primary-solid`" @click="save()" :isLoading="isSaving" :isDisabled="isSaving">
                                {{ $t('Save Changes') }}
                            </t-button>
                            <!-- End Button -->
                        </div>
                        
                    </div>
                </div>
            </template>
        </DrawerRight>

        <Confirmation :id="`confirm`">
            <template #header>
                <p class="text-base font-bold text-gray-800 dark:text-white">
                    Delete Item
                </p>
            </template>
            <template #body>
                <!-- <div class="text-xs text-gray-800 dark:text-neutral-400"> -->
                <p class="text-sm text-gray-800 dark:text-neutral-400"> Are you sure want to delete this item? </p>
                <!-- </div> -->
            </template>
            <template #footer>
                <button type="button" data-hs-overlay="#confirm"
                    class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 focus:outline-none focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
                    Cancel
                </button>
                <button type="button" @click="confirmDelete()" data-hs-overlay="#confirm"
                    class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent 
                bg-red-600 text-white hover:bg-red-700 focus:outline-none focus:bg-red-700 disabled:opacity-50 disabled:pointer-events-none">
                    Delete
                </button>
            </template>
        </Confirmation>
    </div>
    <div v-if="!isFetching && !items.length" class="font-bold text-center w-[300px] top-0 bottom-0 my-auto mx-auto right-0 left-0 absolute h-10 w-10 z-50">
    {{ $t('No records found') }}
</div>
</template>

<script>
    import {
        AdjustmentsIcon,
        UserCircleIcon
    } from "@heroicons/vue/solid";
    import TInput from '@/components/form/Input.vue';
    import {
        PlusIcon
    } from "@heroicons/vue/solid";
    import packageItemApi from "@/api/packageItem";
    import packageApi from "@/api/package";
    import serviceApi from "@/api/service";
    import productApi from "@/api/product";
    import userApi from "@/api/user";

    import DrawerRight from "@/components/form/DrawerRight.vue";
    import Confirmation from "@/components/modal/Confirmation.vue";
    import {
        HSStaticMethods
    } from "preline";
    import Multiselect from 'vue-multiselect';
    import "vue-multiselect/dist/vue-multiselect.css";
    import FileUpload from '@/components/form/fileUpload.vue';
    import Chip from 'primevue/chip';
    import { DocumentIcon } from "@heroicons/vue/outline";
    
    export default {
        name: "userExpertise",
        components: {
            AdjustmentsIcon,
            UserCircleIcon,
            PlusIcon,
            DrawerRight,
            TInput,
            Confirmation,
            Multiselect,
            FileUpload,
            Chip,
            DocumentIcon
        },
        data() {
            return {
                fileUrls: [],
                orderBy: 'name',
                sortBy: 'asc',
                page: 1,
                total: 0,
                maxPage: 1,
                limit: 10,
                name: null,
                description: "",
                quantity: 1,
                costPrice: 0,
                sellingPrice: 0,
                stock: 0,
                price: 0,
                isClient: false,
                deliveryTime: 1,
                deliveryType: "hour",
                selectedItem: null,
                selectedType: "product",
                selectedProduct: "",
                selectedService: "",
                selectedColumn: null,
                selectedUser: {},
                users: {},
                services: [],
                products: [],
                itemId: null,
                items: [],
                package: {},
                meta: null,
                isEdit: false,
                keyword: "",
                curerentActive: -1,
                isSaving: false,
                attachments: [],
                errors: {
                    name: null,
                    quantity: null,
                    description: null,
                    selectedType: null,
                    selectedService: null,
                    selectedProduct: null,
                    selectedColumn: null,
                    deliveryTime: null,
                },
                isFetching: false,
                debounceGetAll: null,
            };
        },
        computed: {
            totalPrice() {
                return this.package?.price
            }
        },
        created() {
            this.debounceGetAll = this.debounce(this.getAll, 300);
            this.getPackageById()
            this.getAllService()
            this.getAllProduct()
            this.getAllUser()
        },
        mounted() {

        },
        methods: {
            printFileName(attachment) {
                const splitUrl = attachment?.fileUrl.split('/')
                return splitUrl[splitUrl.length-1]
            },
            attachmentlength(item) {
                if (item?.attachments?.length > 0) {
                    return `${item?.attachments?.length} item(s) uploaded`
                } else {
                    return 'No file chosen'
                }
            },
            validateForm() {
                this.errors.selectedType = !this.selectedType ? 'Item Type is required' : null;
                this.errors.name = this.selectedType === 'task' && !this.name ? 'Task Name is required' : null;
                this.errors.quantity = this.selectedType !== 'task' && (!this.quantity || this.quantity <= 0) ?
                    'Quantity is required' : null;
                // this.errors.description = !this.description ? 'Description is required' : null;
                this.errors.selectedService = this.selectedType === 'service' && !this.selectedService ?
                    'Service selection is required' : null;
                this.errors.selectedProduct = this.selectedType === 'product' && !this.selectedProduct ?
                    'Product selection is required' : null;
                this.errors.selectedColumn = this.selectedType === 'task' && !this.selectedColumn ?
                    'Task Column selection is required' : null;
                this.errors.deliveryTime = this.selectedType === 'task' && (!this.deliveryTime || this.deliveryTime <=
                    0) ? 'Delivery Time is required' : null;

                return Object.values(this.errors).every(error => !error);
            },
            onUpload(urls) {
                this.fileUrls = urls;
                for (let index = 0; index < this.fileUrls.length; index++) {
                    const element = this.fileUrls[index];
                    // Check if the fileUrl already exists in the attachments array
                    const isDuplicate = this.attachments.some(attachment => attachment.fileUrl === element);
                    if (!isDuplicate) {
                        this.attachments.push({fileUrl: element});
                    } else {
                        console.log(`Duplicate fileUrl detected: ${element}`);
                    }
                }
                console.log(this.fileUrls)
                console.log(this.attachments)
            },
            openDeleteModal(item) {
                HSOverlay.open('#confirm');
                this.selectedItem = item
            },
            sortAsc() {
                this.sortBy = "asc"
                this.getAll()
            },
            sortDsc() {
                this.sortBy = "desc"
                this.getAll()
            },
            onHover(item) {
                this.curerentActive = item.id;
            },
            onHoverOut(item) {
                this.curerentActive = -1;
            },
            customLabel({
                username
            }) {
                return `${username}`;
            },
            getUserImage(user) {
                if (user.image) {
                    return user.image;
                } else {
                    return this.__generateInitialCanvas(user.fullName);
                }
            },
            generatePlaceholder(firstName) {
                const init = firstName ? `${firstName.charAt(0)}` : "PA"
                const initials = init;
                const canvas = document.createElement('canvas');
                canvas.width = 50;
                canvas.height = 50;
                const ctx = canvas.getContext('2d');
                ctx.fillStyle = '#000000';
                ctx.fillRect(0, 0, 50, 50);
                ctx.fillStyle = '#FFFFFF';
                ctx.font = '20px Arial';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(initials, 25, 25);
                return canvas.toDataURL();
            },
            parseItemName(item) {
                if (item.productId) {
                    const id = item.productId
                    const index = this.products.findIndex(item => item.id === id)
                    if (item.productId && index !== -1) return this.products[index].name
                }
                if (item.serviceItemId) {
                    const id = item.serviceItemId
                    const index = this.services.findIndex(item => item.id === id)
                    if (item.serviceItemId && index !== -1) return this.services[index].name
                }
                if ((!item.serviceItemId) && (!item.productId)) {
                    return item.taskName
                }
            },
            parseItemPrice(productId, serviceItemId) {
                if (productId) {
                    const id = productId
                    const index = this.products.findIndex(item => item.id === id)
                    if (productId && index !== -1) return this.products[index].sellingPrice
                }
                if (serviceItemId) {
                    const id = serviceItemId
                    const index = this.services.findIndex(item => item.id === id)
                    if (serviceItemId && index !== -1) return this.services[index].sellingPrice
                }
            },
            onOnTypeChange() {
                setTimeout(() => {
                    HSStaticMethods.autoInit();
                }, 500);
                // if (this.selectedType === 'service') this.getAllService()
                // if (this.selectedType === 'product') this.getAllProduct()
                if (this.selectedType !== 'task') this.taskName = ""
            },
            getAllService() {
                const callback = (response) => {
                    const data = response.data;
                    this.services = data;
                }
                const errCallback = (err) => {
                    const message = err?.response?.data?.message;
                    this.__showNotif('error', 'Error', message);
                }

                const params = {
                    orderBy: "name",
                    sortBy: "asc",
                    page: 1,
                    limit: 1000,
                }
                serviceApi.getList(params, callback, errCallback)
            },
            getAllProduct() {
                const callback = (response) => {
                    const data = response.data;
                    this.products = data;
                }
                const errCallback = (err) => {
                    const message = err?.response?.data?.message;
                    this.__showNotif('error', 'Error', message);
                }

                const params = {
                    orderBy: "name",
                    sortBy: "asc",
                    page: 1,
                    limit: 9999,
                }
                productApi.getList(params, callback, errCallback)
            },
            getAllUser() {
                const callback = (response) => {
                    const data = response.data;
                    this.users = data;
                }
                const errCallback = (err) => {
                    const message = err?.response?.data?.message;
                    this.__showNotif('error', 'Error', message);
                }

                const params = {
                    orderBy: "fullName",
                    sortBy: "asc",
                    page: 1,
                    limit: 9999,
                    excludeRoleIds: '[1, 6]',
                }
                userApi.getList(params, callback, errCallback)
            },
            getPackageById() {
                const id = this.$route.params.id || null;
                const callback = (response) => {
                    const data = response.data;
                    this.package = data;
                    this.getAll();
                }
                const errCallback = (err) => {
                    const message = err?.response?.data?.message;
                    this.__showNotif('error', 'Error', message);
                }
                packageApi.get(id, callback, errCallback)
            },
            nextPage() {
                this.page = this.page + 1;
                this.getAll()
                setTimeout(() => {
                    HSStaticMethods.autoInit();
                }, 500);
            },
            prevPage() {
                this.page = this.page - 1;
                this.getAll()
                setTimeout(() => {
                    HSStaticMethods.autoInit();
                }, 500);
            },
            debounce(func, wait) {
                let timeout;
                return function (...args) {
                    clearTimeout(timeout);
                    timeout = setTimeout(() => {
                        func.apply(this, args);
                    }, wait);
                };
            },
            onInputSearch() {
                // this.debounce(this.getAll(this.keyword), 300); // 300ms debounce
                this.debounceGetAll(this.keyword);
            },
            confirmDelete() {
                if (this.selectedItem) {
                    const callback = (response) => {
                        const data = response.data;
                        this.package = data;
                        const message = response.message;
                        const isDelete = true;
                        if (this.items.length <= 1  && this.page > 1) this.page = this.page - 1
                        this.getAll();
                        this.__showNotif('success', 'Success', message);
                    }
                    const errCallback = (err) => {
                        const message = err?.response?.data?.message;
                        this.__showNotif('error', 'Error', message);
                    }
                    const id = this.selectedItem.id;
                    packageItemApi.delete(id, callback, errCallback)
                }
            },
            closeDrawer() {
                this.$refs.drawerRight.visibleRight = false;
            },
            reset() {
                this.deliveryTime = 1
                this.selectedUser = ""
                this.name = ""
                this.description = ""
                this.selectedProduct = ""
                this.selectedService = ""
                this.quantity = 1
                this.taskName = ""
                this.taskColumn = ""
                this.isClient = false
                this.deliveryType = "hour"

                this.errors.deliveryTime = null
                this.errors.selectedUser = null
                this.errors.name = null
                this.errors.description = null
                this.errors.selectedProduct = null
                this.errors.selectedService = null
                this.errors.quantity = null
                this.errors.taskName = null
                this.errors.taskColumn = null
                this.errors.isClient = false
                this.errors.deliveryTime = null
                this.errors.deliveryType = null
                setTimeout(() => {
                    HSStaticMethods.autoInit();
                }, 500);
            },
            save() {
                if (this.validateForm()) {
                    if (!this.isEdit) {
                        this.addNew()
                    } else {
                        this.updateItem()
                    }
                    this.$refs.drawerRight.visibleRight = false;
                }
            },
            add() {
                this.attachments = []
                this.selectedItem = null;
                console.log(this.selectedItem)
                this.isEdit = false;
                this.reset(); 
                this.edit();
                setTimeout(() => {
                    HSStaticMethods.autoInit();
                }, 500);
            },
            edit(item) {
                this.$refs.drawerRight.visibleRight = true;
                if (item) {
                    this.isEdit = true;
                    this.selectedItem = item;
                    this.attachments = this.selectedItem.attachments;
                    this.deliveryTime = 0
                    if (item.productId) this.selectedType = 'product'
                    if (item.serviceItemId) this.selectedType = 'service'
                    if (!item.serviceItemId && !item.productId) this.selectedType = 'task'
                    const index = this.users.findIndex(user => user.id === item.assignTo)
                    if (index !== -1) this.selectedUser = this.users[index]
                    this.name = this.selectedItem.taskName
                    this.description = this.selectedItem.description
                    this.selectedProduct = this.selectedItem.productId ? this.selectedItem.productId : null
                    this.selectedService = this.selectedItem.serviceItemId ? this.selectedItem.serviceItemId : null
                    this.quantity = this.selectedItem.quantity
                    this.taskName = this.selectedItem.name
                    this.selectedColumn = this.selectedItem.taskColumn
                    if (!this.selectedUser) this.selectedUser  = {}
                    this.selectedUser.id = this.selectedItem?.assignTo
                    this.isClient = this.selectedItem.assignType === "client"
                    this.deliveryTime = this.selectedItem.deliveryTime
                    this.deliveryType = this.selectedItem.deliveryType
                    setTimeout(() => {
                        HSStaticMethods.autoInit();
                    }, 500);
                }
            },
            updateItem() {
                this.isSaving = true;
                let price = this.parseItemPrice(this.selectedProduct, this.selectedService)
                const callback = (response) => {
                    const data = response.data;
                    this.package = data.packages;
                    const message = response.message;
                    this.getAll();
                    this.name = "";
                    this.description = "";
                    this.__showNotif('success', 'Success', message);
                    this.isSaving = false;
                }
                const errCallback = (err) => {
                    const message = err?.response?.data?.message;
                    this.__showNotif('error', 'Error', message);
                    this.isSaving = false;
                }
                const params = {
                    description: this.description,
                    productId: this.selectedProduct ? this.selectedProduct : null,
                    serviceItemId: this.selectedService ? this.selectedService : null,
                    quantity: this.quantity,
                    price: price,
                    taskName: this.name,
                    taskColumn: this.selectedColumn,
                    assignTo: this.selectedUser?.id ?? null,
                    assignType: this.isClient ? 'client' : 'user',
                    deliveryTime: this.deliveryTime,
                    deliveryType: this.deliveryType,
                    files: this.attachments.map(attachment => attachment.fileUrl)
                }
                if (this.fileUrls) params.fileUrls = this.fileUrls
                this.isEdit = false;
                packageItemApi.update(this.selectedItem.id, params, callback, errCallback)
            },
            addNew(item = null) {
                this.isSaving = true;
                if (item) {
                    this.name = item.taskName
                    this.description = item.description
                    this.productId = item.productId ? item.productId : null
                    this.serviceItemId = item.serviceItemId ? item.serviceItemId : null
                    this.quantity = item.quantity
                    this.taskName = item.name
                    this.taskColumn = item.selectedColumn
                    this.selectedUser.id = item.assignTo
                    this.isClient = item.assignType === "client"
                    this.deliveryTime = item.deliveryTime
                    this.deliveryType = item.deliveryType
                }
                let price = this.parseItemPrice(this.selectedProduct, this.selectedService)
                const callback = (response) => {
                    const message = response.message;
                    const data = response.data;
                    this.package = data.packages;
                    this.getAll();
                    this.description = ""
                    this.selectedProduct = ""
                    this.selectedService = ""
                    this.quantity = 1
                    this.name = ""
                    this.selectedColumn = ""
                    this.selectedUser = {}
                    this.isClient = 1
                    this.deliveryTime = 1
                    this.deliveryType = "hour"

                    this.__showNotif('success', 'Success', message);
                    this.isSaving = false;
                }
                const errCallback = (err) => {
                    const message = err?.response?.data?.message;
                    this.__showNotif('error', 'Error', message);
                    this.isSaving = false;
                }
                const packageId = this.$route.params.id;
                
                const params = {
                    packageId: packageId,
                    description: this.description,
                    productId: this.selectedProduct ? this.selectedProduct : null,
                    serviceItemId: this.selectedService ? this.selectedService : null,
                    quantity: this.quantity,
                    price: price,
                    taskName: this.name,
                    taskColumn: this.selectedColumn,
                    assignTo: this.selectedUser.id,
                    assignType: this.isClient ? 'client' : 'user',
                    deliveryTime: this.deliveryTime,
                    deliveryType: this.deliveryType,
                    files: this.attachments.map(attachment => attachment.fileUrl)
                }
                packageItemApi.create(params, callback, errCallback)
            },
            getAll(keyword = null) {
                this.isFetching = true;
                const callback = (response) => {
                    const data = response.data;

                    const meta = response.meta;
                    this.items = data;
                    this.meta = meta;
                    this.page = meta.currentPage;
                    this.maxPage = meta.lastPage;
                    this.total = meta.total;
                    setTimeout(() => {
                        HSStaticMethods.autoInit();
                    }, 500);

                    if (keyword) {
                        this.page = 1;
                    }
                    this.isFetching = false;
                }
                const errCallback = (err) => {
                    console.log(err)
                    this.isFetching = false;
                }
                const packageId = this.$route.params.id;
                const params = {
                    orderBy: this.orderBy,
                    sortBy: this.sortBy,
                    page: this.page,
                    limit: this.limit,
                    packageId: packageId
                }
                if (keyword) params.keyword = keyword;
                packageItemApi.getList(params, callback, errCallback)
            }
        },


    };
</script>
<style>
    .user-image {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        object-fit: cover;
        margin-right: 10px;
    }

    .selected-item,
    .option-item {
        display: flex;
        align-items: center;
    }
</style>