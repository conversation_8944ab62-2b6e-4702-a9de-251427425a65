<template>
  <loader-circle v-if="isFetching" />
  <main v-if="!isFetching" :key="currentId" :class="classMain" class="bg-kb_light_grey dark:bg-kb_black overflow-y-auto overflow-x-auto pl-8">
    <div class="flex justify-between items-center px-6 pt-6 ">
      <div class="truncate font-medium">{{ project?.name }} - {{  __dateFormatProjectKanban(project?.startDate, project?.duration)  }}</div>
      <div class="flex">
        <!-- serach -->
        <div class="relative">
          <div class="absolute inset-y-0 start-0 flex items-center pointer-events-none z-20 ps-3.5">
            <svg class="shrink-0 size-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
              viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
              stroke-linejoin="round">
              <circle cx="11" cy="11" r="8" />
              <path d="m21 21-4.3-4.3" /></svg>
          </div>
          <form autocomplete="off">
            <input type="text" name="search" v-model="keyword" v-value="keyword" @input="onInputSearch"
              autocomplete="off"
              class="py-[7px] ps-10 pe-8 block w-full bg-white border-gray-200 rounded-md text-sm focus:bg-white focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none"
              placeholder="Search">
          </form>
        </div>
        <!-- sort filter -->
        <!-- Filter Dropdown -->
        <!-- Filter Dropdown -->
        <ButtonDropdown class="ml-2" :customClassItems="'ml-[-100px] mt-2'">
          <template #button>
            <t-button id="hs-pro-dptfd" :color="`primary-white`" class="px-5 py-2" aria-haspopup="menu"
              aria-expanded="false" aria-label="Dropdown">
              <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                stroke-linejoin="round">
                <line x1="21" x2="14" y1="4" y2="4" />
                <line x1="10" x2="3" y1="4" y2="4" />
                <line x1="21" x2="12" y1="12" y2="12" />
                <line x1="8" x2="3" y1="12" y2="12" />
                <line x1="21" x2="16" y1="20" y2="20" />
                <line x1="12" x2="3" y1="20" y2="20" />
                <line x1="14" x2="14" y1="2" y2="6" />
                <line x1="8" x2="8" y1="10" y2="14" />
                <line x1="16" x2="16" y1="18" y2="22" /></svg>
              {{ $t('Filter') }}
            </t-button>
          </template>
          <template #items>
            <MenuItem as="div" class="mb-2">
            <t-checkbox @click.stop="" class="font-normal" v-model="selectedStatuses.isMyTask" :label="$t('Just my tasks')" :value="selectedStatuses.isMyTask" />
            </MenuItem>
            <MenuItem as="div" class="mb-2">
            <t-checkbox @click.stop="" class="font-normal" v-model="selectedStatuses.isIncompleteTask" :label="$t('Incomplete tasks')" :value="selectedStatuses.isIncompleteTask" />
            </MenuItem>
            <MenuItem as="div" class="mb-2">
            <t-checkbox @click.stop="" class="font-normal" v-model="selectedStatuses.isCompleteTask" :label="$t('Completed tasks')" :value="selectedStatuses.isCompleteTask" />
            </MenuItem>
            <MenuItem as="div" class="mb-2">
            <t-checkbox @click.stop="" class="font-normal" v-model="selectedStatuses.isDueThisWeek" :label="$t('Due this week')" :value="selectedStatuses.isDueThisWeek" />
            </MenuItem>
            <MenuItem as="div" class="mb-2">
            <t-checkbox @click.stop="" class="font-normal" v-model="selectedStatuses.isDueNextWeek" :label="$t('Due next week')" :value="selectedStatuses.isDueNextWeek" />
            </MenuItem>
            </template>
        </ButtonDropdown>
      </div>
    </div>
    <ShowMain @columnChange="columnChange" @columnRemove="columnRemove" class="relative" />
  </main>
  <ModalContainer />
</template>

<script>
import {
    mapGetters,
    mapActions
  } from 'vuex';
import store from "./store";
import tasksApi from "@/api/tasks";
import HeaderBar from "@/components/kanban/Header/HeaderBar.vue";
import ShowMain from "@/components/kanban/Main/ShowMain.vue";
import ModalContainer from "@/components/kanban/ModalContainer.vue";
import projectApi from "@/api/project";
import kanbanApi from "@/api/kanban";
import TButton from '@/components/global/Button.vue';
import { HSStaticMethods } from "preline";
import { delay } from '@/libraries/helper';
import TCheckbox from '@/components/form/Checkbox.vue';

export default {
  components: {
    ShowMain,
    HeaderBar,
    ModalContainer,
    TButton,
    TCheckbox
  },
  data() {
    return {
      rando: Math.random(1,100),
      isDataOk: false,
      project: null,
      tasks: [],
      keyword: '',
      kanbanColumns: [],
      isFetching: true,
      dataKanban: {
        "boards": [{
            "title": "Kanban Planlagt",
            "columns": [
              { "title": "Back-log", "tasks": [] },
              { "title": "Pre Production", "tasks": [] },
              { "title": "Production", "tasks": [] },
              { "title": "Post Production", "tasks": [] },
              { "title": "Revision", "tasks": [] },
              { "title": "Complete", "tasks": [] }
            ]
          }
        ]
      },
      selectedStatuses: {
          isMyTask: false,
          isCompleteTask: false,
          isIncompleteTask: false,
          isDueThisWeek: false,
          isDueNextWeek: false,
        },
      projectSlug: null,
      projectId: null,
    };
  },
  computed: {
    ...mapGetters({
      getOnlineUsers: 'application/getOnlineUsers',
      getActiveProject: 'application/getActiveProject',
      getActiveColumn: 'application/getActiveColumn',
      getActiveTask: 'application/getActiveTask',
    }),
    classHeader() {
      if (this.wWidth < 768) {
        return "left-0 ";
      }
    },
    classAside() {
      if (this.wWidth < 768) {
        return "hidden ";
      }
      if (this.hideAside) {
        return "-translate-x-[100rem]";
      }
    },
    currentId() {
      return this.projectId;
    }
  },
  watch: {
    currentId() {
      // this.getTasks()
      // this.getProject()
    },
    selectedStatuses: {
      handler(newVal, oldVal) {
        this.resetStore()
        this.dataKanban = {
          boards: [{
            title: "Kanban Planlagt",
            columns: []
          }]
        };
        this.initDataKanban()
        this.getTasks()
      },
      deep: true, // Deep watcher to monitor all changes within the object
    },
  },
  methods: {
    ...mapActions({
      clearOnlineUsers: 'application/clearOnlineUsers',
      setData: 'application/setData',
      setDataProject: 'application/setDataProject',
      changeStatus: 'application/changeStatus',
      updateTaskInStore: 'application/updateTaskInStore',
      resetStore: 'application/resetStore',
    }),
    
    debounce(func, wait) {
      let timeout;
      return function(...args) {
          clearTimeout(timeout);
          timeout = setTimeout(() => {
          func.apply(this, args);
          }, wait);
      };
    },
    onInputSearch() {
      delay(() => {
        this.resetStore()
        this.getTasks()
      }, 1000);
    },
    getProject() {
      this.initDataKanban();
      this.resetStore();
      const callback = (response) => {
        const data = response.data;
        // Check if no project
        if (!data) {
          this.__showNotif('error', 'Project not found', "We're unable to locate the project you're looking for. It may have been moved or deleted.");
          this.$router.push({ name: 'Home' });
        }
        this.project = data;
        const roomId = data.slug;
        this.$soketio.emit('join', roomId);
        this.setDataProject(this.project);
      };
      const errCallback = (err) => {
        console.log(err);
      };
      projectApi.get(this.projectId, callback, errCallback);
    },
    getKanbanColumn() {
      const callback = (response) => {
        const data = response.data;
        this.kanbanColumns = data;
        this.populateKanban(this.kanbanColumns);
      };
      const errCallback = (err) => {
        console.log(err);
      };
      const params = {
        projectId: this.projectId,
        limit: 99,
      }
      kanbanApi.getList(params, callback, errCallback);
    },
    populateKanban(dataFromAPI) {
      // Reset the columns array
      this.dataKanban = {
        boards: [{
          title: "Kanban Planlagt",
          columns: []
        }]
      };
      for (let index = 0; index < dataFromAPI.length; index++) {
        const column = dataFromAPI[index];
        column.tasks = []
        column.title = column.name
        this.dataKanban.boards[0].columns.push(column);
      }
    },
    columnChange(data) {

      // Create a shallow copy of the current columns to avoid mutating the original array
      let currentColumns = [...this.$store.state.application.data[0].columns];

      // Find the column index to update or add
      let columnIndex = currentColumns.findIndex(col => col.id === data.id);
      
      if (columnIndex !== -1) {
        // If the column exists, update the existing column
        currentColumns[columnIndex].title = data.name;
      } else {
        // If the column doesn't exist, add it as a new column
        data.tasks = [];
        data.title = data.name;

        // Insert the new column based on direction and currentIndex
        if (data?.directions === 'left') {
          // Insert the new column before the currentIndex without removing any item
          currentColumns.splice(data?.currentIndex, 0, data);
        } else if (data?.directions === 'right') {
          // Insert the new column after the currentIndex without removing any item
          currentColumns.splice(data?.currentIndex + 1, 0, data);
        }
      }

      // After updating the columns array, assign the modified array back to the state
      this.$store.state.application.data[0].columns = currentColumns;
      const ids = currentColumns.map(item => item.id);
      this.reorderKanbanColumn(ids)
    },

    columnUpdate(data) {
      let currentColumns = [...this.$store.state.application.data[0].columns];
      let columnIndex = currentColumns.findIndex(col => col.id === data.id);
      if (columnIndex !== -1) {
        currentColumns[columnIndex].title = data.name;
      }
    },

    taskUpdate(data) {
      let currentColumns = [...this.$store.state.application.data[0].columns];
      let columnIndex = currentColumns.findIndex(col => col.id === data.id);
      if (columnIndex !== -1) {
        Object.assign(currentColumns[columnIndex], data);
        currentColumns[columnIndex].title = data.name;
      }
    },

    reorderKanbanColumn(ids) {
      const callback = (response) => {
        const data = response.data;
      };
      const errCallback = (err) => {
        console.log(err);
      };
      const params = {
        columnIds: ids,
      }
      kanbanApi.reorder(params, callback, errCallback);
    },


    columnRemove(columnId) {
      let columnIndex = this.$store.state.application.data[0].columns.findIndex(col => col.id === columnId);
      if (columnIndex !== -1) {
        this.$store.state.application.data[0].columns.splice(columnIndex, 1); // Remove the column
      }
    },
    async getTasks() {
      await this.resetStore()
      this.tasks = [];
      this.isFetching = true;
      const callback = async (response) => {
        const data = response.data;
        this.tasks = data;
        this.mapServerDataToKanban(this.tasks, this.dataKanban.boards[0].columns)
      };
      const errCallback = (err) => {
        console.log(err);
        this.isFetching = false;
      };
      let params = {
        orderBy: 'index',
        sortBy: 'asc',
        projectId: this.projectId,
        limit: 99999,
      }
      if (this.keyword) {
        this.initDataKanban();
        params.keyword = this.keyword
      } 
      if (this.selectedStatuses.isMyTask) params.isMyTask = 1
      if (this.selectedStatuses.isDueThisWeek) params.isDueThisWeek = 1
      if (this.selectedStatuses.isDueNextWeek) params.isDueNextWeek = 1
      if (this.selectedStatuses.isCompleteTask) params.status = 'completed'
      if (this.selectedStatuses.isIncompleteTask) params.status = 'incompleted'
      if (this.selectedStatuses.isCompleteTask && this.selectedStatuses.isIncompleteTask) delete params.status

      tasksApi.getList(params, callback, errCallback);
    },
    async mapServerDataToKanban(serverData, kanbanBoard) {
      // Process each task sequentially
      for await (const task of serverData) {
        const kanbanTask = {
          id: task.id,
          name: task.name,
          projectName: task.project.name,
          completed: task.completed,
          assign: task.assign,
          assignTo: task.assignTo,
          startDate: task.startDate,
          dueDate: task.dueDate,
          parentId: task.parentId,
          updatedAt: task.updatedAt,
          description: task.description,
          createdAt: task.createdAt,
          status: task.status,
          index: task.index,
          type: task.type,
          meta: task.meta,
          slug: task.slug,
          creator: task.creator,
          collaborator: task.collaborator,
          subTask: task.subTask ? task.subTask.map(subTask => ({
            name: subTask.name,
            isCompleted: subTask.status === 'completed'
          })) : [],
          // attributes for playing kanban
          activeIndex: -1, // index of active task
          active: '', // name of the active task (in case of edit)
          columnIndex: -1, // index of column of activeTask
          add: false,
          edit: false,
          delete: false,
          show: undefined, // holds the task object, in taskShow
        };

        // Find the correct column and add the task to it
        const column = kanbanBoard.find(col => col.title === task.type);
        if (column) {
          column.tasks.push(kanbanTask);
        }
      }

      // Set the data and update the state
      this.setData(this.dataKanban);
      this.isFetching = false;
      this.isDataOk = true;

      // Return the updated Kanban board
      return kanbanBoard;
    },

    initDataKanban() {
      this.getKanbanColumn()
    }
  },
  mounted() {
    // this.resetStore()
    this.getTasks()
    this.getProject()
    setTimeout(() => {
      HSStaticMethods.autoInit();
    }, 500);
  },
  beforeUnmount () {
    const roomId = this.project.slug;
    this.$soketio.emit('leave', roomId);
  },
  created() {
    const encryptedId = this.$route.params.id; // Get the encrypted string from route params
    const decryptedData = this.__decryptProjectData(encryptedId); // Decrypt the data
    
    if (decryptedData) {
      this.projectId = decryptedData.id;   // Access the original project ID
      this.projectSlug = decryptedData.slug; // Access the original project slug
    }

    this.getKanbanColumn()
    this.initDataKanban()
    this.$soketio.on('project_update', (data) => {
      if (this.project.id === data.id) this.project = data
    });

    let kanbanDataReceived = null;
    let tasks = []
    // Listen for kanban_update event and store the data
    this.$soketio.on('kanban_update', (kanbanData) => {
      let column = kanbanData
      column.tasks = tasks
      this.columnUpdate(column);
      kanbanDataReceived = column; // Store kanbanData for later use
    });

    // Listen for task_update_bulk event (register it once)
    this.$soketio.on('task_update_bulk', (taskData) => {
      tasks = taskData?.sort((a, b) => a.index - b.index)
      if (kanbanDataReceived) {
        // If kanban data is available, merge it with task data
        let updatedColumn = { ...kanbanDataReceived }; // Copy the kanban data
        updatedColumn.tasks = taskData; // Add the tasks to the updated column

        // Call columnChange with the combined data
        this.taskUpdate(updatedColumn);
      } else {
        console.warn('No kanban data available when tasks were updated');
      }
    });
    // need add additionalInfoAddingColumn
    this.$soketio.on('kanban_add', (data) => {
      this.columnChange(data)
    });
    this.$soketio.on('kanban_delete', (data) => {
      this.columnRemove(data.id)
    });
  },
};
</script>

<style scoped>
main {
  @apply absolute left-0 bottom-0 right-0;
}

main {
  @apply top-[0px];
}

._main {
  @apply left-[300px] top-[00px];
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 2.5s;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}

#app {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>
