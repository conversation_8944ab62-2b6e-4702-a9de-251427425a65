<template>
  <div class="calendar-edit relative ml-2" :class="{'mr-[-18px]': isNear, 'mr-[-34px]': !isNear, 'mr-[-38px]': isToday, 'mr-[2px]': !date}">
    <CalendarIcon v-if="date" class="absolute top-0 left-0 ml-[-26px] mt-[5px] h-4 w-4 text-gray-800" />
    <VueDatePicker @date-update="datePickerSelect" :class="{'custom-datepicker-empty dp__input_icon_pad_empty': !date, 'custom-datepicker dp__input_icon_pad': date}" v-model="date" :markers="markers" auto-apply :enable-time-picker="false" :clearable="true" :format="formatDate" :disabled-dates="disablePastDates">
      <template #month-year="{ month, year, months, years, updateMonthYear, handleMonthYearChange }">
        <div class="icons">
          <span class="custom-icon" @click="handleMonthYearChange(false)">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
              <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5 8.25 12l7.5-7.5" />
            </svg>
          </span>
        </div>
        <div class="custom-month-year-component">
          <select class="month-input font-medium" :value="month" @change="updateMonth($event, updateMonthYear, year)">
            <option v-for="m in fullMonths" :key="m.value" :value="m.value">
              {{ m.text }}
            </option>
          </select>
          /
          <select class="year-input font-medium" :value="year" @change="updateYear($event, updateMonthYear, month)">
            <option v-for="y in years" :key="y.value" :value="y.value">
              &nbsp; {{ y.text }}
            </option>
          </select>
        </div>
        <div class="icons">
          <span class="custom-icon" @click="handleMonthYearChange(true)">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
              <path stroke-linecap="round" stroke-linejoin="round" d="m8.25 4.5 7.5 7.5-7.5 7.5" />
            </svg>
          </span>
        </div>
      </template>
      <template #marker="{ marker, day, date }">
        <span class="custom-marker"></span>
      </template>
      <template #action="{ clearValue }">
        <button class="custom-clear-button" @click="clearValue">Clear</button>
      </template>
      <template #action-extra="{ clearValue }">
        <button class="custom-clear-button mt-4 mb-2" @click="clearDate">Clear</button>
      </template>
    </VueDatePicker>
    <div v-if="isShowEstimated" class="mt-4">
      <label class="time-label font-bold">{{ $t('Estimated event duration') }}</label>
      <div class="time-label text-xs text-gray-400">{{ $t('Consider extra time for rehearsal and preparation') }}</div>
      <div class="flex">
        <div v-for="hour in hoursExtend" @click="selectDuration(hour)"
        :class="{'bg-[#2563EB] text-white': duration === hour.value}"
        class="text-sm mt-4 font-bold shadow pointer max-w-[80px] mr-4 p-1 text-center rounded-md w-full border border-gray-300 hover:bg-[#2563EB] hover:text-white mb-6">
          {{ hour.label }}
        </div>
      </div>
    </div>

    <div v-if="isButtonAction" class="mt-4 h-[60px]">
      <div class="border border-gray-300 w-full"></div>
      <div class="flex justify-between items-center mt-2">
        <div class="text-primary-600 font-bold text-sm" @click="customizeEvent()">{{  $t('Customize Event') }}</div>
        <t-button
          :type="'submit'"
          :color="`primary-solid`"
          :isLoading="isBooking"
          :isDisabled="!isFormValid || isBooking"
          @click="bookStudio()"
        >
          {{ $t('Book Now') }}
        </t-button>
      </div>
    </div>
  </div>
</template>


<script>
import {
  CalendarIcon,
} from '@heroicons/vue/outline';
import VueDatePicker from "@vuepic/vue-datepicker";
import "@vuepic/vue-datepicker/dist/main.css";
import addDays from 'date-fns/addDays';
import isBefore from 'date-fns/isBefore';
import startOfDay from 'date-fns/startOfDay';
import TButton from '@/components/global/Button.vue';
import { ref } from 'vue';

export default {
  components: { VueDatePicker, TButton, CalendarIcon },
  data() {
    return {
      isSaving: false,
      date: null,
      markers2: [
        { date: addDays(new Date(), 1), type: 'dot', tooltip: [{ text: 'Dot with tooltip', color: 'green' }] },
        { date: addDays(new Date(), 2), type: 'line', tooltip: [{ text: 'First tooltip', color: 'blue' }, { text: 'Second tooltip', color: 'yellow' }] },
        { date: addDays(new Date(), 3), type: 'dot', color: 'yellow' },
      ],
      fullMonths: [
        { text: 'January', value: 0 }, { text: 'February', value: 1 }, { text: 'March', value: 2 },
        { text: 'April', value: 3 }, { text: 'May', value: 4 }, { text: 'June', value: 5 },
        { text: 'July', value: 6 }, { text: 'August', value: 7 }, { text: 'September', value: 8 },
        { text: 'October', value: 9 }, { text: 'November', value: 10 }, { text: 'December', value: 11 },
      ],
      hours: 12, // Default hour
      ampm: 'PM', // Default AM/PM
      hoursExtend: [
        { label: '2 HRS', value: 2 },
        { label: '4 HRS', value: 4 },
        { label: '8 HRS', value: 8 },
      ],
      duration: 2,
      isNear: false,
      isToday: false,
    };
  },
  props: {
    isShowEstimated: {
      type: Boolean,
      default: false,
    },
    isButtonAction: {
      type: Boolean,
      default: false,
    },
    isBooking: {
      type: Boolean,
      default: false,
    },
    schedules: {
      type: Array,
      default: [],
    },
    currentDate: {
      type: String,
    }
  },
  computed: {
    hoursArray() {
      return Array.from({ length: 12 }, (_, i) => ({
        value: i + 1,
        text: (i + 1).toString().padStart(2, '0'),
      }));
    },
    isFormValid() {
      return this.date ;
    },
    
    markers() {
      return this.schedules
      .filter(item => item.status === "Not Available")
      .map(item => ({
        date: new Date(item.date),
        type: 'dot'
      }));
    },
  },
  mounted() {
    this.duration = 2,
    this.$emit('updateDatepicker', this.date);
    if (this.currentDate) {
      this.date = this.currentDate
    }
  },
  updated() {
  },
  methods: {
    datePickerSelect(date) {
      this.$emit('select', date)
    },
    clearDate() {
      this.date = null;
      this.$emit('select', null)
      this.$emit('updateDatepicker', null);
    },
    getDiffInDays(date) {
    if (!date) return null;

      const currentDate = startOfDay(new Date()); // normalize current date to start of the day
      const selectedDate = startOfDay(new Date(date)); // normalize selected date to start of the day

      const diffInTime = selectedDate - currentDate; // difference in milliseconds
      return Math.floor(diffInTime / (1000 * 60 * 60 * 24)); // convert milliseconds to days
    },
    formatDate(date) {
      const diffInDays = this.getDiffInDays(date);
      if (diffInDays === 0) {
        this.isToday = true;
        this.isNear = false;
        return 'Today';
      } else if (diffInDays === -1) {
        this.isNear = true;
        this.isToday = false;
        return 'Yesterday';
      } else if (diffInDays === 1) {
        this.isToday = false;
        this.isNear = true;
        return 'Tomorrow';
      } else {
        const options = { day: 'numeric', month: 'short' };
        return new Intl.DateTimeFormat('en-US', options).format(date);
      }
    },
    selectDuration(hour) {
      this.duration = hour.value;
      this.$emit('updateDuration', hour.value);
    },
    bookStudio() {
      this.$emit('actionRight');
    },
    customizeEvent() {
      this.$emit('actionLeft');
    },
    onHourChange(value) {
      this.hours = value;
      this.updateDate();
    },
    onAmPmChange(value) {
      this.ampm = value;
      this.updateDate();
    },
    updateDate() {
      let hours = parseInt(this.hours);
      if (this.ampm === 'PM' && hours !== 12) {
        hours += 12;
      } else if (this.ampm === 'AM' && hours === 12) {
        hours = 0;
      }

      const date = new Date(this.date);
      date.setHours(hours);
      this.date = date;
    },
    updateMonth(event, updateMonthYear, year) {
      updateMonthYear(+event.target.value, year);
    },
    updateYear(event, updateMonthYear, month) {
      updateMonthYear(month, +event.target.value);
    },
    isHourUnavailable(hour) {
      if (!this.date) return false;

      const selectedDate = this.date.toISOString().split('T')[0];
      const unavailableEntry = this.schedules.find(schedule => schedule.date === selectedDate);
      
      if (!unavailableEntry) return false;

      let convertedHour = parseInt(hour);
      if (this.ampm === 'PM' && convertedHour !== 12) {
        convertedHour += 12;
      } else if (this.ampm === 'AM' && convertedHour === 12) {
        convertedHour = 0;
      }

      console.log(unavailableEntry.hours, 'kia');

      const hourString = convertedHour.toString().padStart(2, '0') + ':00';
      return unavailableEntry.hours.includes(hourString);
    },
    disablePastDates(date) {
      return isBefore(date, startOfDay(new Date()));
    },
  },
  watch: {
    date() {
      // Emit the updated date with minutes set to 00
      this.$emit('updateDatepicker', this.date);
    },
  },
};
</script>



<style lang="scss">
.calendar-edit {
.dp--clear-btn {
    display: none; /* Hide the clear button */
  }
  .dp__input {
    font-size: 12px;
  }
.custom-datepicker .dp__input_icons {
  border: 1px dashed gray;
  border-radius:100%;
  width: 14px;
  height: 14px;
  stroke-width: 2px!important;
  padding: 4px;
  display: none;
}
.custom-datepicker-empty .dp__input_icons {
  border: 1px dashed gray;
  border-radius:100%;
  width: 14px;
  height: 14px;
  stroke-width: 2px!important;
  padding: 4px;
}
  .dp__pointer {
    border: none;
    margin-right: -70px;
  }
  .dp__input_icon_pad_empty {
    width: 20px;
    padding-left: 0px;
    margin: 0px
  }
  .dp__input_icon_pad {
    max-width: 66px;
    padding: 0px;
    margin: 0px
  }
.dp__arrow_top {
  display: none;
  margin-right: -30px
  
}
.dp-input {
  padding-right: 0px;
}
.dp--clear-btn {
  margin-right: -14px;
  margin: 0;
  padding: 0px;
}
.dp__menu {
    border: none;
    padding: 5px;
    margin-left: -120px!important;
  }
  .dp__menu_inner {
    padding: 0;
  }

  .dp__flex_display {
    display: block !important;
  }

  .custom-time-picker-component {
    align-items: center;
    justify-content: center;
  }

  .dp__today {
    border: none;
    font-weight: bold;
    color: var(--dp-primary-color);
  }

  .time-input {
    background-image: none;
  }

  .dp__calendar_header {
    font-weight: lighter;
    color: grey;
  }

  .dp__calendar_row {
    justify-content: space-between;
    padding: 0;
    margin: 0;
  }

  .dp__active_date {
    border-radius: 100%;
    color: white;
    font-weight: lighter;
  }

  .dp__calendar_item {
    box-sizing: border-box;
    /* Include padding and border in the element's total width and height */
    border: 1px solid #ccc;
    /* Border color */
    /* Adjust padding for better spacing */
    margin: 0;
    /* Remove margin to align items properly */
    text-align: center;
    /* Center align text */
    position: relative;
    /* Relative position for markers */
    width: 38px;
  }
  

  .dp__calendar_item[data-marker] {
    background-color: #f8d7da;
    /* Background color for marked dates */
    border: 1px solid #f5c6cb;
    /* Border color for marked dates */
  }

  .month-input {
    border: none;
    min-width: 80px;
    padding-right: 1rem;
    background-image: none;
  }

  .year-input {
    border: none;
    width: 80px;
    padding-right: 0.5rem;
    background-image: none;
  }

  .custom-month-year-component {
    display: flex;
    align-items: center;
    margin: 0 auto;
  }

  .icons {
    display: flex;
    box-sizing: border-box;
  }

  .custom-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    width: 25px;
    color: var(--dp-icon-color);
    text-align: center;

    svg {
      height: 20px;
      width: 20px;
    }

    &:hover {
      background: var(--dp-hover-color);
    }
  }


  .custom-marker {
    position: absolute;
    top: -6px;
    left: -11px;
    height: 45px;
    width: 58px;
    background-color: #ED7070;
    pointer-events: none;
    /* Disable pointer events on markers */
    z-index: -1;
  }

  .dp__calendar_item.selected {
    background-color: #007bff;
    /* Background color for selected date */
    color: white;
    /* Text color for selected date */
  }

  .dp__pointer {
    z-index: 2;
  }

  .custom-clear-button {
  background-color: #2563EB;
  color: white;
  padding: 5px 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
}

.custom-clear-button:hover {
  background-color: #2563EB;
}
}
</style>
