// @ts-check
const { expect } = require('@playwright/test');
const { test } = require('./fixtures');

test('Counter component should increment when button is clicked', async ({ loggedInPage }) => {
  // Use the logged-in page
  const page = loggedInPage;

  // Navigate to the app's URL after login and wait until network is idle
  await page.goto('http://localhost:4444/testing-fikuri', { waitUntil: 'networkidle' });

  // Verify initial counter value
  const counterText = await page.locator('[data-test="counter"]').textContent();
  expect(counterText.trim()).toBe('Counter: 0');

  // Click the increment button
  await page.click('[data-test="increment"]');

  // Verify the counter has incremented
  const newCounterText = await page.locator('[data-test="counter"]').textContent();
  expect(newCounterText.trim()).toBe('Counter: 1');
});
