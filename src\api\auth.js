import { client } from '@/libraries/http-client';
import { buildQuery } from '@/libraries/helper';

const endpoint = '/api/users';

export default {
	// Login
	login(creds, cb, errorCb) {
		client.post('/auth/login', creds)
			.then((response) => {
				if (cb) {
					cb(response.data);
				}
			})
			.catch((e) => {
				if (errorCb) {
					errorCb(e);
				}
			});
	},
	// Reset Password
	forgot(emailUser, cb, errorCb) {
		const params = {
			email: emailUser,
		};
		client.post('/auth/forgotPassword', params)
			.then((response) => {
				if (cb) {
					cb(response.data);
				}
			})
			.catch((e) => {
				if (errorCb) {
					errorCb(e);
				}
			});
	},
	// Reset Password
	reset(params, cb, errorCb) {
		client.post('/auth/resetPassword', params)
			.then((response) => {
				if (cb) {
					cb(response.data);
				}
			})
			.catch((e) => {
				if (errorCb) {
					errorCb(e);
				}
			});
	},
	// Register
	register(params, cb, errorCb) {
		client.post('/auth/register', params)
			.then((response) => {
				if (cb) {
					cb(response.data);
				}
			})
			.catch((e) => {
				if (errorCb) {
					errorCb(e);
				}
			});
	},

	// Verify
	verify(tokenUser, cb, errorCb) {
		const params = {
			token: tokenUser,
		};
		client.post('/auth/verification', params)
			.then((response) => {
				if (cb) {
					cb(response.data);
				}
			})
			.catch((e) => {
				if (errorCb) {
					errorCb(e);
				}
			});
	},
	// resend verif email
	resend(emailUser, cb, errorCb) {
		const params = {
			email: emailUser,
		};
		client.post('/auth/resendToken', params)
			.then((response) => {
				if (cb) {
					cb(response.data);
				}
			})
			.catch((e) => {
				if (errorCb) {
					errorCb(e);
				}
			});
	},
	// login social media
	loginSocialMedia(params, cb, errorCb) {
		client.post('/auth/loginSocialMedia', params)
			.then((response) => {
				if (cb) {
					cb(response.data);
				}
			})
			.catch((e) => {
				if (errorCb) {
					errorCb(e);
				}
			});
	},

	// ge profile
	getProfile(cb, errorCb) {
		client.get('/auth/me')
			.then((response) => {
				if (cb) {
					cb(response.data);
				}
			})
			.catch((e) => {
				if (errorCb) {
					errorCb(e);
				}
			});
	},

	// update profile
	update(params, cb, errorCb) {
		client.put('/auth/update', params)
			.then((response) => {
				if (cb) {
					cb(response.data);
				}
			})
			.catch((e) => {
				if (errorCb) {
					errorCb(e);
				}
			});
	},

	// changePassword
	changePassword(params, cb, errorCb) {
		client.put('/auth/changePassword', params)
			.then((response) => {
				if (cb) {
					cb(response.data);
				}
			})
			.catch((e) => {
				if (errorCb) {
					errorCb(e);
				}
			});
	},
};
