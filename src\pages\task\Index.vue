
<template>
   <!-- Table Section -->
   <!-- serach -->
   <div class="flex items-center justify-end truncate px-6 pt-6 ">
      <div class="mr-4">{{ project?.name }}</div>
      <!-- serach -->
      <div>
        <div class="relative mr-4">
          <div class="absolute inset-y-0 start-0 flex items-center pointer-events-none z-20 ps-3.5">
            <svg class="shrink-0 size-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
              viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
              stroke-linejoin="round">
              <circle cx="11" cy="11" r="8" />
              <path d="m21 21-4.3-4.3" /></svg>
          </div>
          <form autocomplete="off">
            <input type="text" name="search" v-model="keyword" v-value="keyword" @input="onInputSearch"
              autocomplete="off"
              class="py-[7px] ps-10 pe-8 block w-full bg-white border-gray-200 rounded-md text-sm focus:bg-white focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none"
              placeholder="Search">
          </form>
        </div>
      </div>
      <!-- <t-button :color="`primary-solid`">
        <PlusIcon class="h-5 w-5 text-white" />
        <span class="text-sm text-white">Add New</span>
      </t-button> -->
    </div>
   <div v-if="!isFetching"
    class="overflow-x-auto mt-6 [&::-webkit-scrollbar]:h-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300">
    <div class="min-w-full inline-block align-middle">
      <!-- Table -->
      <table class="min-w-full">
        <thead class="border-t border-b border-gray-200">
          <tr class="">
            <th scope="col" class="px-3 py-2.5 text-start">
            </th>

            <th scope="col" class="min-w-80">
              <!-- Sort Dropdown -->
              <div class="hs-dropdown relative inline-flex w-full cursor-pointer">
                <button id="hs-pro-dutnms" type="button"
                  class="px-2 py-2.5 text-start w-full flex items-center gap-x-1 text-sm text-nowrap font-normal text-gray-500 focus:outline-none focus:bg-gray-100"
                  aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                  Task Name
                </button>
              </div>
              <!-- End Sort Dropdown -->
            </th>

            <th scope="col" class="min-w-40">
              <!-- Sort Dropdown -->
              <div class="hs-dropdown relative inline-flex w-full cursor-pointer">
                <button id="hs-pro-dutads" type="button"
                  class="px-5 py-2.5 text-start w-full flex items-center gap-x-1 text-sm text-nowrap font-normal text-gray-500 focus:outline-none focus:bg-gray-100"
                  aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                  Due Date
                  <!-- <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                    stroke-linejoin="round">
                    <path d="m7 15 5 5 5-5" />
                    <path d="m7 9 5-5 5 5" /></svg> -->
                </button>

                <!-- Dropdown -->
                <!-- <div
                  class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-40 transition-[opacity,margin] duration opacity-0 hidden z-10 bg-white rounded-sm shadow-[0_10px_40px_10px_rgba(0,0,0,0.08)]"
                  role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-dutads">
                  <div class="p-1">
                    <button type="button"
                      class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                      <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                        stroke-linecap="round" stroke-linejoin="round">
                        <path d="m5 12 7-7 7 7" />
                        <path d="M12 19V5" /></svg>
                      Sort ascending
                    </button>
                    <button type="button"
                      class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                      <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                        stroke-linecap="round" stroke-linejoin="round">
                        <path d="M12 5v14" />
                        <path d="m19 12-7 7-7-7" /></svg>
                      Sort descending
                    </button>
                  </div>
                </div> -->
                <!-- End Dropdown -->
              </div>
              <!-- End Sort Dropdown -->
            </th>

            <th scope="col" class="min-w-80">
              <!-- Sort Dropdown -->
              <div class="hs-dropdown relative inline-flex w-full cursor-pointer">
                <button id="hs-pro-dutsgs" type="button"
                  class="px-5 py-2.5 text-start w-full flex items-center gap-x-1 text-sm text-nowrap font-normal text-gray-500 focus:outline-none focus:bg-gray-100"
                  aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                  Project
                </button>
              </div>
              <!-- End Sort Dropdown -->
            </th>
          </tr>
        </thead>

        <tbody class="">
          <tr class=" border-b border-gray-200 hover:bg-slate-100 pointer" v-for="(task, index) in tasks" :key="task.id" @click="goToDetail(task)">
            <td class="pl-6  whitespace-nowrap py-4 w-[50px]">
              {{ (page - 1) * 10 + (index + 1) }}
            </td>
            <td class=" whitespace-nowrap px-3 py-1">
              <span class="text-sm text-gray-600">
                {{task.name || '-'}}
              </span>
            </td>
            <td class=" whitespace-nowrap px-6 py-1">
              <span class="text-sm text-gray-600">
                {{ task.dueDate ? __dateHumanizeText(task.dueDate) : '-'}}
              </span>
            </td>
            <td class=" whitespace-nowrap px-4 py-1">
              <div class="inline-flex items-center px-3 py-1 bg-[#DEDEDE] rounded-lg max-w-xs text-xs">
                <span class="truncate">
                  {{ task.project.name }}
                </span>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
      <!-- End Table -->
    </div>
  </div>
  <!-- Footer -->
  <div v-if="!isFetching && tasks.length" class="mt-5 flex flex-wrap justify-between items-center gap-2">
    <p class="text-sm ml-4">
      <span class="font-medium text-stone-800 mr-1">{{ total }}</span>
      <span class="font-medium text-stone-800">Results</span>
    </p>
    <!-- Pagination -->
    <nav class="flex justify-end items-center gap-x-1" aria-label="Pagination">
          <button type="button" v-if="page > 1" @click="prevPage()"
              class="py-2 inline-flex justify-center items-center gap-x-2 text-sm rounded-mdtext-stone-800 hover:bg-stone-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none"
              aria-label="Previous">
              <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                  viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                  stroke-linejoin="round">
                  <path d="m15 18-6-6 6-6"></path>
              </svg>
              <span class="sr-only">Previous</span>
          </button>
          <div class="flex items-center gap-x-1 mr-2 ml-2">
              <span
                  class="h-[26px] w-[26px] flex justify-center items-center bg-gray-300 py-2 text-sm rounded-md disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-700"
                  aria-current="page">{{ page }}</span>
              <span
                  class="min-h-[38px] flex justify-center items-center text-stone-500 py-2 px-1.5 text-sm dark:text-neutral-500">of</span>
              <span
                  class="min-h-[38px] flex justify-center items-center text-stone-500 py-2 px-1.5 text-sm dark:text-neutral-500">{{ maxPage }}</span>
          </div>
          <button type="button" v-if="page < maxPage" @click="nextPage()"
              class="py-2 inline-flex justify-center items-center gap-x-2 text-sm rounded-mdtext-stone-800 hover:bg-stone-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none"
              aria-label="Next">
              <span class="sr-only">Next</span>
              <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                  viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                  stroke-linejoin="round">
                  <path d="m9 18 6-6-6-6"></path>
              </svg>
          </button>
      </nav>
    <!-- End Pagination -->
  </div>
  <div v-if="!isFetching && !tasks.length" class="font-bold text-center w-[300px] top-0 bottom-0 my-auto mx-auto right-0 left-0 absolute h-10 w-10 z-50">
    {{ $t('No records found') }}
  </div>
  <!-- End Table Section -->
  <ModalContainer />
</template>

<script>
import { delay } from '@/libraries/helper';
import { mapGetters, mapActions } from 'vuex';
import tasksApi from "@/api/tasks";

import {
    ExternalLinkIcon,
    XIcon,
    LinkIcon,
    DotsHorizontalIcon,
    ThumbUpIcon,
    CheckCircleIcon,
    CalendarIcon
  } from '@heroicons/vue/outline';
/* eslint-disable vue/html-closing-bracket-spacing */
export default {
	components: {
    CheckCircleIcon,
	},
	props: {
	},
	data() {
		return {
      isOverDue: false,
      isUpcoming: true,
      tasks: [],
      orderBy: 'createdAt',
      sortBy: 'asc',
      page: 1,
      total: 0,
      maxPage: 1,
      limit: 10,
      isFetching: false,
      taskSlug: null,
		};
	},
	computed: {
    ...mapGetters({
			user: 'auth/user',
		}),
  },
	watch: {
    isUpcoming() {
      this.getTasks()
    },
    '$route.params.slug': function(newRoute) {
      if (!newRoute || newRoute.trim() === '') {
        this.taskSlug = ''
      }
    }
  },
	created() {},
	mounted() {
    this.getTasks()
    if (this.$route.params.slug) {
      this.showDetailTask(atob(this.$route.params.slug));
    }
  },
	beforeUnmount() {},
	methods: {
    ...mapActions({
      showDetailTask: 'application/showDetailTask',
      resetStore: 'application/resetStore',
    }),
    onInputSearch() {
      delay(() => {
        this.getTasks()
      }, 1000);
    },
    handleAvatarError(itemWithError) {
      // Find the index of the item in the items array
      const index = this.items.findIndex(item => item.email === itemWithError.email);
      // If the item is found, update its avatarError property
      if (index !== -1) {
        const item = this.items[index];
        item.imgUrl = null;
        Object.assign(this.items[index], item);
      }
    },
    async getTasks() {
      this.isFetching = true;
      const callback = (response) => {
        const data = response.data;
        const meta = response.meta;
        this.tasks = data;
        this.meta = meta;
        this.page = meta.currentPage;
        this.maxPage = meta.lastPage;
        this.total = meta.total;
        this.isFetching = false;
      };
      const errCallback = (err) => {
        console.log(err);
        this.isFetching = false;
      };
      const params = {
        orderBy: this.orderBy,
        sortBy: this.sortBy,
        page: this.page,
        limit: this.limit,
      }
      if (this.keyword) {
        params.keyword = this.keyword
      } 
      tasksApi.getList(params, callback, errCallback);
    },
    showTask(task) {
      this.showDetailTask(task.slug);
      this.$router.push(`/tasks/${btoa(task?.project?.id)}/${btoa(task.slug)}`) // Combine base URL and current route
    },
    updateComplete(task) {
        const callback = (response) => {
          const data = response.data;
          const index = this.tasks.findIndex(item => item.id === data.id);
          if (index !== -1) {
            // Replace the old item with the updated one
            Object.assign(this.tasks[index], data);
          } else {
            console.log('Item not found');
          }
        };
        let params = {
          status: !task.status || task.status === 'incomplete' ? 'complete' : 'incomplete'
        }

        // Make the API call to update the task on the server
        tasksApi.compeleteTask(task.id, params, callback, (error) => {
          console.error('Error updating task:', error);
        });
      },
      goToDetail(task) {
        if (this.taskSlug === task.slug) {
          return;
        }
        this.taskSlug = task.slug
        this.resetStore()
        const baseURL = import.meta.env.VITE_APP_URL; // Get the base URL from environment variables
        this.$router.push(`/tasks/${btoa(task?.project?.id)}/${btoa(task.slug)}`) // Combine base URL and current route
        setTimeout(() => {
          this.showDetailTask(task.slug);
        }, 500);
    }
  },
};
</script>