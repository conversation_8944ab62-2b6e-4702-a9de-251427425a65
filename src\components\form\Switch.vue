<template>
  <div class="flex items-center">
    <div class="relative">
      <!-- Visually hidden checkbox -->
      <input
        :id="label"
        aria-describedby="checkbox-description"
        type="checkbox"
        class="sr-only"
        :checked="value"
        :disabled="disabled"
        @change="toggle"
      />
      <!-- Custom switch background -->
      <div
        class="block w-10 h-6 rounded-full cursor-pointer"
        :class="{
          'bg-gray-300': disabled,
          'bg-primary-500': !disabled && value,
          'bg-gray-200': !disabled && !value
        }"
        @click="toggle"
      ></div>
      <!-- Custom switch toggle dot -->
      <div
        class="dot absolute left-1 top-1 w-4 h-4 rounded-full transition transform"
        :class="{
          'bg-white': !disabled,
          'bg-gray-500': disabled,
          'translate-x-full': value
        }"
      >
        <CheckIcon @click="toggle" v-if="value" class="h-4 w-4 pointer text-primary-600" aria-hidden="true"></CheckIcon>
        <XIcon @click="toggle" v-if="!value" class="h-3 w-3 pointer text-gray-300 m-[2px]" aria-hidden="true"></XIcon>
      </div>
    </div>
    <div class="ml-3 text-sm">
      <label
        v-if="label"
        :for="label"
        class="font-medium text-gray-700 cursor-pointer"
        :class="{'text-gray-300': disabled}"
        @click="toggle"
      >{{ label }}</label>
      <p
        v-if="subLabel"
        id="checkbox-description"
        class="text-gray-500"
        :class="{'text-gray-300': disabled}"
      >
        {{ subLabel }}
      </p>
    </div>
  </div>
</template>

<script>
import {
  CheckIcon,
  XIcon,
} from '@heroicons/vue/solid';
export default {
  name: "ToggleSwitch",
  components: {
    CheckIcon,
    XIcon,
	},
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    label: {
      type: String,
      default: '',
    },
    subLabel: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    toggle() {
      if (!this.disabled) {
        this.$emit('update:modelValue', !this.value);
        this.$emit('change', !this.value);
      }
    },
  },
};
</script>

<style scoped>
.block {
  display: inline-block;
  position: relative;
  transition: background-color 0.2s;
}

.dot {
  position: absolute;
  top: 0.25rem;
  left: 0.25rem;
  transition: transform 0.2s;
}

input[type="checkbox"].sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}
</style>
