import { createApp, defineCustomElement } from 'vue';
import TButton from '@/components/global/Button.vue';
import TInput from '@/components/form/Input.vue';
import ButtonDropdown from '@/components/global/ButtonDropdown.vue';
import {
	Menu,
	MenuButton,
	MenuItem,
	MenuItems,
} from '@headlessui/vue';
import '@/tailwind.css';
import App from './App.vue';
import PrimeVue from 'primevue/config';
import Aura from '@primevue/themes/aura';
import io from 'socket.io-client';


// initializeApp(firebaseConfig);
const app = createApp(App);
app.config.warnHandler =  (msg, vm, trace) => {
	return null;
};

app.config.globalProperties.$soketio = io(import.meta.env.VITE_API_URL);


// Store
import store from "@/store/index";
app.use(store);

// Routes
import routes from '@/router.js';
const router = routes.createRouter(store);
app.use(router);

// Add global mixin
import globalMixin from '@/mixins/global.js';
app.mixin(globalMixin);

// I18n
import i18n from "@/libraries/i18n.js";
app.use(i18n);

// Notifications
import Notifications from '@kyvg/vue3-notification';
import velocity from 'velocity-animate';
app.use(Notifications, { velocity });

// Fontawesome
import { library } from '@fortawesome/fontawesome-svg-core';
import { faBold, faUnderline, faItalic, faImage, faAlignCenter, faAlignLeft, faAlignRight, faAlignJustify, faHighlighter, faStrikethrough } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
library.add(faBold, faUnderline, faItalic, faImage, faAlignCenter, faAlignLeft, faAlignRight, faAlignJustify, faHighlighter, faStrikethrough);
app.component('FontAwesomeIcon', FontAwesomeIcon);

// component
import LoaderCircle from "@/components/loader/LoaderCircle.vue";
app.component('LoaderCircle', LoaderCircle);

// code mirror
import Codemirror from "codemirror-editor-vue3";
app.use(Codemirror);

// tooltip
import VTooltip from 'v-tooltip';
app.use(VTooltip);

// masonry
import {VueMasonryPlugin} from 'vue-masonry';
app.use(VueMasonryPlugin);

// meta
import { createMetaManager } from 'vue-meta';
app.use(createMetaManager());

//primevue
app.use(PrimeVue, {
    theme: {
        preset: Aura,
		options: {
            darkModeSelector: '.my-app-dark',
        }
    }
});

import { CkeditorPlugin } from '@ckeditor/ckeditor5-vue';
app.use(CkeditorPlugin);


// Mount
app.component('TButton', TButton)
app.component('TInput', TInput)
app.component('ButtonDropdown', ButtonDropdown)
app.component('Menu', Menu)
app.component('MenuItems', MenuItems)
app.component('MenuButton', MenuButton)
app.component('MenuItem', MenuItem)
app.mount('#app');
