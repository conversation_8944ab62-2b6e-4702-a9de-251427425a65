<template>
    <div class="max-w-lg mx-auto  p-6 bg-white rounded-lg ">
        <h2 class="text-xl font-semibold mb-4">Password</h2>

        <!-- Current Password -->
        <div class="mb-4">
            <label for="current-password" class="block text-sm font-medium text-gray-700 mb-2">
                Current password <span class="text-red-500">*</span>
            </label>
            <input type="password" id="current-password" v-model="form.currentPassword"
                class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                required />
        </div>

        <!-- New Password -->
        <div class="mb-4">
            <label for="new-password" class="block text-sm font-medium text-gray-700 mb-2">
                New password <span class="text-red-500">*</span>
            </label>
            <input type="password" id="new-password" v-model="form.newPassword"
                class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                required />
            <p class="text-sm text-gray-500 mt-1">Create a password with at least 8 characters.</p>
        </div>

        <!-- Confirm New Password -->
        <div class="mb-6">
            <label for="confirm-password" class="block text-sm font-medium text-gray-700 mb-2">
                Confirm new password <span class="text-red-500">*</span>
            </label>
            <input type="password" id="confirm-password" v-model="form.confirmPassword"
                class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                required />
        </div>

        <!-- Buttons -->
        <div class="flex justify-between">
            <button type="button"
                class="inline-flex justify-center py-2 px-4 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-300"
                @click="onCancel">
                Cancel
            </button>
            <button type="button"
                class="inline-flex justify-center py-2 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                @click="onSaveChanges">
                Save Changes
            </button>
        </div>
    </div>
</template>

<script>
    import authApi from '@/api/auth'
    export default {
        data() {
            return {
                form: {
                    currentPassword: '',
                    newPassword: '',
                    confirmPassword: ''
                }
            };
        },
        methods: {
            onCancel() {
                // Clear form or navigate away, depending on your logic
                this.form.currentPassword = '';
                this.form.newPassword = '';
                this.form.confirmPassword = '';
            },
            onSaveChanges() {
                // Handle form submission, such as password validation and API call
                if (this.form.newPassword !== this.form.confirmPassword) {
                    alert('New passwords do not match.');
                    return;
                }
                // Further logic like sending the data to an API...
                const callback = (response) => {
                    console.log(response.data)
                    this.onCancel()
                    const message = response.message;
                    this.__showNotif('success', 'Success', message);
                }
                const errCallback = (err) => {
                    console.log(err)
                }

                const params = {
                    oldPassword: this.form.currentPassword,
                    newPassword: this.form.newPassword,
                    confirmPassword: this.form.confirmPassword
                }
                authApi.changePassword(params, callback, errCallback)
            }
        }
    };
</script>

<style scoped>
    /* Customize styling if necessary */
</style>