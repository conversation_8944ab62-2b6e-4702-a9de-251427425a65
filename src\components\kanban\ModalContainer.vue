<template>
  <section
    v-if="$store.state.application.isModal || $store.state.application.mutate"
    class="fixed inset-0 z-50 pointer-events-none"
  >
    <!-- Dimmed Background -->
    <div 
      v-if="isFull" 
      class="absolute inset-0 bg-black opacity-50 pointer-events-auto z-40">
    </div>

    <!-- Drawer -->
    <div
      class="fixed right-0 top-0 h-full overflow-y-auto overflow-x-hidden bg-white transform transition-transform duration-300 ease-in-out pointer-events-auto border-l z-50"
      :class="[drawerClass, drawerWidthClass]"
      :style="drawerStyle"
    >
      <!-- Content -->
      <TaskShow v-if="$store.state.application.task.show" />
      <AddEditBoardOrTask
        v-if="
          $route.name !== 'TaskAlone' && $route.name !== 'TaskAloneFull' && $route.name !== 'TaskDetail' && $route.name !== 'TaskDetailFull' && (
          $store.state.application.board.add ||
          $store.state.application.board.edit ||
          $store.state.application.task.add ||
          $store.state.application.task.edit)
        "
      />
      <AddEditBoardOrTaskAlone
        v-if="
          ($route.name === 'TaskAlone' || $route.name === 'TaskAloneFull' || $route.name === 'TaskDetailFull' || $route.name === 'TaskDetail') && (
          $store.state.application.board.add ||
          $store.state.application.board.edit ||
          $store.state.application.task.add ||
          $store.state.application.task.edit)
        "
      />
      <!-- End Content -->
    </div>
  </section>
</template>


<script>
export default {
  data() {
    return {
      isFull: false
    };
  },
  computed: {
    drawerClass() {
      return this.$store.state.application.mutate ? "translate-x-0" : "translate-x-full";
    },
    drawerWidthClass() {
      return "w-full sm:w-auto";
    },
    drawerStyle() {
      if (this.isFull) {
        return {
          width: "100%" // Adjust for 60px padding on each side
        };
      }
      return null;
    }
  },
  watch: {
    '$route.name': function(newRoute) {
      this.isFull = newRoute === 'TaskDetailKanbanFull' || newRoute === 'TaskDetailFull' || newRoute === 'TaskAloneFull'; // Check for full-screen mode
    }
  },
  mounted() {
    this.isFull = this.$route.name === 'TaskDetailKanbanFull' || this.$route.name === 'TaskDetailFull' || this.$route.name === 'TaskAloneFull'; // Set initially
  }
};

</script>

<style scoped>
/* Ensure smooth sliding transition */
.fixed {
  transition: transform 0.3s ease-in-out;
}

.absolute {
  position: absolute;
}

.bg-black {
  background-color: black;
}

.opacity-50 {
  opacity: 0.5; /* Adjust for the amount of dimming */
}

.inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.z-40 {
  z-index: 40; /* Ensure the overlay is below the drawer but above the rest of the content */
}

.z-50 {
  z-index: 50; /* Ensure the drawer is on top of the overlay */
}

</style>
