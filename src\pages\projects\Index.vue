<template>
  <!-- Loader -->
  <loader-circle v-if="isFetchingProject" />
  <!-- Users Table Card -->
  <div class="p-5 space-y-4 flex flex-col rounded-sm">
    <!-- Filter Group -->
    <div class="grid md:grid-cols-2 gap-y-2 md:gap-y-0 md:gap-x-5">
      <div class="font-bold">{{ $t('Projects') }}</div>
      <div class="flex justify-end items-center gap-x-2">
        <div class="relative">
          <div class="absolute inset-y-0 start-0 flex items-center pointer-events-none z-20 ps-3.5">
            <svg class="shrink-0 size-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
              viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
              stroke-linejoin="round">
              <circle cx="11" cy="11" r="8" />
              <path d="m21 21-4.3-4.3" /></svg>
          </div>
          <form autocomplete="off">
            <input type="text" name="search" v-model="keyword" v-value="keyword" @input="onInputSearch"
              autocomplete="off"
              class="py-[7px] ps-10 pe-8 block w-full bg-white border-gray-200 rounded-md text-sm focus:bg-white focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none"
              placeholder="Search">
          </form>
        </div>

        <!-- Filter Dropdown -->
        <ButtonDropdown :customClassItems="'ml-[-100px] mt-2'">
          <template #button>
            <t-button id="hs-pro-dptfd" :color="`primary-white`" class="px-5 py-2" aria-haspopup="menu"
              aria-expanded="false" aria-label="Dropdown">
              <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                stroke-linejoin="round">
                <line x1="21" x2="14" y1="4" y2="4" />
                <line x1="10" x2="3" y1="4" y2="4" />
                <line x1="21" x2="12" y1="12" y2="12" />
                <line x1="8" x2="3" y1="12" y2="12" />
                <line x1="21" x2="16" y1="20" y2="20" />
                <line x1="12" x2="3" y1="20" y2="20" />
                <line x1="14" x2="14" y1="2" y2="6" />
                <line x1="8" x2="8" y1="10" y2="14" />
                <line x1="16" x2="16" y1="18" y2="22" /></svg>
              {{ $t('Filter') }}
            </t-button>
          </template>

          <template #items>
            <MenuItem as="div" class="mb-2">
            <t-checkbox @click.stop="" v-model="selectedStatuses.pre" :label="$t('Pre')" :value="selectedStatuses.pre" />
            </MenuItem>
            <MenuItem as="div" class="mb-2">
            <t-checkbox @click.stop="" v-model="selectedStatuses.active" :label="$t('Active')" :value="selectedStatuses.active" />
            </MenuItem>
            <MenuItem as="div" class="mb-2">
            <t-checkbox @click.stop="" v-model="selectedStatuses.hold" :label="$t('Hold')" :value="selectedStatuses.hold" />
            </MenuItem>
            <MenuItem as="div" class="mb-2">
            <t-checkbox @click.stop="" v-model="selectedStatuses.completed" :label="$t('Complete')" :value="selectedStatuses.completed" />
            </MenuItem>
            <MenuItem as="div" class="mb-2">
            <t-checkbox @click.stop="" v-model="selectedStatuses.waiting_for_client" :label="$t('Waiting for Client')" :value="selectedStatuses.waiting_for_client" />
            </MenuItem>
            <MenuItem as="div" class="mb-2">
            <t-checkbox @click.stop="" v-model="is_archived" :label="$t('Archived')" :value="is_archived" />
            </MenuItem>

          </template>
        </ButtonDropdown>
        <!-- <t-button :color="`primary-white`" data-hs-overlay="#drawer-right-add" class="ml-2 py-2">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class=" ml-2 mr-1 size-5">
            <path fill-rule="evenodd"
              d="M6.97 2.47a.75.75 0 0 1 1.06 0l4.5 4.5a.75.75 0 0 1-1.06 1.06L8.25 4.81V16.5a.75.75 0 0 1-1.5 0V4.81L3.53 8.03a.75.75 0 0 1-1.06-1.06l4.5-4.5Zm9.53 4.28a.75.75 0 0 1 .75.75v11.69l3.22-3.22a.75.75 0 1 1 1.06 1.06l-4.5 4.5a.75.75 0 0 1-1.06 0l-4.5-4.5a.75.75 0 1 1 1.06-1.06l3.22 3.22V7.5a.75.75 0 0 1 .75-.75Z"
              clip-rule="evenodd" />
          </svg>
          <span class="text-sm pr-4">Import/Export</span>
        </t-button> -->
      </div>
    </div>

    <div v-if="!isFetchingProject && items.length" >
      <!-- Tab Content -->
      <div id="hs-pro-tabs-dut-all" role="tabpanel" aria-labelledby="hs-pro-tabs-dut-item-all">
        <!-- Table Section -->
        <div
          class="overflow-x-auto [&::-webkit-scrollbar]:h-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300">
          <div class="min-w-full inline-block align-middle">
            <!-- Table -->
            <table class="min-w-full">
              <thead>
                <tr class="">
                  <th scope="col" class="pl-3 py-2.5 text-start">
                  </th>

                  <th scope="col" class="min-w-80">
                    <!-- Sort Dropdown -->
                    <div class="hs-dropdown relative inline-flex w-full cursor-pointer">
                      <button id="hs-pro-dutnms" type="button"
                        class="px-1 py-2.5 text-start w-full flex items-center gap-x-1 text-sm text-nowrap font-normal text-gray-500 focus:outline-none focus:bg-gray-100"
                        aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                        Event
                      </button>
                    </div>
                  </th>

                  <th scope="col" class="min-w-80">
                    <!-- Sort Dropdown -->
                    <div class="hs-dropdown relative inline-flex w-full cursor-pointer">
                      <button id="hs-pro-dutads" type="button"
                        class="px-5 py-2.5 text-start w-full flex items-center gap-x-1 text-sm text-nowrap font-normal text-gray-500 focus:outline-none focus:bg-gray-100"
                        aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                        Client
                      </button>
                    </div>
                  </th>

                  <th scope="col" class="min-w-40">
                    <!-- Sort Dropdown -->
                    <div class="hs-dropdown relative inline-flex w-full cursor-pointer">
                      <ButtonDropdown>
                        <template #button>
                          <button id="hs-pro-dutsgs" type="button"
                            class="px-5 py-2.5 text-start w-full flex items-center gap-x-1 text-sm text-nowrap font-normal text-gray-500 focus:outline-none focus:bg-gray-100"
                            aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                            Status
                            <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                              viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                              stroke-linecap="round" stroke-linejoin="round">
                              <path d="m7 15 5 5 5-5" />
                              <path d="m7 9 5-5 5 5" /></svg>
                          </button>
                        </template>

                        <template #items>
                          <MenuItem @click="deletetask()" v-slot="{ active }">
                          <button type="button" @click="sortAsc()"
                            class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                            <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                              viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                              stroke-linecap="round" stroke-linejoin="round">
                              <path d="m5 12 7-7 7 7" />
                              <path d="M12 19V5" /></svg>
                            Sort ascending
                          </button>
                          </MenuItem>
                          <MenuItem @click="deletetask()" v-slot="{ active }">
                          <button type="button" @click="sortDsc()"
                            class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100">
                            <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                              viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                              stroke-linecap="round" stroke-linejoin="round">
                              <path d="M12 5v14" />
                              <path d="m19 12-7 7-7-7" /></svg>
                            Sort descending
                          </button>
                          </MenuItem>
                        </template>
                      </ButtonDropdown>
                    </div>
                  </th>

                  <th scope="col" class="min-w-40">
                    <!-- Sort Dropdown -->
                    <div class="hs-dropdown relative inline-flex w-full cursor-pointer">
                      <button id="hs-pro-dutems" type="button"
                        class="px-5 py-2.5 text-start w-full flex justify-left items-center gap-x-1 text-sm text-nowrap font-normal text-gray-500 focus:outline-none focus:bg-gray-100"
                        aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                        Progress
                      </button>
                    </div>
                    <!-- End Sort Dropdown -->
                  </th>
                </tr>
              </thead>

              <tbody>
                <tr class="border-b hover:bg-slate-100 border-gray-200 group" v-for="(item, index) in items" :key="item.id" @dblclick="gotoDetail(item)">
                  <td class="whitespace-nowrap px-2 py-4 w-[50px]">
                    {{ (page- 1) * 10 + (index + 1) }}
                  </td>
                  <td class="pr-2 py-1 relative cursor-pointer">
                    <div class="w-full flex justify-between items-center min-w-[330px]">
                      <div class="flex items-center gap-x-3">
                        <img v-if="item && item.imgUrl" class="rounded-full size-9 object-cover" :src="item.imgUrl"
                          alt="avatar-image" referrerpolicy="no-referrer" @error="handleAvatarError(item)">
                        <div v-else>
                          <div :style="{ backgroundColor: __getColorByInitial(item.name[0]) }"
                            class="font-bold text-sm rounded-full h-9 w-9 pt-[8px] text-center text-black uppercase">
                            {{ __generateInitial(item.name) }}
                          </div>
                        </div>
                        <div>
                          <div class="text-sm font-medium text-gray-800 max-w-[250px] truncate">
                            {{ item.name }}
                          </div>
                          <div class="text-xs text-gray-400">{{ __dateFormatProject(item.startDate) }}</div>
                        </div>
                      </div>

                      <div class="hidden group-hover:flex">
                        <ButtonDropdown>
                          <template #button>
                            <button type="button"
                              class="w-[30px] h-[26px] text-center justify-center flex items-center text-sm text-nowrap font-normal text-gray-800 shadow outline-gray-200 border border-gray-200 rounded focus:bg-gray-100">
                              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                stroke="currentColor" class="size-5">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                  d="M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z" />
                              </svg>
                            </button>
                          </template>

                          <template #items>
                            <MenuItem as="div">
                              <div @click="gotoDetail(item)"
                                class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100">
                                View Detail
                              </div>
                            </MenuItem>
                            <MenuItem as="div">
                              <div @click="editProject(item)"
                                class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100">
                                Rename
                              </div>
                            </MenuItem>
                            <MenuItem v-if="item?.status === 'active' && !item?.meta?.isStarred" as="div">
                              <div @click="starProject(item)"
                                class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100">
                                Starred
                              </div>
                              </MenuItem>
                              <MenuItem v-if="item?.status === 'active' && item?.meta?.isStarred" as="div">
                              <div @click="removeStarred(item)"
                                class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100">
                                Unstarred
                              </div>
                              </MenuItem>

                            <!-- Set Status Parent -->
                            <MenuItem v-if="isAdmin || (isClient && item.status !== 'completed')" as="div" @mouseover="showSubmenu = true" @mouseleave="showSubmenu = false">
                              <div class="w-full flex justify-between items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800">
                                <div>Set Status</div>
                                <div>
                                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
                                  </svg>
                                </div>
                              </div>
                              <!-- Submenu (Children) for Completed and Hold -->
                              <transition name="fade">
                                <div v-if="showSubmenu" class="absolute top-[60px] left-full bg-white shadow-lg rounded-md z-50 w-48 p-2">
                                  <div v-if="item.status !== 'completed'" :class="{'disabled': item.status === 'completed'}" @click="setStatus(item, 'completed')"
                                    class="w-full justify-between flex items-center gap-x-3 py-1 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100">
                                    <div>Completed</div>
                                  </div>
                                  <div v-if="item.status !== 'hold'" :class="{'disabled': item.status === 'hold'}" @click="setStatus(item, 'hold')"
                                    class="w-full flex justify-between items-center gap-x-3 py-1 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100">
                                    <div>Hold</div>
                                  </div>
                                  <div v-if="isAdmin && item.status !== 'pre'" :class="{'disabled': item.status === 'pre'}" @click="setStatus(item, 'pre')"
                                    class="w-full flex justify-between items-center gap-x-3 py-1 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100">
                                    <div>Pre</div>
                                  </div>
                                  <div v-if="isAdmin && item.status !== 'active'" :class="{'disabled': item.status === 'active'}" @click="setStatus(item, 'active')"
                                    class="w-full flex justify-between items-center gap-x-3 py-1 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100">
                                    <div>Active</div>
                                  </div>
                                  <div v-if="isAdmin && item.status !== 'active_revision'" :class="{'disabled': item.status === 'active_revision'}" @click="setStatus(item, 'active_revision')"
                                    class="w-full flex justify-between items-center gap-x-3 py-1 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100">
                                    <div>Active Revision</div>
                                  </div>
                                  <div v-if="isAdmin && item.status !== 'review'" :class="{'disabled': item.status === 'review'}" @click="setStatus(item, 'review')"
                                    class="w-full flex justify-between items-center gap-x-3 py-1 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100">
                                    <div>Review</div>
                                  </div>
                                </div>
                              </transition>
                            </MenuItem>

                            <MenuItem as="div">
                              <div @click="viewQuotation(item)"
                                class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100">
                                View Quotation
                              </div>
                            </MenuItem>
                            <MenuItem as="div">
                              <div @click="acrhive(item)"
                                class="border-t-[1px]  w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100">
                                {{ item.isArchived ? 'Unarchive' : 'Archive' }}
                              </div>
                            </MenuItem>
                            <MenuItem v-if="isAdmin" as="div" data-hs-overlay="#confirm-delete">
                              <div @click="deleteProject(item)"
                                class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-sm text-[13px] font-normal text-gray-800 hover:bg-gray-100">
                                Delete
                              </div>
                            </MenuItem>
                          </template>
                        </ButtonDropdown>

                      </div>
                    </div>

                  </td>
                  <td class="whitespace-nowrap px-6 py-1">
                    <span class="text-sm text-gray-600">
                      {{ item?.user?.fullName }}
                    </span>
                    <div class="text-xs text-gray-400">{{ item?.user?.jobTitle?.name }}</div>
                  </td>
                  <td class="whitespace-nowrap px-4 py-1">
                    <span class="py-1 pb-[6px] px-4 inline-flex items-center text-xs font-medium text-white rounded-md"
                      :class="{'bg-yellow-800': item.status === 'waiting_for_client', 'bg-[#14b8a6]': item.status === 'active', 'bg-teal-400': item.status === 'active_revision', 'bg-[#F05b5b]': item.status === 'hold', 'bg-[#FAB312]': item.status === 'pre','bg-[#43e5fb]': item.status === 'review', 'bg-[#2563EB]': item.status === 'completed'}">
                      <span v-if="item.status !== 'active_revision'"> {{ item.status === 'waiting_for_client' ? 'Waiting For Client': item?.status[0].toUpperCase() + item?.status.slice(1).toLowerCase()}} </span>
                      <span v-if="item.status === 'active_revision'"> {{ item.status === 'active_revision' ? 'Active Revision': item?.status[0].toUpperCase() + item?.status.slice(1).toLowerCase()}} </span>

                    </span>
                  </td>
                  <td class="whitespace-nowrap px-6 py-1">
                    <span v-if="item.status !== 'completed'" class="text-sm text-gray-600">
                      {{item.meta ? `${item.meta.completedTaskPercentage}%` : '0%' }}
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
            <!-- End Table -->
          </div>
        </div>
        <!-- End Table Section -->

        <!-- Footer -->
        <div class="mt-5 flex flex-wrap justify-between items-center gap-2">
          <p class="text-sm ml-4">
            <span class="font-medium text-stone-800 mr-1">{{ total }}</span>
            <span class="font-medium text-stone-800">Results</span>
          </p>
          <!-- Pagination -->
          <nav class="flex justify-end items-center gap-x-1" aria-label="Pagination">
            <button type="button" v-if="page > 1" @click="prevPage()"
              class="py-2 inline-flex justify-center items-center gap-x-2 text-sm rounded-mdtext-stone-800 hover:bg-stone-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none"
              aria-label="Previous">
              <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                stroke-linejoin="round">
                <path d="m15 18-6-6 6-6"></path>
              </svg>
              <span class="sr-only">Previous</span>
            </button>
            <div class="flex items-center gap-x-1 mr-2 ml-2">
              <span
                class="h-[26px] w-[26px] flex justify-center items-center bg-gray-300 py-2 text-sm rounded-md disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-700"
                aria-current="page">{{ page }}</span>
              <span
                class="min-h-[38px] flex justify-center items-center text-stone-500 py-2 px-1.5 text-sm dark:text-neutral-500">of</span>
              <span
                class="min-h-[38px] flex justify-center items-center text-stone-500 py-2 px-1.5 text-sm dark:text-neutral-500">{{ maxPage }}</span>
            </div>
            <button type="button" v-if="page < maxPage" @click="nextPage()"
              class="py-2 inline-flex justify-center items-center gap-x-2 text-sm rounded-mdtext-stone-800 hover:bg-stone-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none"
              aria-label="Next">
              <span class="sr-only">Next</span>
              <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                stroke-linejoin="round">
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </button>
          </nav>
          <!-- End Pagination -->
        </div>
        <!-- End Footer -->
      </div>
      <!-- End Tab Content -->
    </div>
    <!-- modal delete -->
    <ModalGeneral :isShow="isModalDeleteOpen" @update:isShow="isModalDeleteOpen = $event">
      <template #header>
        <p class="text-base font-medium text-gray-800 dark:text-white">
          {{$t('Delete Project')}}
        </p>
      </template>
      <template #body>
        <!-- <div class="text-xs text-gray-800 dark:text-neutral-400"> -->
        <p class="text-sm text-gray-800 dark:text-neutral-400"> {{ `${$t('Are you sure want to delete')} ${selectedItem.name}?`}} </p>
        <!-- </div> -->
      </template>
      <template #footer>
        <t-button :color="`secondary-solid`" @click="isModalDeleteOpen = false;">
          {{ $t('Cancel') }}
        </t-button>
        <t-button :color="`red-solid`" :isDisabled="deleteCountdown > 0" @click="confirmDelete()">
          {{ $t('Delete') }} {{ deleteCountdown > 0 ? `(${deleteCountdown}s)` : '' }}
        </t-button>
      </template>
    </ModalGeneral>
    <!-- modal rename -->
    <ModalGeneral :isShow="isModalEditOpen" @update:isShow="isModalEditOpen = $event">
      <template #header>
        <p class="text-base font-medium text-gray-800 dark:text-white">
          {{$t('Rename Project')}}
        </p>
      </template>
      <template #body>
        <!-- <div class="text-xs text-gray-800 dark:text-neutral-400"> -->
        <t-input v-model="name" :value="name" :type="`text-input`" placeholder="Type in new name"> </t-input>
        <!-- </div> -->
      </template>
      <template #footer>
        <t-button :color="`secondary-solid`" @click="isModalEditOpen = false;">
          {{ $t('Cancel') }}
        </t-button>
        <t-button :isDisabled="!name" :color="`primary-solid`" @click="confirmEdit()">
          {{ $t('Rename') }}
        </t-button>
      </template>
    </ModalGeneral>
    <!-- modal client waiting approval -->
    <ModalGeneral :isShow="isShowModalClientPre" @update:isShow="isShowModalClientPre = $event">
      <template #header>
        <p class="text-base font-medium text-gray-800 dark:text-white">
          {{$t('Project Details')}}
        </p>
      </template>
      <template #body>
        <!-- <div class="text-xs text-gray-800 dark:text-neutral-400"> -->
        <p class="text-sm text-gray-800 dark:text-neutral-400"> {{$t('Admin still reviewing the quotation for this event')}} </p>
        <!-- </div> -->
      </template>
      <template #footer>
        <t-button class="px-6" :color="`primary-solid`" @click="isShowModalClientPre = false;">
          {{ $t('Ok') }}
        </t-button>
      </template>
    </ModalGeneral>

  </div>
  <div v-if="!isFetchingProject && !items.length" class="font-bold text-center w-[300px] top-0 bottom-0 my-auto mx-auto right-0 left-0 absolute h-10 w-10 z-50">
    {{ $t('No records found') }}
  </div>
</template>

<script>
  import TInput from '@/components/form/Input.vue';
  import TTextarea from '@/components/form/Textarea.vue';
  import 'vue-multiselect/dist/vue-multiselect.css';
  import VueMultiselect from 'vue-multiselect';
  import projectApi from "@/api/project";
  import rolesApi from "@/api/roles";
  import jobsApi from "@/api/jobs";
  import ModalGeneral from "@/components/modal/ModalGeneral.vue";
  import fileApi from '@/api/files';
  import Confirmation from "@/components/modal/Confirmation.vue";
  import TCheckbox from '@/components/form/Checkbox.vue';

  import {
    BriefcaseIcon,
    DownloadIcon,
    UserGroupIcon,
    XIcon,
    PlusIcon,
    TrashIcon,
    UserCircleIcon,
  } from '@heroicons/vue/solid';

  import {
    mapGetters,
    mapActions
  } from 'vuex';
  import TModal from '@/components/global/Modal.vue';
  import TButton from '@/components/global/Button.vue';
  import {
    HSStaticMethods
  } from "preline";
  export default {
    components: {
      BriefcaseIcon,
      DownloadIcon,
      UserGroupIcon,
      XIcon,
      TModal,
      PlusIcon,
      TrashIcon,
      UserCircleIcon,
      TInput,
      TTextarea,
      VueMultiselect,
      ModalGeneral,
      TButton,
      TCheckbox,
      Confirmation,
    },
    data() {
      return {
        orderBy: 'createdAt',
        sortBy: 'desc',
        page: 1,
        total: 0,
        maxPage: 1,
        limit: 10,
        selectedItem: null,
        itemId: null,
        items: [],
        meta: null,
        isEdit: false,
        keyword: "",
        colors: ['#13ac9a', '#ac5a13', '#aca013', '#1342ac', '#13ac2b', '#9a13ac'],
        selectedColor: null,
        isDropdownOpen: false,
        isModalDeleteOpen: false,
        isModalEditOpen: false,
        name: '',
        selectedStatuses: {
          pre: false,
          active: false,
          hold: false,
          completed: false,
          waiting_for_client: false,
        },
        is_archived: false,
        isShowModalClientPre: false,
        deleteCountdown: 5,
        deleteInterval: null,
        isFetchingProject: false,
      }
    },
    watch: {
      selectedStatuses: {
        handler(newVal, oldVal) {
          console.log('selectedStatuses changed:', newVal); // Debugging
          this.page = 1;
          this.fetchData();
        },
        deep: true, // Deep watcher to monitor all changes within the object
      },
      is_archived: {
        handler(newVal, oldVal) {
          this.page = 1;
          this.fetchData();
        },
      },
    },
    computed: {
      ...mapGetters({
        getToken: 'auth/getToken',
        user: 'auth/user',
        isFetchingUser: 'auth/isFetchingUser',
        isAdmin: 'auth/isAdmin',
        isClient: 'auth/isClient',
        getActiveProject: 'application/getActiveProject',
      }),

    },
    created() {
      this.fetchData();
      this.$soketio.on('project_update', (data) => {
        if (this.project.id === data.id) this.setDataProject(data); 
			});
    },
    mounted() {},
    methods: {
      openDropdown() {
        this.isDropdownOpen = true;
      },
      closeDropdown() {
        this.isDropdownOpen = false;
        this.$refs.dropdown.closeMenu();
      },
      ...mapActions({
        fetchUser: 'auth/fetchUser',
        resetStore: 'application/resetStore',
        setDatastarredProject: 'application/setDatastarredProject',
        setDataProject: 'application/setDataProject',
      }),
      sortAsc() {
        this.sortBy = "asc"
        this.orderBy = "status"
        this.fetchData()
      },
      sortDsc() {
        this.orderBy = "status"
        this.sortBy = "desc"
        this.fetchData()
      },
      editProject(item) {
        this.selectedItem = item
        this.name = this.__duplicateVar(item.name)
        this.isModalEditOpen = true;
        // const roomId = this.selectedItem.slug;
        // this.$soketio.emit('join', roomId);
      },
      deleteProject(item) {
        this.deleteCountdown = 5;
        clearInterval(this.deleteInterval);
        this.startDeleteCountdown();

        this.isModalDeleteOpen = true;
        this.selectedItem = item
      },
      startDeleteCountdown() {
        this.deleteInterval = setInterval(() => {
          if (this.deleteCountdown > 0) {
            this.deleteCountdown--;
          } else {
            clearInterval(this.deleteInterval);
          }
        }, 1000);
      },
      acrhive(item) {
        this.isSaving = true;
        const callback = (response) => {
          const data = response.data;
          const message = response.message;
          this.fetchData();
          this.__showNotif('success', 'Success', message);
          this.isSaving = false;
        }
        const errCallback = (err) => {
          const message = err.response.data.message;
          this.__showNotif('error', 'Error', message || 'Error User');
          this.handleErrors(err.response.data)
          this.isSaving = false;
        }
        const params = {
          isArchived: item.isArchived ? false : true,
        }
        if (item?.id) projectApi.update(item.id, params, callback, errCallback)
      },
      viewQuotation(item) {
        this.resetStore()
        
        if ((item.status === 'pre' || item.status === 'review') && !this.isAdmin) this.isShowModalClientPre = true
        else this.$router.push({ name: 'EventQuotation', params: { id: item.id } });
      },
      gotoDetail(item) {
        this.resetStore()
        
        
        if (item.status === 'pre' && !this.isAdmin) this.isShowModalClientPre = true
        if (item.status === 'review' && !this.isAdmin) this.isShowModalClientPre = true
        
        if (item.status === 'waiting_for_client' && this.isClient) this.$router.push({
          name: 'EventQuotationClient',
          params: {
            id: item.id
          }
        });

        if (item.status === 'active' || item.status === 'completed') this.$router.push({
          name: 'EventKanban',
          params: {
            id: this.__encryptProjectData(item.id, item.slug)
          }
        });

        if (item.status === 'active_revision') this.$router.push({
          name: 'EventKanban',
          params: {
            id: this.__encryptProjectData(item.id, item.slug)
          }
        });

        if (item.status === 'waiting_for_client' && !this.isClient) this.$router.push({
          name: 'EventQuotation',
          params: {
            id: item.id
          }
        });

        if (item.status === 'hold' && this.isClient) this.$router.push({
          name: 'EventQuotationClient',
          params: {
            id: item.id
          }
        });
        else if ((item.status === 'pre' || item.status === 'review') && !this.isClient) this.$router.push({ name: 'EventQuotation', params: { id: item.id } });
        
        if (item.status === 'hold' && !this.isClient) this.$router.push({ name: 'EventQuotation', params: { id: item.id } });

      },
      assignRandomColors() {
        this.items = this.items.map(item => {
          // Generate a random index based on the colors array length
          const randomIndex = Math.floor(Math.random() * this.colors.length);
          // Assign a random color to each item
          return {
            ...item,
            color: this.colors[randomIndex],
          };
        });
      },
      onInputSearch() {
        this.debounce(this.fetchData(this.keyword), 300); // 300ms debounce
      },
      nextPage() {
        this.page = this.page + 1;
        this.fetchData()
        setTimeout(() => {
          HSStaticMethods.autoInit();
        }, 500);
      },
      prevPage() {
        this.page = this.page - 1;
        this.fetchData()
        setTimeout(() => {
          HSStaticMethods.autoInit();
        }, 500);
      },
      debounce(func, wait) {
        let timeout;
        return function (...args) {
          clearTimeout(timeout);
          timeout = setTimeout(() => {
            func.apply(this, args);
          }, wait);
        };
      },
      fetchData(keyword = null) {
        this.isFetchingProject = true
        const callback = (response) => {
          const data = response.data;
          const meta = response.meta;
          this.items = data.filter(item => item.username !== "superadmin");
          this.meta = meta;
          this.page = meta.currentPage;
          this.maxPage = meta.lastPage;
          this.total = meta.total;
          this.assignRandomColors();
          this.isFetchingProject = false
          setTimeout(() => {
              HSStaticMethods.autoInit();
          }, 500);

        }

        const errCallback = (err) => {
          console.log(err);
          this.isFetchingProject = false
        }

        // Collecting all selected statuses where the value is true
        const selectedStatuses = Object.keys(this.selectedStatuses)
          .filter(status => this.selectedStatuses[status]) // Filter by true values
          .join(','); // Join the selected statuses with commas

        const params = {
          orderBy: this.orderBy,
          sortBy: this.sortBy,
          page: this.page,
          limit: this.limit,
        }

        if (keyword) {
          params.keyword = keyword;
        }

        if (selectedStatuses) {
          params.status = selectedStatuses; // Adding the selected statuses to params
        }
        params.isArchived = this.is_archived ? 1 : 0; // Set isArchived based on 'archived' status

        projectApi.getList(params, callback, errCallback);
      },
      handleAvatarError(itemWithError) {
        // Find the index of the item in the items array
        const index = this.items.findIndex(item => item.email === itemWithError.email);
        // If the item is found, update its avatarError property
        if (index !== -1) {
          const item = this.items[index];
          item.imgUrl = null;
          Object.assign(this.items[index], item);
        }
      },
      confirmDelete() {
        if (this.selectedItem) {
          const callback = (response) => {
            const data = response.data;
            const message = response.message;
            this.total = this.total - 1;
            this.selectedItem = null;
            this.fetchData();
            this.isModalDeleteOpen = false;
            this.__showNotif('success', 'Success', message);
          }
          const errCallback = (err) => {
            console.log(err)
          }
          const id = this.selectedItem.id;
          projectApi.delete(id, callback, errCallback)
        }
      },
      setStatus(item, status) {
        this.isSaving = true;
        const callback = (response) => {
          const data = response.data;
          const message = response.message;
          this.fetchData();
          this.__showNotif('success', 'Success', message);
          this.isSaving = false;
        }
        const errCallback = (err) => {
          const message = err.response.data.message;
          this.__showNotif('error', 'Error', message || 'Error User');
          this.handleErrors(err.response.data)
          this.isSaving = false;
        }
        const params = {
          status,
        }
        if (item?.id) projectApi.update(item.id, params, callback, errCallback)
      },
      confirmEdit() {
        this.isSaving = true;
        const callback = (response) => {
          const data = response.data;
          const message = response.message;
          this.fetchData();
          this.__showNotif('success', 'Success', message);
          this.isSaving = false;
          this.isModalEditOpen = false;
          this.name = ''
        }
        const errCallback = (err) => {
          const message = err.response.data.message;
          this.__showNotif('error', 'Error', message || 'Error User');
          this.handleErrors(err.response.data)
          this.isSaving = false;
          this.isModalEditOpen = false;
          this.name = ''
        }
        const params = {
          name: this.name
        }
        if (this.selectedItem?.id) projectApi.update(this.selectedItem.id, params, callback, errCallback)
      },
      starProject(item) {
        this.isSaving = true;
        const callback = (response) => {
          const data = response.data;
          const message = response.message;
          this.fetchData();
          this.__showNotif('success', 'Success', message);
          this.isSaving = false;
          this.getProjects()
        }
        const errCallback = (err) => {
          const message = err.response.data.message;
          this.__showNotif('error', 'Error', message || 'Error User');
          this.handleErrors(err.response.data)
          this.isSaving = false;
        }
        projectApi.starred(item.id, callback, errCallback)
      },
      removeStarred(item) {
        this.isSaving = true;
        const callback = (response) => {
          const data = response.data;
          const message = response.message;
          this.fetchData();
          this.__showNotif('success', 'Success', message);
          this.isSaving = false;
          this.getProjects()
        }
        const errCallback = (err) => {
          const message = err.response.data.message;
          this.__showNotif('error', 'Error', message || 'Error User');
          this.handleErrors(err.response.data)
          this.isSaving = false;
        }
        projectApi.removeStarred(item.id, callback, errCallback)
      },
      getProjects() {
				const callback = (response) => {
					const data = response.data;
          this.setDatastarredProject(data)
				}
				const errCallback = (err) => {
					console.log(err)
				}

				const params = {
					orderBy: 'createdAt',
					sortBy: 'desc',
					page: 1,
					limit: 100,
				}
				projectApi.getListStarred(params, callback, errCallback)
			},
      resetModel() {
        this.model = {
          fullName: null,
          phone: null,
          email: null,
          username: null,
          password: null,
          shortBio: null,
          location: null,
          company: null,
          language: null,
          jobTitleId: null,
          roleId: null,
          isClient: null,
          isInternal: null,
          isActive: null,
          status: null,
          imgUrl: null,
          rating: null,
          userExpertise: null,
        }
      },
    },
  };
</script>