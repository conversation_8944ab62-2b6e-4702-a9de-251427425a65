<template>
  <div class="ml-5 mr-2 mt-2 flex flex-row items-center">
    <div class="flex justify-between items-center w-full">
      <div class="flex items-center">
        <ChatAltIcon class="h-6 w-6 mr-2 text-gray-700 pointer" aria-hidden="true"></ChatAltIcon>
        <div class="text-xs">{{ $t('Activity') }}</div>
      </div>
      <div class="flex">
        <div class="pt-1 text-xs text-gray-400 mr-2">{{ $t('Only show comments') }}</div>
        <div><t-switch v-model="isComment" :value="isComment" /></div>
      </div>
    </div>
  </div>
  <!-- assignee task -->
  <div v-if="!isComment" class="mt-2 text-gray-400 text-xs ml-6 mr-6">
    <div class="flex items-center ">
      <img
        v-if="workData?.creator && workData?.creator?.imgUrl"
        class="rounded-full w-[20px] h-[20px] object-cover mr-2"
        :src="workData?.creator?.imgUrl"
        alt="avatar-image"
        referrerpolicy="no-referrer"
      >
      <div :style="{ backgroundColor: workData?.creator?.fullName[0] ? __getColorByInitial(workData?.creator?.fullName[0]) : '' }" v-else class="text-[8px] text-black mr-2 rounded-full w-[22px] h-[22px] pt-[2px] font-medium text-center uppercase border-[1px]">
        {{ __generateInitial(workData?.creator?.fullName) }} 
      </div>
      <div class="text-xs text-gray-700 font-bold">{{workData?.creator?.fullName}} {{ $t('Created this task') }} · {{__dateHumanizeText(workData?.createdAt)}}</div>
    </div>
  </div>

  <!-- comment -->
  <div class="text-primary-600 text-xs ml-14 pl-1 mt-4 pointer" @click.prevent="toggleComments" v-if="comments.length > initialLimit && !showMore">{{ comments.length - initialLimit }} more comments</div>
  <div ref="commentContainer" class="min-h-[300px] text-gray-400 text-xs mx-6 comment-container mt-2">
    <div class="mb-3" v-for="comment in displayedComments" :key="comment.id">
      <div class="flex items-center">
        <img
            v-if="comment?.user && comment?.user?.imgUrl"
            class="rounded-full w-[26px] h-[26px] object-cover mr-2"
            :src="comment?.user?.imgUrl"
            alt="avatar-image"
            referrerpolicy="no-referrer"
        >
        <div :style="{ backgroundColor: workData?.creator?.fullName[0] ? __getColorByInitial(workData?.creator?.fullName[0]) : '' }" v-else class="text-xs mr-2 rounded-full h-7 w-7 font-medium pt-[4px] text-center text-black uppercase border-2">
          {{ __generateInitial(comment?.user?.fullName) }} 
        </div>
        <div class="text-xs text-gray-400">{{ comment?.user?.fullName }} · {{__dateHumanizeText(comment?.createdAt)}}</div>
      </div>
      <div v-if="!comment.attachment" class="text-xs ml-9" v-html="__urlify(comment?.description)" :class="{'text-primary-600 underline': comment?.description.includes('http'), 'text-gray-700 ': !comment?.description.includes('http')}"
      </div>
      <!-- show file -->
      <div v-if="comment.attachment" class="text-xs text-gray-700 ml-9">
        <div class="border-[1px] border-gray-300 p-2 flex items-center rounded-md w-[280px] h-[46px] lg:mr-2 mb-2 justify-between">
          <PaperClipIcon class="h-6 w-6 mr-2 text-gray-200" aria-hidden="true"></PaperClipIcon>
          <div>
            <div class="text-[12px] w-[200px] truncate">{{ comment?.attachment?.name }}</div>
            <div class="text-[12px] w-[200px] truncate text-gray-400">{{ getFileExtension(comment?.attachment?.fileUrl) }}</div>
          </div>
          <DownloadIcon class="h-6 w-6 mr-2 pointer" @click="downloadFile(comment?.attachment?.fileUrl)" aria-hidden="true"></DownloadIcon>
        </div>
      </div>
    </div>
    <!-- completed task -->
    <div class="text-green-600 flex items-center" v-if="workData?.completed">
      <CheckCircleIcon class="h-6 w-6 pointer text-green-600 mr-2" aria-hidden="true"
      :class="{'text-green-600': task?.status === 'completed' || workData?.status === 'completed'}"></CheckCircleIcon>
      {{ workData?.completed?.fullName }} {{ $t('marked this task complete') }}
    </div>
  </div>
</template>

<script>
import {
  mapGetters,
  mapActions
} from 'vuex';
import { toRaw } from 'vue';
import {
    PaperClipIcon,
    DownloadIcon,
  } from '@heroicons/vue/outline';
  import {
    CheckCircleIcon
  } from '@heroicons/vue/solid';
import store from "../../../pages/kanban/store.js";
import TButton from '@/components/global/Button.vue';
import TSwitch from '@/components/form/Switch.vue';
import commentsApi from "@/api/comments";
import kbInput from "@/components/kanban/kbInput.vue";

import {
  ChatAltIcon,
} from '@heroicons/vue/outline';

export default {
  components: {
    TButton,
    ChatAltIcon,
    TSwitch,
    kbInput,
    PaperClipIcon,
    DownloadIcon,
    CheckCircleIcon
  },
  data() {
    return {
      isComment: false,
      comments: [],
      comment: '',
      isCommenting: false,
      initialLimit: 3,
      showMore: false,
    };
  },
  props: {
    workDataSubtask: {
    },
  },
  computed: {
    ...mapGetters({
      getOnlineUsers: 'application/getOnlineUsers',
      getActiveProject: 'application/getActiveProject',
      getActiveColumn: 'application/getActiveColumn',
      getActiveTask: 'application/getActiveTask',
      getBoardColsLength: 'application/getBoardColsLength',
    }),
    displayedComments() {
      if (this.showMore) {
        // Show all comments sorted from oldest to newest
        return this.sortedComments;
      } else {
        // Show the last 3 comments (newest first)
        return this.sortedComments.slice(-this.initialLimit);
      }
    },
    sortedComments() {
      // Sort comments from oldest to newest
      return this.comments
    },
  },
  mounted() {
    this.workData = JSON.parse(JSON.stringify(toRaw(this.getActiveTask))) || this.workDataSubtask;
    this.getComments()
  },
  created() {
    this.$soketio.on('task_update', (task) => {
      const taskComments = task.comments;
      if (this.workData.id === task.id) {
        // Assuming that each comment has a unique 'id' property
        const uniqueComments = taskComments.filter(
          (newComment) => !this.comments.some((existingComment) => existingComment.id === newComment.id)
        );

        // Add only unique comments to this.comments
        this.comments = [...this.comments, ...uniqueComments];
      }
      this.workData.completed = task.completed
    });
  },
  methods: {
    ...mapActions({
      clearOnlineUsers: 'application/clearOnlineUsers',
      setData: 'application/setData',
      changeStatus: 'application/changeStatus',
      updateTaskInStore: 'application/updateTaskInStore',
    }),
    reinitComments(comments) {
      // if (comments.length > 0) {
        this.comments = comments
      // }
    },
    toggleComments() {
    this.showMore = !this.showMore;
    // Scroll to the top when showing more comments
    if (this.showMore) {
      this.$nextTick(() => {
        const commentSection = this.$refs.commentContainer
        setTimeout(() => {
          commentSection.scrollTop = commentSection.scrollHeight;
        }, 500);
      });
    }
  },
    getComments() {
      const callback = (response) => {
        const data = response.data;
        this.comments = data;
      };
      const errCallback = (err) => {
        const message = err?.response?.data?.message;
        this.__showNotif('error', 'Error', message);
      };
      let params = {
        orderBy: 'createdAt',
        sortBy: 'asc',
        taskId: this.workData?.id,
        limit: 9999,
      };
      commentsApi.getList(params, callback, errCallback);
    },
    createComments(comment, attachment) {
      const callback = (response) => {
        const data = response.data;
        this.comment = '';
        if (this.$route.name == 'TaskDetail') { this.comments.push(data) }
      };
      const errCallback = (err) => {
        const message = err?.response?.data?.message;
        this.__showNotif('error', 'Error', message);
      };
      let params = {
        description: comment,
        taskId: this.workData.id,
        attachmentId: attachment,
      };
      commentsApi.create(params, callback, errCallback);
    },
    scrollToOlderComments() {
      const commentSection = this.$el.querySelector('.comment-container');
      setTimeout(() => {
        commentSection.scrollTop = commentSection.scrollHeight;
      }, 1000);
    },
    getFileName(url) {
      // Split the URL by '/' and return the last part
      return url.split('/').pop();
    },
    downloadFile(url) {
      // Create a new anchor element
      const link = document.createElement("a");

      // Set the href to the file URL
      link.href = url;

      // Set the target to '_blank' to open in a new tab
      link.target = "_blank";

      // Set the download attribute (optional, use only if you want to prompt download)
      // link.download = this.fileUrl.split('/').pop(); // Uncomment if you want to force download

      // Append the link to the body (required for Firefox)
      document.body.appendChild(link);

      // Programmatically click the link to trigger the download or open in new tab
      link.click();

      // Remove the link from the document
      document.body.removeChild(link);
    },
    getFileExtension(url) {
      if (!url) return ''; // Return empty if no URL

      // Split the URL by '.' and get the last element
      const parts = url.split('.');
      const extension = parts.length > 1 ? parts.pop().toUpperCase() : ''; // Get extension and convert to uppercase

      // Append additional text based on the file type
      if (extension === 'JPG' || extension === 'JPEG' || extension === 'PNG' || extension === 'GIF') {
        return `${extension} (Image)`; // For image files
      } else if (extension) {
        return `${extension} (File)`; // For other file types
      }
      
      return ''; // Return empty if no extension found
    },
  },
};
</script>

<style scoped></style>
