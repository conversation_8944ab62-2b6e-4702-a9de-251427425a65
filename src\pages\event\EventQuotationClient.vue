<template>
  <loader-circle v-if="isFetchingQuotation" />
  <div v-show="!isFetchingQuotation" id="invoiceId" class="min-h-screen font-sans ">
    <!-- Header -->
      
    <!-- Main Content -->
    <main class="max-w-8xl mx-auto p-4 px-8">
      <div class="bg-white rounded-lg">
        <div class="bg-white rounded-lg px-12 pt-12">
        <div class="grid grid-cols-1 md:grid-cols-[1fr_auto] gap-4 items-center pb-4 mb-4">
          <!-- Content Area -->
          <!-- truncate responsive -->
          <div class="min-w-0"> <!-- min-w-0 ensures that the container will shrink -->
            <h1 class="text-xl font-bold min-h-8">
              {{ project?.name }}
            </h1>
            <p class="text-gray-600 truncate  min-h-8">
              {{ project?.packages?.studio ?? 'No Studio' }},
              {{ __dateFormatQuotation(project?.startDate, project?.duration) }}
              <span v-if="project?.booking?.invoiceNumber"> | {{ project?.booking?.invoiceNumber }}</span>
            </p>
          </div>

          <!-- Logo Area -->
          <div v-show="!isPrint" class="no-print items-center justify-center bg-white-200 border rounded-full w-20 h-20 text-2xl font-bold md:flex hidden">
            {{ __generateInitial(project?.user?.company) }}
          </div>
        </div>

          <div class="flex items-center mb-4">
            <div>
              <p class="text-gray-600">Quote Amount:</p>
              <p class="text-2xl font-bold">{{ __convertCurrency(printTotalPrice,'NOK') }} </p>
            </div>
            <div class="ml-16">
              <p class="text-gray-600">Invoice Recipient:</p>
              <p class="text-2xl font-bold">{{ project?.user?.fullName }} - {{ project?.user?.company }}</p>
            </div>
          </div>

          <div class="flex justify-between mb-2 items-center " v-if="(projectStatus === 'waiting_for_client') && (projectStatus !== 'active_revision') ">
            <div class="text-sm font-semibold">{{ $t('Item Requested Summary') }}</div>
            <div>
              <t-button v-show="!isPrint && isEditQuo" :color="`primary-white`" :isLoading="isContinue"
                class="no-print px-4 py-1 mr-2" @click="cancelEdit" >
                <XIcon v-if="isEditQuo" class="h-4 w-4 mr-1" aria-hidden="true"></XIcon>
                {{ $t('Cancel Edit') }}
              </t-button>
              <t-button v-if="!isPrint && project?.booking?.isEnableClientEditable" :color="`primary-white`" :isLoading="isContinue" class="no-print px-4 py-1 "
                @click="onStartEdit">
                <PencilAltIcon v-if="!isEditQuo" class="h-4 w-4 mr-1" aria-hidden="true"></PencilAltIcon>
                <XIcon v-if="isEditQuo" class="h-4 w-4 mr-1" aria-hidden="true"></XIcon>
                {{ !isEditQuo ? $t('Edit Quotation') : $t('Close Edit') }}
              </t-button>
            </div>
          </div>

          <div class="rounded-t-lg max-h-[500px] overflow-auto" :class="{'bg-[#F4F4F4]': !isEditQuo}">
            <div class="border rounded-md overflow-hidden relative">
              <div v-for="(item, index) in bookingItems" :key="index"
                class="flex justify-between items-center p-4 border-b min-h-[57px]" :class="{'mt-[-10px]': isDownloadPdf}">
                <div class="w-60">
                  <p class="text-xs font-semibold ">{{ item.name }} </p>
                </div>
                <div class="flex items-center w-40 justify-end">
                  <span class="text-gray-700 mr-1" v-if="!isEditQuo">{{ item.quantity }}</span> <span v-if="!isEditQuo"> pcs</span>
                  <div v-if="!isPrint && isEditQuo && item.price > 0" class="mb-4 flex-1 flex flex-col overflow-y-auto overflow-hidden max-w-[150px]
                      [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 
                      [&::-webkit-scrollbar-thumb]:bg-gray-300">
                          <div class="flex">
                              <!-- Input Number -->
                              <div class="mr-2 bg-white border border-gray-200 rounded-lg dark:bg-neutral-700 dark:border-neutral-700"
                                  data-hs-input-number="">
                                  <div class="w-full flex justify-between items-center gap-x-1">
                                      <div class="grow py-2 px-3">
                                          <input class="w-full p-0 bg-transparent border-0 text-gray-800 focus:ring-0 
                                  [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none
                                  dark:text-white" style="-moz-appearance: textfield;" type="number"
                                              aria-roledescription="Number field" data-hs-input-number-input=""
                                              v-model="item.quantity" :value="item.quantity" @input="onInputChange(item)">
                                      </div>
                                      <div
                                          class="flex items-center -gap-y-px divide-x divide-gray-200 border-s border-gray-200 dark:divide-neutral-700 dark:border-neutral-700">
                                          <button @click="decreaseCount(item)" type="button"
                                              class="size-10 inline-flex justify-center items-center gap-x-2 text-sm font-medium last:rounded-e-lg bg-white text-gray-800 hover:bg-gray-50 focus:outline-none focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:text-white dark:hover:bg-neutral-800 dark:focus:bg-neutral-800"
                                              aria-label="Decrease">
                                              <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24"
                                                  height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                  stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                  <path d="M5 12h14"></path>
                                              </svg>
                                          </button>
                                          <button @click="increaseCount(item)" type="button"
                                              class="size-10 inline-flex justify-center items-center gap-x-2 text-sm font-medium last:rounded-e-lg bg-white text-gray-800 hover:bg-gray-50 focus:outline-none focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:text-white dark:hover:bg-neutral-800 dark:focus:bg-neutral-800"
                                              aria-label="Increase">
                                              <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24"
                                                  height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                  stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                  <path d="M5 12h14"></path>
                                                  <path d="M12 5v14"></path>
                                              </svg>
                                          </button>
                                      </div>
                                  </div>
                              </div>
                              <!-- End Input Number -->
                          </div>
                  </div>
                </div>
                <div class="w-20 text-right">
                  <span class="text-gray-700 mr-1" >{{ item.price }}</span> 
                </div>
              </div>
            </div>
          </div>
          <!-- Total Amount -->
          <div class="flex justify-between items-center p-4 bg-gray-200 rounded-b-lg">
            <div>Total Amount before tax :</div>
            <div class="text-xl font-bold">{{ __convertCurrency(printTotalPrice,'NOK') }}</div>
          </div>
          
          <div class="flex justify-between items-center mt-6 no-print" v-if="!isPrint">
            <div class="flex space-x-2">
              <t-button @click="downloadPdf()" :color="`primary-white`" :isLoading="isContinue" class="px-4 py-2">
                <DocumentIcon class="h-4 w-4 mr-1" aria-hidden="true"></DocumentIcon>
                PDF
              </t-button>
              <t-button @click="printPreview()" :color="`primary-white`" :isLoading="isContinue" class="px-4 py-2">
                <PrinterIcon class="h-4 w-4 mr-1" aria-hidden="true"></PrinterIcon>
                {{ $t('Print Quotation') }}
              </t-button>
              <t-button :color="`primary-white`" @click="onEmailAdmin" :isLoading="isContinue" class="px-4 py-2" data-hs-overlay="#confirm">
                <MailIcon class="h-4 w-4 mr-1" aria-hidden="true"></MailIcon>
                {{ $t('Email Admin') }} 
              </t-button>
            </div>
            <div>
              <span class="text-gray-500 font-semibold" v-if="projectStatus !== 'waiting_for_client' && projectStatus !== 'active' && projectStatus !== 'active_revision'"> {{ $t('Waiting response from admin') }} </span> 
              <span class="text-gray-500 font-semibold" v-if="projectStatus === 'active'"> {{ $t('Project is already active') }} </span>
              <div v-if="projectStatus === 'waiting_for_client' || projectStatus === 'active_revision'">
                <div v-if="status === 'approve' && projectStatus !== 'active_revision'">
                  <t-button @click="openApproveModal" :color="`primary-solid`"
                  :is-disabled="projectStatus !== 'waiting_for_client' || projectStatus === 'active' || isEditQuo">
                    {{ projectStatus === 'active_revision' ? $t('Approve') : $t('Approve & Send Quotation') }}
                  </t-button>
                </div>
                <div v-if="status === 'approve' && projectStatus === 'active_revision'">
                  <t-button @click="openApproveModal" :color="`primary-solid`"
                  :is-disabled="isEditQuo">
                    {{ projectStatus === 'active_revision' ? $t('Approve') : $t('Approve & Send Quotation') }}
                  </t-button>
                </div>
                <div v-if="status === 'review' && projectStatus !== 'active_revision'">
                  <t-button @click="sendQuotation('review')" :color="`primary-solid`"
                  :is-disabled="projectStatus !== 'waiting_for_client' || projectStatus === 'active' || isEditQuo">
                    {{ $t('Send Edited Quotation For Review') }}
                  </t-button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="border-t-[1px] border-gray-200 mt-6">
          <div class="px-12 text-sm text-gray-600 text-left py-4 font-medium">
            {{ $t('Project will be started when quotation is approved, please respond immediately to book the venue and personnel, for more questions and information') }}
            <a href="#" class="text-blue-500">click here</a>
          </div>
        </div>
      </div>
    </main>

    <iframe ref="printFrame" style="display: none;"></iframe>
  </div>
    <Confirmation :id="`confirm`">
            <template #header>
                <p class="text-base font-bold text-gray-800 dark:text-white">
                    Email: {{ subject }}
                </p>
            </template>
            <template #body>
                <!-- <div class="text-xs text-gray-800 dark:text-neutral-400"> -->
                  <div
                    class="h-full flex-1 flex flex-col overflow-y-auto overflow-hidden [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300">
                    <!-- List Item -->
                    <div class="pb-4 grid grid-cols-1 gap-x-4 border-gray-200">
                        <label class="block text-sm font-semibold text-gray-700 mb-2">Subject</label>
                        <t-input :type="`text-input`" v-model="subject" :value="subject" placeholder="Type in email subject">
                        </t-input>
                    </div>
                    <!-- End List Item -->

                    <!-- List Item -->
                    <div class="pb-4 grid grid-cols-1 gap-x-4 border-gray-200">
                        <t-input :type="`area`" v-model="message" :value="message"
                            placeholder="Type your message here"> </t-input>
                    </div>
                    <!-- End List Item -->
                </div>
                <!-- </div> -->
            </template>
            <template #footer>
                <t-button :color="`primary-white`" class="px-4 py-2" @click="onSendEmail" data-hs-overlay="#confirm">
                  {{ $t('Send Email') }}
                </t-button>
            </template>
    </Confirmation>
    <Confirmation :id="`confirm-approve`">
        <template #header>
            <p class="text-base font-bold text-gray-800 dark:text-white">Approve Quotation</p>
        </template>
        <template #body>
            <!-- <div class="text-xs text-gray-800 dark:text-neutral-400"> -->
            <p class="text-sm text-gray-800 dark:text-neutral-400">
                You are going to approve this quotation, proceed?
            </p>
            <!-- </div> -->
        </template>
        <template #footer>
            <button type="button" data-hs-overlay="#confirm-approve"
                class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 focus:outline-none focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
                Cancel
            </button>
            <button type="button" @click="sendQuotation('approve')" data-hs-overlay="#confirm-approve"
                class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none">
                Proceed
            </button>
        </template>
    </Confirmation>
</template>

<script>
  import TButton from '@/components/global/Button.vue';
  import TSwitch from '@/components/form/Switch.vue';
  import TInput from '@/components/form/Input.vue';

  import projectApi from "@/api/project";
  import bookingApi from "@/api/booking";
  import productApi from "@/api/product";
  import serviceApi from "@/api/service";
  import {
    PencilAltIcon,
    DocumentIcon,
    PrinterIcon,
    XIcon,
    PlusIcon,
    MailIcon,
    UserCircleIcon 
  } from '@heroicons/vue/outline';
  import html2pdf from 'html2pdf.js';
  import Confirmation from "@/components/modal/Confirmation.vue";
  import { duplicateVar } from '@/libraries/helper';
  import { mapGetters } from 'vuex';
  import { HSOverlay } from 'preline';

  export default {
    components: {
      TSwitch,
      TInput,
      TButton,
      PencilAltIcon,
      DocumentIcon,
      PrinterIcon,
      XIcon,
      PlusIcon,
      MailIcon,
      UserCircleIcon,
      Confirmation
    },
    data() {
      return {
        project: null,
        isEditQuo: false,
        isDownloadPdf: false,
        totalPrice: 0,
        isPrint: false,
        isEnableClientEditable: false,
        selectedType: "product",
        selectedProduct: "",
        selectedService: "",
        quantity: 1,
        description: "",
        deliveryTime: 1,
        deliveryType: "hour",
        isSaving: false,
        drawerId: 'drawer-right',
        services: {},
        products: {},
        isEdit: false,
        selectedItem: {},
        subject: '',
        message: '',
        status: 'approve',
        previousBookingItems: null,
        previousTotalPrice: 0,
        isFetchingQuotation: false,
      };
    },
    mounted() {
      
    },
    computed: {
      ...mapGetters({
				isAdmin: 'auth/isAdmin',
				isClient: 'auth/isClient',
				isFreelancer: 'auth/isFreelancer',
			}),
      bookingItems() {
        return this.project?.booking?.items.filter(booking=> booking.price);
      },
      printTotalPrice() {
        return this.totalPrice;
      },
      projectStatus() {
        return this.project?.status;
      },
    },
    watch: {
      project: {
        handler(newVal, oldVal) {
          // Deeply compare the newVal and oldVal to see the changes
          let booking = newVal.booking
        },
        deep: true, // Enable deep watching
      },
    },
    created() {
      this.isPrint = this.$route.name === 'PrintEventQuotation'
      this.getProject();
      this.$soketio.on('project_quotation', (data) => {
        this.project = data.project;
        this.totalPrice = this.project?.booking?.totalPrice;
        this.status = 'approve'
        setTimeout(() => {
          HSStaticMethods.autoInit();
        }, 500);
        this.getProjectsStarred()
			});
    },
    methods: {
      cancelEdit() {
        // this.previousBookingItems = duplicateVar(this.bookingItems)
        this.project.booking.items = this.previousBookingItems;
        this.totalPrice = this.previousTotalPrice;
        this.isEditQuo = !this.isEditQuo;
      },
      onStartEdit() {
        this.previousBookingItems = duplicateVar(this.bookingItems)
        this.previousTotalPrice = duplicateVar(this.totalPrice)
        this.isEditQuo = !this.isEditQuo
      },
      openApproveModal(item) {
        HSOverlay.open('#confirm-approve');
      },
      onEmailAdmin() {
        this.subject = `Quotation - ${this.project?.packages?.name}`;
      },
      onSendEmail() {
        const params = {
          subject: this.subject,
          question: this.message,
        }
        bookingApi.sendEmail(params, (response)=>{
          this.__showNotif('success', 'Success', 'Your email has been sent!');
        },(err)=>{
          console.log(err)
        })
      },
      onAddItem() {
        this.getAllService()
        this.getAllProduct()
      },
      getAllService() {
        const callback = (response) => {
          const data = response.data;
          this.services = data;
        }
        const errCallback = (err) => {
          console.log(err)
        }

        const params = {
          orderBy: 'createdAt',
          sortBy: 'desc',
          page: 1,
          limit: 1000,
        }
        serviceApi.getList(params, callback, errCallback)
      },
      getAllProduct() {
        const callback = (response) => {
          const data = response.data;
          this.products = data;
        }
        const errCallback = (err) => {
          console.log(err)
        }

        const params = {
          orderBy: 'createdAt',
          sortBy: 'desc',
          page: 1,
          limit: 9999,
        }
        productApi.getList(params, callback, errCallback)
      },
      save() {
        this.isSaving = true;
        const payload = {
          selectedType: this.selectedType,
          productId: this.selectedProduct,
          serviceItemId: this.selectedService,
          quantity: this.quantity,
          description: this.description,
          projectId: this.project.id
        };
        const callback = (response) => {
          const data = response.data;
          const message = response.message;
          this.__showNotif('success', 'Success', message);
          this.project.booking.items.push(data);
          this.isSaving = false;
        }
        const errCallback = (err) => {
          console.log(err)
          this.isSaving = false;
        }
        bookingApi.create(payload, callback, errCallback)
        this.isSaving = false;
      },
      onOnTypeChange() {
        // Handle type change logic
      },
      printPreview() {
        // Create a hidden iframe
        const iframe = this.$refs.printFrame;
        const url =
          import.meta.env.VITE_APP_URL
        const urlToPrint = url + '/print/quotation/' + this.project.id // Replace with your URL

        // Set the iframe's src attribute to the URL
        iframe.src = urlToPrint;

        // Listen for the iframe to load, then trigger the print
        iframe.onload = () => {
          setTimeout(() => {
            iframe.contentWindow.focus();
            iframe.contentWindow.print();
          }, 1000);
        };
      },
      downloadPdf() {
        this.isDownloadPdf = true;
        setTimeout(() => {
          // Clone the content to avoid affecting the actual DOM
          const element = document.getElementById('invoiceId');
          const clonedElement = element.cloneNode(true);
  
          // Hide elements with the no-print class in the cloned element
          const elementsToHide = clonedElement.querySelectorAll('.no-print');
          elementsToHide.forEach(el => {
            el.style.display = 'none';
          });
  
          // Create a container to hold the cloned content
          const container = document.createElement('div');
          container.appendChild(clonedElement);
          const fileName = this.project?.booking?.invoiceNumber ? `Invoice-${this.project?.booking?.invoiceNumber}` : `Invoice`
          const opt = {
            margin: [0, 0, 0, 0],
            filename: fileName,
            image: {
              type: 'jpeg',
              quality: 1
            },
            html2canvas: {
              scale: 1,
            },
            jsPDF: {
              unit: 'mm',
              format: 'letter',
              orientation: 'portrait'
            },
            pagebreak: {
              mode: ['avoid-all', 'css', 'legacy']
            }
          };
  
          // Generate the PDF from the cloned content
          html2pdf().from(container).set(opt).save().then(() => {
            // Remove the cloned container after generating the PDF
            container.remove();
            this.isDownloadPdf = false;
          });
        }, 20);
      },
      sendQuotation(status) {
        const id = this.project.id;
        const slug = this.project.slug;
        const callback = (response) => {
          const data = response.data;
          this.project = data.project;
          const message = status === 'review' ?  `Quotation has been sent for review` : `Congrats Quotation has been approved`
          this.__showNotif('success', 'Success', message);
          const url = import.meta.env.VITE_APP_URL;
          if (status === 'approve') window.location.href = `${url}/e/kanban/${this.__encryptProjectData(id, slug)}`
          if (status === 'reject') window.location.href = `${url}/event`
        }
        const errCallback = (error) => {
          console.log(error)
          const data = error.response.data.data;
          if (data) {
            const names = data.map(item => item.name)
            const message = `${error.response.data.message} - ${names}`;
            this.__showNotif('error', 'Error', message);
          } else {
            const message = `${error.response.data.message}`;
            this.__showNotif('error', 'Error', message);
          }
          
        }
        const params = {
          customItems: this.project.booking.items.map(item => ({
            id: item.id,
            quantity: item.quantity,
            isEnabled: item.isEnabled ? 1 : 0, // Convert boolean to 0 or 1
          })),
        }
        if (this.projectStatus === "active_revision") params.isRevision = 1;
        projectApi.sendQuote(id, status, params, callback, errCallback)
      },
      onInputChange(item) {
        this.status = 'review';
        this.totalPrice = this.bookingItems.reduce((total, booking) => {
          return total + (booking.quantity * booking.price);
        }, 0);
        this.updateItemStatus(item);
      },
      increaseCount(item) {
        this.status = 'review';
        item.quantity++;
        this.totalPrice = this.totalPrice + item.price
        this.updateItemStatus(item);
      },
      decreaseCount(item) {
        this.status = 'review';
        if (item.quantity > 0) {
          item.quantity--;
          this.totalPrice = this.totalPrice - item.price
          this.updateItemStatus(item);
        }
      },
      updateItemStatus(item) {
        item.isEnabled = item.quantity > 0;
      },
      onAllowModify() {
        bookingApi.changeEditableQuotation(this.project.booking.id, (response) => {
          console.log(response.data)
        }, (err) => {
          console.log(err)
        })
        console.log(this.isEnableClientEditable)
      },
      onEnabledChange(item, index) {
        console.log(item);
        this.status = 'review';
        if (item.isEnabled) {
          this.totalPrice = this.totalPrice + (this.project.booking.items[index].quantity * this.project.booking.items[index].price)
        }
        if (!item.isEnabled) {
          this.totalPrice = this.totalPrice - (this.project.booking.items[index].quantity * this.project.booking.items[index].price)
        }
      },
      getProject() {
        this.isFetchingQuotation = true;
        const callback = (response) => {
          const data = response.data;
          this.project = data;
          const roomId = this.project.slug;
          this.$soketio.emit('join', roomId);
          this.totalPrice = this.project?.booking?.totalPrice;
          this.isEnableClientEditable = this.project.booking.isEnableClientEditable;
          // this.initializeItemStatus();
          this.isFetchingQuotation = false;

        };
        const errCallback = (err) => {
          const message = `${err?.response?.data?.message}`;
          this.__showNotif('error', 'Error', message);
          this.isFetchingQuotation = false;

        };
        projectApi.get(this.$route.params.id, callback, errCallback);
      },
      getProjectsStarred() {
				const callback = (response) => {
					const data = response.data;
					// this.setDatastarredProject(data)
				}
				const errCallback = (err) => {
					console.log(err)
				}

				const params = {
					orderBy: 'createdAt',
					sortBy: 'desc',
					page: 1,
					limit: 100,
				}
				projectApi.getListStarred(params, callback, errCallback)
			},
      initializeItemStatus() {
        this.project.booking.items.forEach(item => {
          this.updateItemStatus(item);
        });
      },
    },
  };
</script>