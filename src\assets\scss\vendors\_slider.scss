.vue-slider {
    height: 10px!important;
}

.vue-slider-process {
    background-color: #4f46e5!important;
}

.vue-slider-dot-tooltip-inner {
    min-width: 4px!important;
    max-width: 4px!important;
    padding: 0px 0px!important;
    border-color: #4f46e5!important;
    margin-top: -10px!important;
}

.vue-slider-dot-tooltip-inner-top {
    padding: 0!important;
    background-color: #4f46e5!important;
}

.custom-tooltip-slider .vue-slider-dot-tooltip-inner-top:after {
    margin-top: -10px!important;
}

.vue-slider-dot-tooltip-inner-bottom:after, .vue-slider-dot-tooltip-inner-top:after {
    width: 12px!important;
}


.vue-slider-dot-tooltip-inner-top:after { 
    border-width: 10px!important;
}

.custom-dot {
    width: 4px;
    height: 100%;
    background-color: #4f46e5;
    margin: 0 0 0 3px;
}