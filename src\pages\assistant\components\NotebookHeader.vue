<template>
  <header class="bg-white border-b border-gray-200 px-6 py-4">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <div class="flex items-center space-x-2">
          <button 
            @click="handleIconClick"
            class="hover:bg-gray-50 rounded transition-colors p-1"
          >
            <Logo />
          </button>
          
          <!-- Editable Title -->
          <input
            v-if="isEditing"
            v-model="editedTitle"
            @keydown="handleKeyDown"
            @blur="handleBlur"
            class="text-lg font-medium text-gray-900 border-none outline-none bg-transparent min-w-[300px] w-auto"
            ref="titleInput"
            :disabled="isUpdating"
          />
          <span 
            v-else
            @click="handleTitleClick"
            class="text-lg font-medium text-gray-900 cursor-pointer hover:bg-gray-50 rounded px-2 py-1 transition-colors"
          >
            {{ title }}
          </span>
        </div>
      </div>
      
      <div class="flex items-center space-x-4">
        <!-- User Menu -->
        <div class="relative" ref="userMenu">
          <button 
            @click="toggleUserMenu"
            class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center cursor-pointer hover:bg-purple-600 transition-colors"
          >
            <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </button>
          
          <!-- Dropdown Menu -->
          <div 
            v-if="showUserMenu"
            class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-50"
          >
            <div class="py-1">
              <button 
                @click="handleSignOut"
                class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
              >
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
                Sign Out
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script>
import Logo from './Logo.vue';

export default {
  name: 'NotebookHeader',
  components: {
    Logo,
  },
  props: {
    title: {
      type: String,
      required: true
    },
    notebookId: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      isEditing: false,
      editedTitle: this.title,
      isUpdating: false,
      showUserMenu: false
    };
  },
  watch: {
    title(newTitle) {
      this.editedTitle = newTitle;
    }
  },
  mounted() {
    document.addEventListener('click', this.handleClickOutside);
  },
  beforeUnmount() {
    document.removeEventListener('click', this.handleClickOutside);
  },
  methods: {
    handleTitleClick() {
      if (this.notebookId) {
        this.isEditing = true;
        this.editedTitle = this.title;
        this.$nextTick(() => {
          this.$refs.titleInput?.focus();
        });
      }
    },
    
    async handleTitleSubmit() {
      if (this.notebookId && this.editedTitle.trim() && this.editedTitle !== this.title) {
        this.isUpdating = true;
        try {
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 500));
          
          this.$emit('title-updated', this.editedTitle.trim());
          this.__showNotif('success', 'Success', 'Title updated successfully');
        } catch (error) {
          console.error('Failed to update title:', error);
          this.__showNotif('error', 'Error', 'Failed to update title');
          this.editedTitle = this.title; // Reset on error
        } finally {
          this.isUpdating = false;
        }
      }
      this.isEditing = false;
    },
    
    handleKeyDown(event) {
      if (event.key === 'Enter') {
        event.preventDefault();
        this.handleTitleSubmit();
      } else if (event.key === 'Escape') {
        this.editedTitle = this.title;
        this.isEditing = false;
      }
    },
    
    handleBlur() {
      this.handleTitleSubmit();
    },
    
    handleIconClick() {
      this.$emit('back-to-dashboard');
    },
    
    toggleUserMenu() {
      this.showUserMenu = !this.showUserMenu;
    },
    
    handleClickOutside(event) {
      if (this.$refs.userMenu && !this.$refs.userMenu.contains(event.target)) {
        this.showUserMenu = false;
      }
    },
    
    handleSignOut() {
      this.showUserMenu = false;
      this.$router.push('/logout');
    }
  }
};
</script>
