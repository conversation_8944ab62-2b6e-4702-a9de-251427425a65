<template>
    <div class="py-6 px-4 bg-gray-50 min-h-screen">
        <!-- Date and Welcome Text -->
        <div class="mb-6">
            <p class="text-sm text-gray-500">{{ __dateFormatHome() }}</p>
            <h1 class="text-2xl font-semibold text-gray-800">
                Good {{ __getGreet() }}, {{user?.fullName}}
            </h1>
        </div>

        <!-- Action Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <!-- Change Password Card -->
            <div @click="goto('changePassword')" class="flex bg-white border rounded-lg shadow-sm p-8 text-center cursor-pointer">
                <div class="flex justify-end items-center text-3xl my-2 m-auto">
                    <div class="mr-6">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                            stroke="currentColor" class="size-6">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M16.5 10.5V6.75a4.5 4.5 0 1 0-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H6.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z" />
                        </svg>
                    </div>
                    <div class="text-start">
                        <h2 class="text-lg font-semibold text-gray-800">Change Password</h2>
                        <p class="text-sm text-gray-500">Set your own chosen password</p>
                    </div>
                </div>
            </div>

            <!-- Select Your Expertise Card -->
            <div @click="goto('booking')" class="flex bg-white border rounded-lg shadow-sm p-8 text-center cursor-pointer">
                <div class="flex justify-end items-center text-3xl my-2 m-auto">
                    <div class="mr-6">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z" />
                        </svg>
                    </div>
                    <div class="text-start">
                        <h2 class="text-lg font-semibold text-gray-800">Book an event</h2>
                        <p class="text-sm text-gray-500">Select and book an event</p>
                    </div>
                </div>
            </div>

            <!-- Account Profile Card -->
            <div @click="goto('accountProfile')" class="flex bg-white border rounded-lg shadow-sm p-8 text-center cursor-pointer">
                <div class="flex justify-end items-center text-3xl my-2 m-auto">
                    <div class="mr-6">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                            stroke="currentColor" class="size-6">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z" />
                        </svg>
                    </div>
                    <div class="text-start">
                        <h2 class="text-lg font-semibold text-gray-800">Account Profile</h2>
                        <p class="text-sm text-gray-500">Modify your profile and other preferences</p>
                    </div>
                </div>
            </div>

            
        </div>
    </div>
</template>

<script>
    import { mapActions, mapGetters } from 'vuex';
    import authApi from '@/api/auth'
    export default {
        methods: {
            ...mapActions({
                fetchUser: 'auth/fetchUser',
                setUser: 'auth/setUser',
                
            }),
            updateIsActive() {
                if (!this.user?.isActive && this.user.role?.id === 6) {
                    this.updateUser()
                } else {
                    console.log("apakah apakah", this.user.isActive);
                }
            },
            updateUser() {
                const callback = (response) => {
                    this.setUser(response.data)
                }

                const errCallback = (err) => {
                    console.log(err)
                }
                const params = {
                    isActive: 1,
                    fullName: this.user.fullName,
                    phone: this.user.phone,
                    email: this.user.email,
                    username: this.user.username,
                }
                const id = this.user?.id
                authApi.update(params, callback, errCallback)
            },
            goto(target) {
                if (target === 'changePassword') this.$router.push('/settings/change-password')
                if (target === 'accountProfile') this.$router.push('/settings/profile')
                if (target === 'booking') this.$router.push('/event')
            }
        },
        mounted() {
            this.userName = this.user?.fullName;
            this.updateIsActive()
        },
        data() {
            return {
                userName: "Alexandra", // Assuming you fetch this from a store or API
                date: new Date(),
            };
        },
        computed: {
            ...mapGetters({
                user: 'auth/user',
                isClient: 'auth/isClient',
            }),
            formattedDate() {
                const options = {
                    weekday: "long",
                    day: "numeric",
                };
                return this.date.toLocaleDateString(undefined, options);
            },
        },
    };
</script>

<style scoped>
    /* You can customize some additional styling here if necessary */
</style>