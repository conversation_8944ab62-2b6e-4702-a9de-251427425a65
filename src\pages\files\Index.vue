<template>
  <loader-circle v-if="isFetching" />
  <div class="px-6">
    <div v-if="!selectedFile">
        <div class="flex justify-end m-3">
            <div class="flex">
                <div class="relative mr-2">
                    <input type="text" v-model="keyword" @input="onInputSearch"
                        class="py-[7px] ps-10 pe-8 block w-full bg-white border-gray-200 rounded-md text-sm focus:bg-white focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none"
                        placeholder="Search">
                    <div class="absolute inset-y-0 start-0 flex items-center pointer-events-none ps-4">
                        <svg class="shrink-0 size-4 text-gray-500 dark:text-neutral-500"
                            xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
                        </svg>
                    </div>
                </div>
                <div>
                    <t-button :color="`primary-solid`" class="h-[36px]" @click="addFile()">
                        <PlusIcon class="h-5 w-5 text-white" />
                        <span class="text-sm text-white">{{$t('Add File')}}</span>
                    </t-button>
                </div>
            </div>
        </div>
        <!-- List Section -->
        <div v-if="!isFetching && items.length">
            <div class="container mx-auto p-4">
                <div class="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4">
                    <!-- Card 1 -->
                    <div v-for="(item, index) in items" :key="item.id" class="bg-white rounded-lg shadow-md pointer" @contextmenu.prevent="openContextAsset(item, $event)">
                      <div class="flex items-start h-[90px] p-4" @click.stop="selectedFile = item">
                        <div class="mr-2 mt-[6px]">
                          <!-- Conditional icons based on file type -->
                          <div v-if="getFileType(item.fileUrl) === 'image'">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-image"><rect width="18" height="18" x="3" y="3" rx="2" ry="2"/><circle cx="9" cy="9" r="2"/><path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/></svg>
                          </div>
                          <div v-else-if="getFileType(item.fileUrl) === 'video'">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-play"><polygon points="6 3 20 12 6 21 6 3"/></svg>
                          </div>
                          <div v-else-if="getFileType(item.fileUrl) === 'document'">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-archive"><path d="M10 12v-1"/><path d="M10 18v-2"/><path d="M10 7V6"/><path d="M14 2v4a2 2 0 0 0 2 2h4"/><path d="M15.5 22H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v16a2 2 0 0 0 .274 1.01"/><circle cx="10" cy="20" r="2"/></svg>
                          </div>
                          <div v-else-if="getFileType(item.fileUrl) === 'music'">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-music"><path d="M9 18V5l12-2v13"/><circle cx="6" cy="18" r="3"/><circle cx="18" cy="16" r="3"/></svg>
                          </div>
                          <div v-else>
                            <!-- Default icon for unknown file types -->
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/><polyline points="14 2 14 8 20 8"/></svg>
                          </div>
                        </div>
                        <div class="font-medium overflow-hidden">
                          <div v-if="!item.name"class="text-[16px] truncate w-full max-w-full">{{ getFileName(item.fileUrl) }}</div>
                          <div v-else class="text-[16px] truncate w-full max-w-full">{{ `${item.name}` }}</div>
                          <div class="text-[12px]">{{ item?.user?.fullName }} </div>
                          <div v-if="item?.description" class="text-[12px]">{{ item?.description }} </div>
                        </div>
                      </div>
                      <div @click.stop="selectedFile = item" v-if="getFileType(item.fileUrl) === 'image'" class="h-[120px]">
                        <img :src="item.fileUrl" class="object-cover w-full h-[120px] rounded-b-md" alt="Image Preview" />
                      </div>
                      <Menu
                        as="div"
                        class="text-white"
                      >
                        <div>
                          <MenuButton
                            :id="`file-${item.id}`"
                            class="bg-transparent max-w-xs bg-white flex items-center text-xs rounded-full"
                          >
                            <span class="sr-only">Open asset menu</span>
                          </MenuButton>
                        </div>
                        <transition
                          enterActiveClass="transition ease-out duration-100" 
                          enterFromClass="transform opacity-0 scale-95" 
                          enterToClass="transform opacity-100 scale-100" 
                          leaveActiveClass="transition ease-in duration-75" 
                          leaveFromClass="transform opacity-100 scale-100" 
                          leaveToClass="transform opacity-0 scale-95"
                        >
                          <MenuItems
                            :style="posContextEdit"
                            class="mt-2 text-black w-[150px] max-h-[300px] rounded-sm shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-10"
                          >
                          <MenuItem
                              v-show="selectedFileContextId && user && user.id === selectedFileContextUserId || selectedFileContextId && isAdmin"
                              as="div"
                              class="hover:bg-gray-200"
                              @click.stop="renameFile(item)"
                            >
                              <div class="p-2 text-sm pointer">
                                {{ $t('Rename') }}
                              </div>
                            </MenuItem>
                            <MenuItem
                              as="div"
                              class="hover:bg-gray-200"
                              @click.stop="copyURL(item)"
                            >
                              <div class="p-2 text-sm pointer">
                                {{ $t('Copy Link') }}
                              </div>
                            </MenuItem>
                            <MenuItem
                              v-show="selectedFileContextId && user && user.id === selectedFileContextUserId || selectedFileContextId && isAdmin"
                              as="div"
                              class="hover:bg-gray-200 border-t-[1px] border-gray-200"
                              @click.stop="deleteFile()"
                            >
                              <div class="p-2 text-sm pointer">
                                {{ $t('Delete') }}
                              </div>
                            </MenuItem>
                          </MenuItems>
                        </transition>
                      </Menu>
                    </div>
                </div>
            </div>
        </div>
        <!-- End List Section -->
        <!-- Footer -->
        <div v-if="!isFetching && items.length" class="mt-5 flex flex-wrap justify-between items-center gap-2">
            <p class="text-sm ml-4">
                <span class="font-medium text-stone-800 mr-1">{{ total }}</span>
                <span class="font-medium text-stone-800">Results</span>
            </p>
            <!-- Pagination -->
            <nav class="flex justify-end items-center gap-x-1" aria-label="Pagination">
                <button type="button" v-if="page > 1" @click="prevPage()"
                    class="py-2 inline-flex justify-center items-center gap-x-2 text-sm rounded-md text-stone-800 hover:bg-stone-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none"
                    aria-label="Previous">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round">
                        <path d="m15 18-6-6 6-6"></path>
                    </svg>
                    <span class="sr-only">Previous</span>
                </button>
                <div class="flex items-center gap-x-1 mr-2 ml-2">
                    <span
                        class="h-[26px] w-[26px] flex justify-center items-center bg-gray-300 py-2 text-sm rounded-md disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-700"
                        aria-current="page">{{ page }}</span>
                    <span
                        class="min-h-[38px] flex justify-center items-center text-stone-500 py-2 px-1.5 text-sm dark:text-neutral-500">of</span>
                    <span
                        class="min-h-[38px] flex justify-center items-center text-stone-500 py-2 px-1.5 text-sm dark:text-neutral-500">{{ maxPage }}</span>
                </div>
                <button type="button" v-if="page < maxPage" @click="nextPage()"
                    class="py-2 inline-flex justify-center items-center gap-x-2 text-sm rounded-md text-stone-800 hover:bg-stone-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none"
                    aria-label="Next">
                    <span class="sr-only">Next</span>
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round">
                        <path d="m9 18 6-6-6-6"></path>
                    </svg>
                </button>
            </nav>
            <!-- End Pagination -->
        </div>
        <!-- End Footer -->
        <!-- End Footer -->
        <DrawerRight :id="`drawer-right`" ref="drawerRight">
            <template #header>
                <div class="">
                    <div class="flex mt-4 pb-2">
                        <p id="hs-pro-dutoo-label" class="text-sm font-semibold text-gray-800 ml-6 ">
                            Add Files
                        </p>
                    </div>
                </div>
            </template>
            <template #body>
              <div class="p-4">
                  <uploader
                    class="mt-2"
                    :type="'file'"
                    :preview="true"
                    :multiple="false"
                    @upload="upload"
                  />
                  <div class="hidden" v-if="fileUrl">
                    <div class="mr-2 mt-[6px] flex">
                      <!-- Conditional icons based on file type -->
                      <div v-if="getFileType(fileUrl) === 'image'">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-image"><rect width="18" height="18" x="3" y="3" rx="2" ry="2"/><circle cx="9" cy="9" r="2"/><path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/></svg>
                      </div>
                      <div v-else-if="getFileType(fileUrl) === 'video'">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-play"><polygon points="6 3 20 12 6 21 6 3"/></svg>
                      </div>
                      <div v-else-if="getFileType(fileUrl) === 'document'">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-archive"><path d="M10 12v-1"/><path d="M10 18v-2"/><path d="M10 7V6"/><path d="M14 2v4a2 2 0 0 0 2 2h4"/><path d="M15.5 22H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v16a2 2 0 0 0 .274 1.01"/><circle cx="10" cy="20" r="2"/></svg>
                      </div>
                      <div v-else-if="getFileType(fileUrl) === 'music'">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-music"><path d="M9 18V5l12-2v13"/><circle cx="6" cy="18" r="3"/><circle cx="18" cy="16" r="3"/></svg>
                      </div>
                      <div v-else>
                        <!-- Default icon for unknown file types -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/><polyline points="14 2 14 8 20 8"/></svg>
                      </div>
                      <div>
                        {{ getFileName(fileUrl) }}
                      </div>
                    </div>
                  </div>
                  <div class="mt-5">
                    <label class="text-sm font-semibold">{{ $t('Description') }}</label>
                    <t-textarea class="mt-1" v-model="description" :value="description"></t-textarea>
                  </div>
              </div>
                <!-- need confirm -->
            </template>
              <template #footer>
                <div class="p-5 border-t border-gray-200">
                    <div class="flex items-center gap-x-2 justify-end">
                        <!-- Button -->
                        <t-button :color="`secondary-solid`" @click="hideDrawer()">
                            Cancel
                        </t-button>
                        <!-- End Button -->
                        <!-- Button -->
                        <t-button :isDisabled="!fileUrl || isUploadingFile" :isLoading="isUploadingFile" :color="`primary-solid`" @click="save()" data-hs-overlay="#drawer-right">
                            Save
                        </t-button>
                        <!-- End Button -->
                    </div>
                </div>
            </template>
        </DrawerRight>
    </div>
    <Detail v-else :files="items" :selectedFile="selectedFile" @closeDetail="closeDetail">
    </Detail>

    <ModalGeneral :isShow="isModalEditOpen" @update:isShow="isModalEditOpen = $event">
      <template #header>
        <p class="text-base font-medium text-gray-800 dark:text-white">
          {{$t('Rename File')}}
        </p>
      </template>
      <template #body>
        <!-- <div class="text-xs text-gray-800 dark:text-neutral-400"> -->
        <div class="">
          <div>
            <div class="mt-2 flex rounded-md shadow-sm ">
              <div class="relative flex flex-grow items-stretch focus-within:z-10  w-full">
                <input type="email" name="email" v-model="selectedFileRename.name" id="email" class="block w-full rounded-none rounded-l-md border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-primary-600 sm:text-sm sm:leading-6" placeholder="John Smith" />
              </div>
              <div class="relative -ml-px inline-flex items-center gap-x-1.5 rounded-r-md px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50">
                .{{ getFileExtension(selectedFileRename?.fileUrl) }}
              </div>
            </div>
          </div>
        </div>
        <!-- </div> -->
      </template>
      <template #footer>
        <t-button :color="`secondary-solid`" @click="isModalEditOpen = false;">
          {{ $t('Cancel') }}
        </t-button>
        <t-button :isDisabled="!selectedFileRename.name" :color="`primary-solid`" @click="confirmEdit()">
          {{ $t('Rename') }}
        </t-button>
      </template>
    </ModalGeneral>

    <ModalGeneral :isShow="isShowModalDelete" @update:isShow="isShowModalDelete = $event">
      <template #header>
        <p class="text-base font-medium text-gray-800 dark:text-white">
          {{$t('Delete File')}}
        </p>
      </template>
      <template #body>
        <!-- <div class="text-xs text-gray-800 dark:text-neutral-400"> -->
        <p class="text-sm text-gray-800 dark:text-neutral-400"> {{$t('Are you sure want to delete this file?')}} </p>
        <!-- </div> -->
      </template>
      <template #footer>
        <t-button class="px-6" :color="`secondary-solid`" @click="isShowModalDelete = false;">
          {{ $t('Cancel') }}
        </t-button>
        <t-button class="px-6" :color="`red-solid`" @click="confirmDelete">
          {{ $t('Delete') }}
        </t-button>
      </template>
    </ModalGeneral>
  </div>
  <div v-if="!isFetching && !items.length" class="font-bold text-center w-[300px] top-0 bottom-0 my-auto mx-auto right-0 left-0 absolute h-10 w-10 z-50">
    {{ $t('No records found') }}
  </div>
</template>

<script>
  import {
    mapGetters,
		mapActions,
  } from 'vuex';
  import {
      InformationCircleIcon,
      PlusIcon
  } from "@heroicons/vue/solid";
  import attachmentApi from "@/api/attachment";
  import fileApi from "@/api/files";
  import Confirmation from "@/components/modal/Confirmation.vue";
  import Detail from "@/pages/files/Detail.vue";
  import Uploader from "@/components/global/Uploader.vue";
  import DrawerRight from "@/components/form/DrawerRight.vue";
  import TTextarea from '@/components/form/Textarea.vue';
  import projectApi from "@/api/project";
  import ModalGeneral from "@/components/modal/ModalGeneral.vue";
  
  import {
      HSStaticMethods
  } from "preline";
  export default {
      name: "files",
      components: {
          PlusIcon,
          Confirmation,
          InformationCircleIcon,
          DrawerRight,
          TTextarea,
          Uploader,
          Detail,
          ModalGeneral
      },
      data() {
          return {
              orderBy: 'createdAt',
              sortBy: 'asc',
              page: 1,
              total: 0,
              maxPage: 1,
              limit: 12,
              name: "",
              description: "",
              selectedIFile: null,
              itemId: null,
              items: [],
              meta: null,
              isEdit: false,
              keyword: "",
              isModalEditOpen: false,
              
              // upload
              fileUrl: null,
              isUploadingFile: false,

              // model
              description: null,
              fileUrl: null,
              oriFileName: null,
              projectId: null,
              selectedFileRename: null,
              selectedFile: null,
              selectedFileContextId: null,
              selectedFileContextUserId: null,
              posContextEdit: null,
              isShowModalDelete: false,
              isFetching: false,

              // url
              projectSlug: null,
              projectId: null,
          };
      },
      computed: {
        ...mapGetters({
          getActiveProject: 'application/getActiveProject',
          user: 'auth/user',
          isAdmin: 'auth/isAdmin',
        }),
        currentId() {
          return this.$route.params.id;
        }
      },
      created() {
          console.log("Asdasdasdas")
          // this.getProject()
          this.setDataUrl()
      },
      watch: {
        currentId() {
          this.setDataUrl()
        },
      },
      methods: {
          ...mapActions({
            setDataProject: 'application/setDataProject',
          }),
          setDataUrl() {
            const encryptedId = this.$route.params.id; // Get the encrypted string from route params
            const decryptedData = this.__decryptProjectData(encryptedId); // Decrypt the data
            
            if (decryptedData) {
              this.projectId = decryptedData.id;   // Access the original project ID
              this.projectSlug = decryptedData.slug; // Access the original project slug
            }
            this.getProject()
          },
          getProject() {
            const callback = (response) => {
              const data = response.data;
              this.setDataProject(data);
              this.getAll();
            };
            const errCallback = (err) => {
              console.log(err);
            };
            console.log("disini ", this.projectId)
            projectApi.get(this.projectId, callback, errCallback);
          },
          nextPage() {
              this.page = this.page + 1;
              this.getAll()
              setTimeout(() => {
                  HSStaticMethods.autoInit();
              }, 500);
          },
          prevPage() {
              this.page = this.page - 1;
              this.getAll()
              setTimeout(() => {
                  HSStaticMethods.autoInit();
              }, 500);
          },
          debounce(func, wait) {
              let timeout;
              return function (...args) {
                  clearTimeout(timeout);
                  timeout = setTimeout(() => {
                      func.apply(this, args);
                  }, wait);
              };
          },
          onInputSearch() {
              this.debounce(this.getAll(this.keyword), 300); // 300ms debounce
          },
          confirmDelete() {
              if (this.selectedFileContextId) {
                  const callback = (response) => {
                      const message = response.message;
                      this.getAll();
                      this.selectedFileContextId = null
                      this.selectedFileContextUserId = null
                      this.__showNotif('success', 'Success', message);
                      this.isShowModalDelete = false
                  }
                  const errCallback = (err) => {
                      console.log(err)
                      this.isShowModalDelete = false
                  }
                  const id = this.selectedFileContextId;
                  attachmentApi.delete(id, callback, errCallback)
              }
          },
          save() {
            this.createAttachment()
          },
          
          getAll(keyword = null) {
            this.isFetching = true;
              const callback = (response) => {
                  const data = response.data;

                  const meta = response.meta;
                  this.items = data;
                  this.meta = meta;
                  this.page = meta.currentPage;
                  this.maxPage = meta.lastPage;
                  this.total = meta.total;
                  this.isFetching = false;
              }
              const errCallback = (err) => {
                  const message = err?.response?.data?.message;
                  this.__showNotif('error', 'Error', message);
                  this.isFetching = false;
              }

              const params = {
                  orderBy: this.orderBy,
                  sortBy: this.sortBy,
                  page: this.page,
                  limit: this.limit,
                  projectId: this.getActiveProject?.id,
              }
              if (keyword) params.keyword = keyword;
              attachmentApi.getList(params, callback, errCallback)
          },
          getFileName(url) {
            // Split the URL by '/' and return the last part
            return url.split('/').pop();
          },
          getFileType(url) {
            const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.tiff'];
            const videoExtensions = ['.mp4', '.mov', '.wmv', '.avi', '.mkv', '.flv', '.webm', '.ogg', '.m4v'];
            const documentExtensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt', '.csv'];
            const musicExtensions = ['.mp3'];
            const urlLower = url.toLowerCase();

            if (imageExtensions.some(extension => urlLower.endsWith(extension))) {
              return 'image';
            } else if (videoExtensions.some(extension => urlLower.endsWith(extension))) {
              return 'video';
            } else if (documentExtensions.some(extension => urlLower.endsWith(extension))) {
              return 'document';
            } else if (musicExtensions.some(extension => urlLower.endsWith(extension))) {
              return 'music';
            } else {
              return 'unknown';
            }
          },
          addFile() {
            this.$refs.drawerRight.visibleRight = true;
          },
          hideDrawer() {
            this.$refs.drawerRight.visibleRight = false;
          },
          upload(e) {
            this.isUploadingFile = true;
            const files = e;
            if (files.length > 0) {
              const fileType = files[0].type;
              this.isUploadingFile = true;
              const file = files[0];
              if (!file) {
                this.__showNotif('warning', 'Upload File', this.$t('Sorry, currently we can\'t upload the file'));
                return;
              }
              const params = new FormData();
              params.append('file', file);
              const callback = (response) => {
                const File = response.data;
                this.fileUrl = File;
                this.oriFileName = file.name;
                this.isUploadingFile = false;
              };
              const errorCallback = () => {
                this.isUploadingFile = false;
                this.__showNotif('warning', 'Upload File', this.$t('Sorry, currently we can\'t upload the file'));
                this.isUploadingFile = false;
              };
              fileApi.upload(params, callback, errorCallback);
            } else {
              this.__showNotif('warning', 'Upload File', this.$t('Unsupported File'));
              return;
            }
          },

          createAttachment() {
            this.isUploadingFile = true;
            const callback = (response) => {
              const data = response.data;
              const message = response.message;
              this.__showNotif('success', 'Success', message);
              // Trigger the drawer open using data-hs-overlay
              this.isUploadingFile = false;
              this.fileUrl = null;
              this.description = null;
              this.oriFileName = null;
              this.getAll();
              this.hideDrawer()
            }
            const errCallback = (err) => {
              const message = err.response.data.message;
              this.__showNotif('error', 'Error', message || 'Error on creating file');
              this.handleErrors(err.response.data)
              this.isUploadingFile = false;
            }
            let params = {
              fileUrl: this.fileUrl,
              projectId: this.getActiveProject?.id,
              description: this.description,
              name: this.oriFileName,
            };
            if (this.fileUrl) attachmentApi.create(params, callback, errCallback)
          },

          closeDetail() {
            this.selectedFile = null
          },

          openContextAsset(file, event) {
            this.selectedFileContextId = file.id;
            this.selectedFileContextUserId = file.user.id;
            const posX = event.x;
            const posY = event.y;
            const fileId = this.selectedFileContextId || null;
            const element = document.getElementById(`file-${fileId}`);
            element.click();
            this.posContextEdit = `top: ${posY - 50}px; left: ${posX - 250}px; position: absolute;`;
          },

          deleteFile() {
            this.isShowModalDelete = true;
          },

          copyURL(file) {
            navigator.clipboard.writeText(file.fileUrl)
              .then(() => {
                // Optional: Notify the user that the URL has been copied
                this.__showNotif('success', 'success', 'URL Copied to clipboard');
              })
              .catch(err => {
                console.error("Failed to copy the URL: ", err);
              });
          },

          renameFile(item) {
            const nameWithoutExt = item?.name.substring(0, item.name.lastIndexOf('.'));
            this.selectedFileRename = this.__duplicateVar(item)
            this.selectedFileRename.name = nameWithoutExt
            this.isModalEditOpen = true;
          },

          getFileExtension(url) {
            if (!url) return ''; // Return empty if no URL

            // Split the URL by '.' and get the last element
            const parts = url.split('.');
            const extension = parts.length > 1 ? parts.pop() : ''; // Get extension and convert to uppercase

            // Append additional text based on the file type
            if (extension === 'jpg' || extension === 'jpeg' || extension === 'png' || extension === 'gif') {
              return `${extension}`; // For image files
            } else if (extension) {
              return `${extension}`; // For other file types
            }
            
            return ''; // Return empty if no extension found
          },

          confirmEdit() {
            this.isSaving = true;
            const callback = (response) => {
              const data = response.data;
              const message = response.message;
              this.getAll();
              this.__showNotif('success', 'Success', message);
              this.isSaving = false;
              this.isModalEditOpen = false;
              this.selectedFileRename = null
            }
            const errCallback = (err) => {
              const message = err.response.data.message;
              this.__showNotif('error', 'Error', message || 'Error User');
              this.handleErrors(err.response.data)
              this.isSaving = false;
              this.isModalEditOpen = false;
              this.selectedFileRename = null
            }
            const extension = this.getFileExtension(this.selectedFileRename?.fileUrl);
            const params = {
              name: `${this.selectedFileRename.name}.${extension}`,
            }
            if (this.selectedFileRename?.id) attachmentApi.update(this.selectedFileRename.id, params, callback, errCallback)
          },
      },
  };
</script>

<style scoped>
  /* Add any scoped styles here if necessary */
</style>
