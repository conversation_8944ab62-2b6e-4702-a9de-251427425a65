import { client } from '@/libraries/http-client';

const endpoint = '/v1/subscriptions';

export default {
	cancel(params, cb, errorCb) {
		const url = `${endpoint}/cancel`;
		client.post(url, params)
			.then((response) => {
				if (cb) cb(response.data);
			})
			.catch((e) => {
				if (errorCb) errorCb(e);
			});
	},
	updateCredit(params, cb, errorCb) {
		const url = `${endpoint}/credit`;
		client.put(url, params)
			.then((response) => {
				if (cb) cb(response.data);
			})
			.catch((e) => {
				if (errorCb) errorCb(e);
			});
	},
};
